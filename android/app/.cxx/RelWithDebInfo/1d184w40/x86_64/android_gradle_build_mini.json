{"buildFiles": ["C:\\Users\\<USER>\\shorebird-main\\bin\\cache\\flutter\\a5bd04b23bd448f5c825a20ddb391110ea15d3f1\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\RelWithDebInfo\\1d184w40\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\RelWithDebInfo\\1d184w40\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}