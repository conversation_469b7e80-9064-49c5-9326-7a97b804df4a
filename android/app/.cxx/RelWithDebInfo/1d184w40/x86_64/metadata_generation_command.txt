                        -HC:\Users\<USER>\shorebird-main\bin\cache\flutter\a5bd04b23bd448f5c825a20ddb391110ea15d3f1\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.3.11579264\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\projects\job\task app\rasiin_task_management_app_front_end\build\app\intermediates\cxx\RelWithDebInfo\1d184w40\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\projects\job\task app\rasiin_task_management_app_front_end\build\app\intermediates\cxx\RelWithDebInfo\1d184w40\obj\x86_64
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BD:\projects\job\task app\rasiin_task_management_app_front_end\android\app\.cxx\RelWithDebInfo\1d184w40\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2