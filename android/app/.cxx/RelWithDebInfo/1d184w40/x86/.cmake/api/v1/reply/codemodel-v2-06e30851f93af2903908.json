{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/projects/job/task app/rasiin_task_management_app_front_end/android/app/.cxx/RelWithDebInfo/1d184w40/x86", "source": "C:/Users/<USER>/shorebird-main/bin/cache/flutter/a5bd04b23bd448f5c825a20ddb391110ea15d3f1/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}