{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\RelWithDebInfo\\22572359\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\RelWithDebInfo\\22572359\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}