{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\stable\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\Debug\\3g5x6lk2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\projects\\job\\task app\\rasiin_task_management_app_front_end\\android\\app\\.cxx\\Debug\\3g5x6lk2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}