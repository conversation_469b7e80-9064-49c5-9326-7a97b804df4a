{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/projects/job/task app/rasiin_task_management_app_front_end/android/app/.cxx/Debug/3g5x6lk2/armeabi-v7a", "source": "C:/Users/<USER>/fvm/versions/stable/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}