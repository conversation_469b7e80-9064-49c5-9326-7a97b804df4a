plugins {
    id "com.android.application"
    id 'com.google.gms.google-services' // Firebase configuration
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
    namespace = "com.rasiin.hodan_tasks_app"
    // ndkVersion = flutter.ndkVersion
    ndkVersion "25.1.8937393"
    compileSdkVersion 35 // Ensure you are targeting the latest SDK

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
        coreLibraryDesugaringEnabled true
    }

    kotlinOptions {
        jvmTarget = "1.8" // Ensure it matches Java 17
    }

    defaultConfig {
        applicationId "com.rasiin.hodan_tasks_app"
        minSdkVersion 21  // Ensure minimum SDK version is set correctly
        targetSdkVersion 35 // Updated Target SDK version
        multiDexEnabled true
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
        }
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            minifyEnabled true  // Enables code shrinking, recommended for production
            shrinkResources true  // Removes unused resources, reducing app size
        }
    }

    // flavorDimensions "default"
    // productFlavors {
    //     dev {
    //         dimension "default"
    //         // applicationIdSuffix ".dev"
    //         resValue "string", "app_name", "RASIIN_TASKS Dev"
    //     }
    //     staging {
    //         dimension "default"
    //         // applicationIdSuffix ".staging"
    //         resValue "string", "app_name", "RASIIN_TASKS Staging"
    //     }
    //     prod {
    //         dimension "default"
    //         resValue "string", "app_name", "RASIIN"
    //     }
    // }
}

flutter {
    source = "../.."
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "com.android.support:multidex:1.0.3"

    // Desugaring for Java 8+ APIs
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.4'

    // Firebase SDK and BOM
    implementation platform("com.google.firebase:firebase-bom:33.4.0")
    implementation "com.google.firebase:firebase-analytics-ktx"
}
