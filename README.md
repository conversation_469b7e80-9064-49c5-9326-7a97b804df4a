# 🚀 Rasiin Tasks App

<div align="center">

![Flutter](https://img.shields.io/badge/Flutter-02569B?style=for-the-badge&logo=flutter&logoColor=white)
![Dart](https://img.shields.io/badge/Dart-0175C2?style=for-the-badge&logo=dart&logoColor=white)
![Firebase](https://img.shields.io/badge/Firebase-039BE5?style=for-the-badge&logo=Firebase&logoColor=white)
![BLoC](https://img.shields.io/badge/BLoC-0175C2?style=for-the-badge&logo=flutter&logoColor=white)

**A powerful task management and social collaboration platform built with Flutter**

[Features](#-features) • [Installation](#-installation) • [Architecture](#-architecture) • [Contributing](#-contributing)

</div>

---

## 📱 Overview

Rasiin Tasks App is a comprehensive task management application that combines productivity features with social collaboration. Built with Flutter and powered by modern state management patterns, it offers a seamless experience across all platforms.

### 🎯 Key Highlights

- **📋 Task Management**: Create, organize, and track tasks with advanced filtering
- **💰 Payroll Integration**: View and download payroll documents with PDF support
- **📱 Social Posts**: Share updates, media, and polls with your team
- **🔄 Real-time Updates**: Live synchronization across all devices
- **📊 Analytics**: Visual insights with interactive charts
- **🎨 Modern UI**: Beautiful, responsive design with dark/light themes

---

## ✨ Features

### 🏢 Core Functionality
- **Task Management System**
  - Create, edit, and delete tasks
  - Priority levels and due dates
  - Category-based organization
  - Progress tracking

- **Payroll Management**
  - Monthly payroll viewing
  - PDF document generation
  - Year/month filtering
  - Download and share capabilities

### 📱 Social Features
- **Post Creation & Sharing**
  - Text posts with rich content
  - Image and video uploads
  - Poll creation with voting
  - Like and comment system

- **Media Management**
  - Multiple image selection
  - Video recording and upload
  - Image compression and optimization
  - Media preview and editing

### 🔧 Technical Features
- **Offline Support**: Local data caching with ObjectBox
- **Push Notifications**: Firebase Cloud Messaging integration
- **State Management**: BLoC pattern for predictable state handling
- **Pagination**: Efficient data loading with infinite scroll
- **Error Handling**: Comprehensive error management
- **Security**: Secure storage for sensitive data

---

## 🛠 Tech Stack

### Frontend
- **Flutter** `3.24.4+` - Cross-platform UI framework
- **Dart** `3.4.4+` - Programming language
- **BLoC** `9.1.1` - State management pattern
- **ObjectBox** `4.1.0` - Local database
- **Dio** `5.6.0` - HTTP client

### Backend Integration
- **Firebase Core** `3.15.1` - Backend services
- **Firebase Messaging** `15.2.9` - Push notifications
- **Frappe Framework** - Custom API backend

### UI & Design
- **Flutter ScreenUtil** `5.9.3` - Responsive design
- **Google Fonts** `6.2.1` - Typography
- **Lottie** `3.1.3` - Animations
- **Cached Network Image** `3.4.1` - Image caching

### Development Tools
- **Flutter Launcher Icons** `0.14.4` - App icon generation
- **Flutter Native Splash** `2.2.18` - Splash screen
- **Build Runner** `2.1.7` - Code generation
- **Shorebird** - Code push updates

---

## 🚀 Installation

### Prerequisites
- Flutter SDK `>=3.4.4 <4.0.0`
- Dart SDK `>=3.4.4`
- Android Studio / VS Code
- Git

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/rasiin_tasks_app.git
   cd rasiin_tasks_app
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Environment Configuration**
   ```bash
   # Copy environment files
   cp .env.example .env.development
   cp .env.example .env.production

   # Configure your API endpoints and keys
   ```

4. **Generate code**
   ```bash
   flutter packages pub run build_runner build
   ```

5. **Run the app**
   ```bash
   # Development
   flutter run --flavor development

   # Production
   flutter run --flavor production --release
   ```

### 🔧 Build Configuration

```bash
# Generate app icons
flutter pub run flutter_launcher_icons

# Generate splash screens
flutter pub run flutter_native_splash:create

# Build APK
flutter build apk --release

# Build iOS
flutter build ios --release
```

---

## 🏗 Architecture

### 📁 Project Structure
```
lib/
├── app/                    # App-level configuration
│   ├── theme/             # Theme and styling
│   └── routes/            # Navigation routes
├── core/                  # Core functionality
│   ├── bloc/              # State management
│   ├── models/            # Data models
│   ├── enums/             # Enumerations
│   ├── utils/             # Utilities and helpers
│   └── constants/         # App constants
├── features/              # Feature modules
│   ├── auth/              # Authentication
│   ├── home/              # Home dashboard
│   ├── posts/             # Social posts
│   ├── tasks/             # Task management
│   └── common/            # Shared widgets
└── main.dart              # App entry point
```

### 🔄 State Management Pattern

The app uses **BLoC (Business Logic Component)** pattern for state management:

```dart
// Event-driven architecture
PostBloc -> PostEvent -> PostState -> UI Update

// Example usage
context.postBloc.add(PostGetAllEvent(
  userEmail: userEmail,
  limit: 20,
  offset: 0,
));
```

### 📊 Data Flow

```mermaid
graph TD
    A[UI Layer] --> B[BLoC Layer]
    B --> C[Repository Layer]
    C --> D[Data Sources]
    D --> E[Local DB / API]
    E --> D
    D --> C
    C --> B
    B --> A
```

---

## 📱 Screenshots

<div align="center">
<table>
  <tr>
    <td><img src="screenshots/home.png" width="200"/></td>
    <td><img src="screenshots/posts.png" width="200"/></td>
    <td><img src="screenshots/payroll.png" width="200"/></td>
    <td><img src="screenshots/tasks.png" width="200"/></td>
  </tr>
  <tr>
    <td align="center">Home Dashboard</td>
    <td align="center">Social Posts</td>
    <td align="center">Payroll</td>
    <td align="center">Task Management</td>
  </tr>
</table>
</div>

---

## 🔧 Configuration

### Environment Variables
```env
# API Configuration
API_BASE_URL=https://your-api-url.com
API_VERSION=v1

# Firebase Configuration
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_API_KEY=your-api-key

# App Configuration
APP_NAME=Rasiin Tasks
APP_VERSION=1.0.1
```

### Build Flavors
- **Development**: Debug builds with development API
- **Staging**: Testing builds with staging API
- **Production**: Release builds with production API

---

## 🧪 Testing

```bash
# Run unit tests
flutter test

# Run integration tests
flutter test integration_test/

# Generate coverage report
flutter test --coverage
genhtml coverage/lcov.info -o coverage/html
```

---

## 📦 Deployment

### Android
```bash
# Build signed APK
flutter build apk --release --split-per-abi

# Build App Bundle
flutter build appbundle --release
```

### iOS
```bash
# Build for iOS
flutter build ios --release

# Archive for App Store
flutter build ipa --release
```

### Code Push (Shorebird)
```bash
# Deploy patch
shorebird patch android
shorebird patch ios
```

---

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**
   ```bash
   git commit -m 'Add amazing feature'
   ```
4. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

### 📋 Development Guidelines
- Follow [Flutter style guide](https://dart.dev/guides/language/effective-dart/style)
- Write unit tests for new features
- Update documentation for API changes
- Use conventional commit messages

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 👥 Team

<div align="center">

**Built with ❤️ by the Rasiin Development Team**

[Report Bug](https://github.com/your-username/rasiin_tasks_app/issues) • [Request Feature](https://github.com/your-username/rasiin_tasks_app/issues) • [Documentation](https://docs.rasiin.com)

</div>

---

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/rasiin)
- 📖 Documentation: [docs.rasiin.com](https://docs.rasiin.com)
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/rasiin_tasks_app/issues)

---

<div align="center">

**⭐ Star this repository if you found it helpful!**

</div>
