{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98284fb9041a067851af8f79732c303f23", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d750bce8e8e3c4a60f355066b2da01e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983d750bce8e8e3c4a60f355066b2da01e", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9896d5f5d512a27a3ee78951ef3fc5e951", "guid": "bfdfe7dc352907fc980b868725387e982ef32830090a5f180689e16980928fd2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfce676737cf8c5c5feab1d9cbec11e0", "guid": "bfdfe7dc352907fc980b868725387e985a916ced4ab8bf3396d2522efbf655a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdee575a4c129126ec6171ecb3aaf2a4", "guid": "bfdfe7dc352907fc980b868725387e98ff53f399fa1e2ecb7849daf4286e9ade", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98726e23765fc3783c7b6adcd821781e06", "guid": "bfdfe7dc352907fc980b868725387e988ad8e0322ec48311f0d9e5a75f54c724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98764beca90483f23bd3e747381a413232", "guid": "bfdfe7dc352907fc980b868725387e98b11ed937bba18c7408a49a9a028bb33f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817168ead3b62b12407d811483bbc3c83", "guid": "bfdfe7dc352907fc980b868725387e983d80b84cb41fbc5fdcc8bc3d451f292d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a6efc62dfb8cf00d344cd1fa1267382", "guid": "bfdfe7dc352907fc980b868725387e983bf5db94459714fd2562bfa5ba3794e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987342f30d6dd6b72a8413cb609b029b69", "guid": "bfdfe7dc352907fc980b868725387e98b4cf2c1398499b177aa5f12416a2cefc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f8a69d094757fdbd4b2cecb4d13199e", "guid": "bfdfe7dc352907fc980b868725387e98a5931e111640051bba64bb234ef28a63", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f27c2528365d45929f393fcfa737dfb8", "guid": "bfdfe7dc352907fc980b868725387e984e2d3cd6dd0a8b8ce5a9345bf5eed7de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804e7c8b7377572a32929da02521bbcc2", "guid": "bfdfe7dc352907fc980b868725387e984bcefc59b1341fdd0e8bad71dba39c92", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98185d1ec5ca116b2df6c846f6e00fbcbf", "guid": "bfdfe7dc352907fc980b868725387e98c7eb3ea66f1fa44a1f5858283c92137d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5cd95d6daf17be44499259394f0420d", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddbb8c4172c5c9f9898974c9444afa8e", "guid": "bfdfe7dc352907fc980b868725387e98cdc5844f930b9d0fd5308dd5856a500e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805c810b175d7247c7350e76b908682b0", "guid": "bfdfe7dc352907fc980b868725387e988f5bb97be79cc2d8abff7c92bd4b7567", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b94ed1418a06a03c68d044dee00dfdac", "guid": "bfdfe7dc352907fc980b868725387e983de4f1e8f5a97a6028a6bb34227f49d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0fe38d86e92f9185a38c4cdd64b7e9e", "guid": "bfdfe7dc352907fc980b868725387e98f2f63511bf606bb48ce3eb1b71b306f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98492070eacce38e7999dcab743b114a5a", "guid": "bfdfe7dc352907fc980b868725387e98327b38ee62f98da7d09431ea5004a053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810ec0c5962bbf1e157d8c417ca928d7a", "guid": "bfdfe7dc352907fc980b868725387e980188baa19b7914aff2e34c8da54773e1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec36a49cb31dff4b2245c8311a0ff4ea", "guid": "bfdfe7dc352907fc980b868725387e9812d25da0977d54b4f93c43814429ebf2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e6a0b8f4c3be422bb4137ba4d6a2915", "guid": "bfdfe7dc352907fc980b868725387e98b9ff284581573c4ba830aef19b6cb271", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cca0285b5486ef912a2ed696277e2ab", "guid": "bfdfe7dc352907fc980b868725387e98938189fe00ada6ed049fb839ef6bf7d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98190f96aab816bf5350a8c689833d058e", "guid": "bfdfe7dc352907fc980b868725387e9864333a3fadcb2636b3f7fea467c141f6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9865e9f1c236375b8328d3ccc20cb69b2d", "guid": "bfdfe7dc352907fc980b868725387e982a61503d8185fa65d0d61935791efb04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a4108a48f5678aa2cb2a5e2c562cfc5", "guid": "bfdfe7dc352907fc980b868725387e9835220589345efb92af42631dbf6b9459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c23d4865842615f0a96c5239a42de24", "guid": "bfdfe7dc352907fc980b868725387e98c4ed4b65682f6200b53ab06f33e7c896"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983760831bdca8602159cf3f8cbe67cb90", "guid": "bfdfe7dc352907fc980b868725387e98f67d51cdaaf5e1f78be421f374efde62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef2f3f2defe185a1d9e702cd7e9faaac", "guid": "bfdfe7dc352907fc980b868725387e98aa97ddbe032d174d7e0e5ddbc64b9d5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984503850aa3f53f18e6974191d041f688", "guid": "bfdfe7dc352907fc980b868725387e98ff8928328841298b0ed62804c598770b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ee2e1c6aa47eb330f07d882f9b2aba", "guid": "bfdfe7dc352907fc980b868725387e98ef84c0ffd43efae0c864668786839def"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e665a3006ab7a401d5edab762a5b455", "guid": "bfdfe7dc352907fc980b868725387e9848cadd1ba7bb24a85b533aabf8e22799"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c177a5a84b729c26d328a0f7fa6935f", "guid": "bfdfe7dc352907fc980b868725387e9821497750e138db752421f5224d128654"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b83efa276c491352eb2b4808005dcd40", "guid": "bfdfe7dc352907fc980b868725387e984a4a9db5329a7711e351ab57794c53a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980634a3b1ed9cbc18eb411a58cc695c01", "guid": "bfdfe7dc352907fc980b868725387e9833885db617a4d69d1571730987fbd3fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d49c2e21df85ebe366dcefa18d29d10", "guid": "bfdfe7dc352907fc980b868725387e9824cbbfe0ddc5f68e6717f61ca670184f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae3d6e21a98aade2294c3ead067d110", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867e47df24cc454c03b2d57b03705e2b4", "guid": "bfdfe7dc352907fc980b868725387e984e5371b8cd4eca1a3ea6dfcf7d307c37"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b23bf58b785972315793e192f100de4", "guid": "bfdfe7dc352907fc980b868725387e98d3572e24141325a30bc3c0a31c41fa85"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a78e0e8d0f2d0d33b14acab964bd27a0", "guid": "bfdfe7dc352907fc980b868725387e98b2f53712d1d0af9bc7d13919388a2c6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840124d8ec10da1e0fd16ed371ef6d2ee", "guid": "bfdfe7dc352907fc980b868725387e98e29bf2527a03f8a7f0ee4f8fb682157b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fb1bec9cc43485f3f35f5440b1013f3", "guid": "bfdfe7dc352907fc980b868725387e9827d1f3d71d300da27dcd08b0f8c4d523"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecdc0ee64d94099499f1a37a5906cd6f", "guid": "bfdfe7dc352907fc980b868725387e98eee17b6918192a58e3242e8202b1af3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ef692cb3e3a69ae49542cb8e462359", "guid": "bfdfe7dc352907fc980b868725387e98319f0ee3ee4a0d94236a327f7214191b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98237e33a299bc80fc6dbffc84c1b46b38", "guid": "bfdfe7dc352907fc980b868725387e9889d6808f96db31c0fa88901646d50905"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}