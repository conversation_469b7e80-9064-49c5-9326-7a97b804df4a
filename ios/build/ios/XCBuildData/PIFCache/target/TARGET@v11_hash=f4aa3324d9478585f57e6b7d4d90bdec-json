{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cdfce79768518578a68dab21c71f62f2", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e2a8da72eeff92c07f1e57298eae7ff4", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c32e92ddd49bd20a7c20a837439f9482", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982042b0538584883f9fca2b91b680069d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c32e92ddd49bd20a7c20a837439f9482", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982c3fb0f9d8991ba0c5123b134f242f2c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c20ea4b47a5eafca8c6981c37e845d32", "guid": "bfdfe7dc352907fc980b868725387e9845307f39053fcd053458d9094240df33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889ff714f7d040babcea92749bf2e2cd4", "guid": "bfdfe7dc352907fc980b868725387e981b2ea684659188987977dc92cd993807", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835d84542e88a8c4d3e94a2c05effb2ce", "guid": "bfdfe7dc352907fc980b868725387e98cbbde9d98ffea3220d0925e90d9810b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982329a8138683f6ff87c772be335d2734", "guid": "bfdfe7dc352907fc980b868725387e98de1598cdeadf6f3fdb530b73a900f108", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ddf8fec57cc4ea6249dc2a691e014a8", "guid": "bfdfe7dc352907fc980b868725387e98e6b7a8d4d50d2ca92d9827b15a9aec16", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895d318f973201d4b4ebcb7e282395251", "guid": "bfdfe7dc352907fc980b868725387e9854f7f876dba0b2a35e11c65991590457", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1840242ae3d0617151de13e653e0f64", "guid": "bfdfe7dc352907fc980b868725387e98034382bc587eefd058c617eddb32bcd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c2004f87ee6b7a8294078bd05138c2", "guid": "bfdfe7dc352907fc980b868725387e9828c9a95fe91e0b0f066f2f0a711ddec0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894e6825a5f4cd09a168c74729094bcd2", "guid": "bfdfe7dc352907fc980b868725387e986c2e2ea6ae9bc9a499dd2abc6e7c8ac6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5462afb270e5b99642c17ec38eeae35", "guid": "bfdfe7dc352907fc980b868725387e98725334bd521161d8d6702e7be29ba4b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef26a2d2add6073bc00bdbd8d7962eca", "guid": "bfdfe7dc352907fc980b868725387e982f41aebb475d08195dd44b11f2c209eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1b17484141cf37518435f906e077814", "guid": "bfdfe7dc352907fc980b868725387e983683aeb705e45847154c5320c0cc3007", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982205753710a739e661396d37c7372316", "guid": "bfdfe7dc352907fc980b868725387e986804036ea5b9bf5ab4feffee89ba9569", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891c89eb6634149fc4b4c893a790d6382", "guid": "bfdfe7dc352907fc980b868725387e98ff111d3b42e1026054604cf9151465f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e08f9456c6fe37c8effa15fedbf91fc6", "guid": "bfdfe7dc352907fc980b868725387e9805c2fef05c5796b83d3f1ba0d67bd5ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eff090a8b59f45410ca149e5d1a1c5b6", "guid": "bfdfe7dc352907fc980b868725387e98a482ff649a5274edc19e58833ceb6e70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcb11fc8ef919e4fe5c42271f095f386", "guid": "bfdfe7dc352907fc980b868725387e984543e0e98330e8844e1d469052dd1702", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98923262f5a86b03ef17ff9c1f595e763a", "guid": "bfdfe7dc352907fc980b868725387e98231fc59678512d3db1a992ea947c4e55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e43948bc9a7cb250c997997b5237df8", "guid": "bfdfe7dc352907fc980b868725387e983192ffa273f4134ee1edfdf1fe40766c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fc6c907427a9036b6b2015347c23e9e", "guid": "bfdfe7dc352907fc980b868725387e980e3954a88b3c3d8b9a709c6c4f749c20", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c713a2a488947502b351f933b7781d0f", "guid": "bfdfe7dc352907fc980b868725387e9877998473e30b70f8ed26673a5a891048", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d1b2d73fcc24676fdc034588dd799a", "guid": "bfdfe7dc352907fc980b868725387e98d4020493cf6d9a47d7c30408347cf4a8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a985a3235047064b306d205a80a5198", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fcafb159996d0b4b3dcb1767523b115c", "guid": "bfdfe7dc352907fc980b868725387e98c32c4ddf1a958dd8686d3b9dd6b8ff7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987684e95e2c804ab6b207c3501904d3e8", "guid": "bfdfe7dc352907fc980b868725387e98792a393ab125ea5051af2597f5cab244"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca8e77424248ff65f0d0d981338ff2e9", "guid": "bfdfe7dc352907fc980b868725387e987dc3a87ecb4edf79c9fcc7e9edd3bf07"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af119565e3adbc8ecdfa7b9b204910c4", "guid": "bfdfe7dc352907fc980b868725387e98c4fdc7a00a623fdb90e314a78a7d7f8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98945eada2e31ac407adf4e40530c1d67e", "guid": "bfdfe7dc352907fc980b868725387e980087b829bc6b9d0f15ef1ff648af43b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c469255e5af3b2b831e3d3e762c3c690", "guid": "bfdfe7dc352907fc980b868725387e98e44a0078ae99d25e59682753d45e0e56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751a3b1c78f27e4f1e859d6f0d5de276", "guid": "bfdfe7dc352907fc980b868725387e982a8837aea5d269013d857d37f3b7aba0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833408a779ba35eea777404ab1b1f8190", "guid": "bfdfe7dc352907fc980b868725387e98b8bd15c31193aebbdfc1ef449e64ffd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841f46caf721a6d5cdc5a4089ae9b5e16", "guid": "bfdfe7dc352907fc980b868725387e98f658db112a6cca5dd204f65c31e38a14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835b31b16e4af13546aed1c822ed4b5f5", "guid": "bfdfe7dc352907fc980b868725387e9844b98a0c7e6feeeb7535af1295446614"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c379c93046d00ed0a67be7a8c5514429", "guid": "bfdfe7dc352907fc980b868725387e98fd893ff9d4cf882aa7d76607660389d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917194a4cb3bf4e4daabbb6ba0536510", "guid": "bfdfe7dc352907fc980b868725387e98153c7a4a1553de5a19d141cedade597d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98560940208d13ce13dcb808b9b64ac712", "guid": "bfdfe7dc352907fc980b868725387e989544c43139ed6ad18505c585339679d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ee16d14beba3fc3bcb4ec30e805fbc0", "guid": "bfdfe7dc352907fc980b868725387e9877c63a320b382bf580151a4c3d404638"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852482256f5b187f18e607b585d7662b6", "guid": "bfdfe7dc352907fc980b868725387e98034366ec3b5401628cc344ca317a8778"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ef209aaa264fe54bb5b3d4c55b1507c", "guid": "bfdfe7dc352907fc980b868725387e98b188dda493bd3e2804b7acb70009a8b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834377beba2f32dd59fdeea5f269d6376", "guid": "bfdfe7dc352907fc980b868725387e9824b65cb763d5e16336253d99e4b43591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f644d2b2baf561d3a7761d54efc8217c", "guid": "bfdfe7dc352907fc980b868725387e98dd0c2579c59fbd014f14173a7c06bb72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986570a29487b078d1b0410e64826d0427", "guid": "bfdfe7dc352907fc980b868725387e98aa56df226eb995c81e412401016e1575"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980660a8f9d1bc3f62baf1f5bacd6a5f97", "guid": "bfdfe7dc352907fc980b868725387e980b1cee5b03a698c395b0966498a15c52"}], "guid": "bfdfe7dc352907fc980b868725387e984fdc5afa9386460b6a5337013db3211a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e987bd91fb52b82c93c71db2b5c13870bcd"}], "guid": "bfdfe7dc352907fc980b868725387e9858e5ddbfd1a636871801d96c8405b266", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9854bd3eb36f7522d309a87469be5630e1", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98774a96f493165d556dfe049cd7137559", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}