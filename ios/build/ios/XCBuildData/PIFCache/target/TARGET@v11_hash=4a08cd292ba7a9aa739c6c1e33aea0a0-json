{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988ec52048f8de9a2370f86658b628b7ec", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f006eb89c2266b4b06a3643624aaed9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c791b3ce52953b5647ac8bc771d6e84d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98394437e83645c0d5e33c11e53a8dd095", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c791b3ce52953b5647ac8bc771d6e84d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982a3189ef5e2318522219531f59c0e06c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d288e2d16d898b076e8e482112a509ac", "guid": "bfdfe7dc352907fc980b868725387e988424421178e860b69cc313ceea30a84d", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9863f8fe87a5a74f77f68cbe95abc29e8a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983df0b2f0edd4300b832b7c56b2cb1e0b", "guid": "bfdfe7dc352907fc980b868725387e983c10e47bd4a73b00f1cd6460c633b3a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828fed6f76721d60063ecd913792fa170", "guid": "bfdfe7dc352907fc980b868725387e981720c7e5dbab1443345d5b4d1f163192"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd81a7f84671ccd51a8aa8e8a46cc717", "guid": "bfdfe7dc352907fc980b868725387e98f2a357b6a5f4a6c1d835c6ab6211b083"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bef0a4a9ebb498a9f031aaf0afb32df", "guid": "bfdfe7dc352907fc980b868725387e9894e1820503cc0fbb2ab761302460c671"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfddd9907adc6e9776fa3d39a51b4a51", "guid": "bfdfe7dc352907fc980b868725387e98e9d915583cebed8e523cfdbc5a13d30f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888e734b164c9575e2cccacc2ddcf010b", "guid": "bfdfe7dc352907fc980b868725387e98f4e7c91454a205f81489a4bb59a3e99b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e92c8a0ac6f1af68fa4a2dd3b95fccba", "guid": "bfdfe7dc352907fc980b868725387e98ccdeaa635afc42f571a3af6351b992d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bef8d916ed8d65157395007f26200dc", "guid": "bfdfe7dc352907fc980b868725387e9861b2059559a3eda011ffdc6ca07b9553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843e417b2b2f84842de231118b51db858", "guid": "bfdfe7dc352907fc980b868725387e98e6456f7c4b29be80f96a04a3b8034acf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4724982dd244c8fd5008b90a64e98c9", "guid": "bfdfe7dc352907fc980b868725387e9806f30b2f1960c872802cf1fefe60fc87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08ad8373952b8ad12d36fbfa5dc9050", "guid": "bfdfe7dc352907fc980b868725387e984ef8fec16c607d086ca55a04d87ce296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99402682dc641f4c1b15e3035db6880", "guid": "bfdfe7dc352907fc980b868725387e98d0b8d1395ec17685545c386040521a88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd2f0829de518478f861fa134d363c4e", "guid": "bfdfe7dc352907fc980b868725387e98fc9cd350faf1b19fd251c43bd3fd84c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc1da0b46133e2813819523532fc7efb", "guid": "bfdfe7dc352907fc980b868725387e980fde871581e5544c6c46dcbc6ab58e5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b5095c06f63a4036ae8308d903679c", "guid": "bfdfe7dc352907fc980b868725387e98170775c842304e22626e36f8ae2dd11d"}], "guid": "bfdfe7dc352907fc980b868725387e98fd4fd597d1c5d8b9f68813ac50bed7cf", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980984734552459b757f995e1a298c38e8"}], "guid": "bfdfe7dc352907fc980b868725387e989a0a30184f15b03047497bf54a70be2f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98dee4c4366e88b6fac9a3c0635725bcb2", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e986e8584e7ee8208856180bf26e62a3a33", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}