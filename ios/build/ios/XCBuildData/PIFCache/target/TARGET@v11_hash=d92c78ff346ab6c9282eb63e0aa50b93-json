{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a3f343a19aa0e1f6658315798a0c41c4", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fb312ceedb88382c8ff26fa975db4492", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985bcaf44a0a72d9a0098589aade4669db", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98fcac8d32695ea64b649946e63d745be0", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985bcaf44a0a72d9a0098589aade4669db", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e982a1c3194ddd620a9f0415177ed51efed", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98f3c9cc3d5610ea77c4b182de37dc3887", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9856c5578beb12ec3b132e3784b2aa30d1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9864517d8a40c860085996d72421f1e3bf", "guid": "bfdfe7dc352907fc980b868725387e985d5f50fce3a9dab7b7e1a77208576859"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e99262c87d90f3dfb9d52326672f03e", "guid": "bfdfe7dc352907fc980b868725387e9874c539f0a6d5088044a6ed703a369030"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f1889859114748896e16d1ac395ee2", "guid": "bfdfe7dc352907fc980b868725387e98e4031f57b97132fbe506ad4de3b0c507"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d342af38201a6ad3cb5b08ae8962c415", "guid": "bfdfe7dc352907fc980b868725387e9876a52755fdd9af2b1d967abfc7462e7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c981fe719e79f0112bdf8fd3cd83e6c", "guid": "bfdfe7dc352907fc980b868725387e98a0b31b6f5a25e1d95dc81e2929ec5cf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826c1d4dc2cb8717bdd865cd2a13789f5", "guid": "bfdfe7dc352907fc980b868725387e9804592a238db83e6026721225b7f733f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dfe86bd241a7531c555202f5f85e7a9", "guid": "bfdfe7dc352907fc980b868725387e98bcd9106745b8e488eabb1ff4f60849a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b870067dfa199af9a8b50b3477d73521", "guid": "bfdfe7dc352907fc980b868725387e98030dc7d0d14ae7f93c404bbf02a33656"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d310314d7591a79a024449dc8e84e294", "guid": "bfdfe7dc352907fc980b868725387e98e600255d9cad3a9cee5968d9e565a715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b644ca921de3953af8f56b175c02864e", "guid": "bfdfe7dc352907fc980b868725387e98f7f21eaf8318047ab6c8c4907ddbc4b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880076a3c73031988586d3d6c8f89bdda", "guid": "bfdfe7dc352907fc980b868725387e987989c6647e7422b91e9cac4ab577bfc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a9797c219e263584bd4770c7f955df5", "guid": "bfdfe7dc352907fc980b868725387e9800b0f4f2400bd5a1a85afdc531718dbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98952983f711ab20b3d18b02f100427252", "guid": "bfdfe7dc352907fc980b868725387e98efe5e34e25ff58a6e5a16fd416a31d30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98838e8a110a37fa2034f506db3811cf9e", "guid": "bfdfe7dc352907fc980b868725387e98ccf696c21604d7f64e1ebf2065bc7dcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec2087c1149f751efc580e20bc077376", "guid": "bfdfe7dc352907fc980b868725387e9823ddc42ed513169533ad3572510892ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981980e54dd5ea88e17f7ef3ba4aa3b61d", "guid": "bfdfe7dc352907fc980b868725387e988dd47ce9ca82ffe9f61e0b2185516ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98566b901b3ebda3c017660530793ba6a9", "guid": "bfdfe7dc352907fc980b868725387e98aa2d4cb9998e336ed3fbbeec623b385a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e916b28a3cd30ad44cfedb139c8ffcd3", "guid": "bfdfe7dc352907fc980b868725387e98f0697b29e071263b088ba935594e9ead"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981751d2ae3ff7d378fe8692829b7b2820", "guid": "bfdfe7dc352907fc980b868725387e987dda8865806668f2fb618e7911121d8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1999b0e6b6e7282312df2fcad18a20", "guid": "bfdfe7dc352907fc980b868725387e98ccbc157dfc483574430c7aeea2d4663d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6b191e641825b4dc648c49bd6da6435", "guid": "bfdfe7dc352907fc980b868725387e98e15062b99e7b1583f2bb71fa7aa6b2e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e658be0cc254b7b3a30755a0615ed91a", "guid": "bfdfe7dc352907fc980b868725387e98d66a71762f6910bb25444555c161996b"}], "guid": "bfdfe7dc352907fc980b868725387e98c4502dc7ad71cca06e0f2739b4a430fe", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}