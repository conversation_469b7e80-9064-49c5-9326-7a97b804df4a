{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98459b8d73e612df45e5e24085b8518ffe", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981fa87ce522b81ee41a3d8677a5bc5d71", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825eb09229341fcbf3388a3fde2a51866", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d700411d8125de3b0d47db41b0e40a6d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825eb09229341fcbf3388a3fde2a51866", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c4afec6e386f6a6e8b8e8f96887dbd0c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98653225b2f199eb8a2df503776d17a018", "guid": "bfdfe7dc352907fc980b868725387e98a5f99a5c50acd718b97d8b3507df0819", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2f86fbe18d2c5c2b830914f0523546", "guid": "bfdfe7dc352907fc980b868725387e98c531983e1191a7acb7e195a2ac7a5f2f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9b9f5513488bd22d91db85ba53c97e9", "guid": "bfdfe7dc352907fc980b868725387e98a05be10717dddbe26b27804d454a84bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f278af7736d65aab6d204d9fb04f5419", "guid": "bfdfe7dc352907fc980b868725387e9891f62278bac07968effd0ab6b43a88db", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bee50317dfb7727a6a862f4567a6e6b9", "guid": "bfdfe7dc352907fc980b868725387e98460e475d6e6bdad33905e21bf9315382", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98553d079f7a6b178f5ced8bd84e4197d9", "guid": "bfdfe7dc352907fc980b868725387e98868ea1fe140e27be5bd37807cc8e80d8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985595c009c3b7dc9c6db2ffdac85bb378", "guid": "bfdfe7dc352907fc980b868725387e98a37fc186ee2b642df4ce12db526896d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858d2c18dbb077d9db4a7254c43e706d4", "guid": "bfdfe7dc352907fc980b868725387e98ced8b4817b5adf69637f3bae367907b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c068928901519311610551b40e96d700", "guid": "bfdfe7dc352907fc980b868725387e98a0076013fddfc518d54921ca03d6f161"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddf875d5d362d9a40f2ecf0bec369c4e", "guid": "bfdfe7dc352907fc980b868725387e98a0c8f04c424f32101c10d3a88986d614", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989caff3084df87ad54a71e3210a545c3d", "guid": "bfdfe7dc352907fc980b868725387e981e5e63e43dcc7c10a7473350963cc2ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98986c17efad5903e23688a66ad0af61a3", "guid": "bfdfe7dc352907fc980b868725387e98ad264bc4818d556a5436ae2930f590f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869503708220f4dcd0fc7eb4e89cc2122", "guid": "bfdfe7dc352907fc980b868725387e987c69b86c86cfcde5c0c71435a19b8877", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988959e2464db0b4610ddb1168bcd20299", "guid": "bfdfe7dc352907fc980b868725387e98961068287cfb1cdf0ed10e742eafa8bf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98356da851e3b8b1b7c31871bfe4694ccb", "guid": "bfdfe7dc352907fc980b868725387e9873516e621b3bef428941b7810786b2f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb10f3a83f76df7b5e22fee61cbbaca", "guid": "bfdfe7dc352907fc980b868725387e9827a1d46bebb484983e9bdd09a4f2d930", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af397d6a37d97193d1910ba81f0d79f", "guid": "bfdfe7dc352907fc980b868725387e98da618eb3c5bc4ace8fb3288c03dd52fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f68d40d794359309857c87a4238c0285", "guid": "bfdfe7dc352907fc980b868725387e98d374809ddce5b91de4b9f18fbfd2a375", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984927152277b31bd19ac6d44d2a2e4c02", "guid": "bfdfe7dc352907fc980b868725387e98edd760c70053bbba48cad76a36079767", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9daebc81f6a1f3c5d70df12d7750917", "guid": "bfdfe7dc352907fc980b868725387e98a0b4fb18449cd1c605b278d203ec9b83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bcfe507c374c4a0cb8d71b596b16dbf", "guid": "bfdfe7dc352907fc980b868725387e98495f53f54154ae4d15daf6ffd3a1adbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896ffb56883506527edcb1ef770c99d28", "guid": "bfdfe7dc352907fc980b868725387e98952e6a0203371c08a5afcbf350992c4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98145fefbd2d029360c586cf74a10b1471", "guid": "bfdfe7dc352907fc980b868725387e98b7f3a84ba3800202535653537cfb8deb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bcd5e4c9e243e6e23c4cc9d449f2aed", "guid": "bfdfe7dc352907fc980b868725387e9848196cd5256bf5bfced5531725feb96c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3169c39e4724e51fde3cc03bfa0e83a", "guid": "bfdfe7dc352907fc980b868725387e98a74d8bf105b5128e9156569cd1da26f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acd97fd50ab395a1579276b1d3389bda", "guid": "bfdfe7dc352907fc980b868725387e9859b265612fd6f8abd9c742033627ddca"}], "guid": "bfdfe7dc352907fc980b868725387e98d7e53ea56bbc19455a01c01b7c8e55c9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980df9c5bdb1f7cfddf85b2006adae0b5f", "guid": "bfdfe7dc352907fc980b868725387e989b525b9d55d38005dd86d0808dfd73af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fa4fdc14ba8b9fc0ce7ac0c1c6159d9", "guid": "bfdfe7dc352907fc980b868725387e986510598d494914e0d128ded2714402d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883b5943f63dba831dd48f4adb06fe6b9", "guid": "bfdfe7dc352907fc980b868725387e98f4a133e4cbea37c35a8b3206fd24c992"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989416c5a3de8191672a1d9497c7f4ce33", "guid": "bfdfe7dc352907fc980b868725387e984813e28967e1a7d81ce6de958384badc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98555ccd40a08844798ccf8c1d53d7b36c", "guid": "bfdfe7dc352907fc980b868725387e9847f352ec901e68657052ef10f8933713"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981670e39b351fb71313627cd8f65d72a8", "guid": "bfdfe7dc352907fc980b868725387e98a8250387c367ca97fd2e6fa580c5c0c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dade96c165bee64180a2fad04f491a2a", "guid": "bfdfe7dc352907fc980b868725387e98718a55f64449d262fc87817710cf4b69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f9b35a10793575bf3d4088bcd5f3e7", "guid": "bfdfe7dc352907fc980b868725387e98d4322a0534bbf1a301ae19b714bcaa39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1408922615f65a4338f06af64941326", "guid": "bfdfe7dc352907fc980b868725387e98a9f115e0970d1d741ded18bfb8bb8623"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ffa4b8cb825b2cd6435ba79f4bfaaaa", "guid": "bfdfe7dc352907fc980b868725387e982bd538d750fb883659d02ef8afa0a77d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503ae90cb494150478b6699485fcc714", "guid": "bfdfe7dc352907fc980b868725387e9802c76cd1a0707e15bbf87bb8197290cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b362855592dd9cdd7a65e5eb404fe56", "guid": "bfdfe7dc352907fc980b868725387e98b134bb5023e8103121cd726452b17ee5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882d76c5d0a9e47a5f472225e09420269", "guid": "bfdfe7dc352907fc980b868725387e98689148740184755703389fdf92b55664"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de17970d5937918312070a08e6f0cff7", "guid": "bfdfe7dc352907fc980b868725387e98d13d1fdf20d4f33c72d5dd35df0e4db0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e8fac893149e650cd991dd69b5b34eb", "guid": "bfdfe7dc352907fc980b868725387e98e05f2186554ff71946482344ba343914"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5fffab0fb133de4c71754dd8c7e4cce", "guid": "bfdfe7dc352907fc980b868725387e980931421da5b6b548b50940ec65a64fdd"}], "guid": "bfdfe7dc352907fc980b868725387e98be672681bd2d79dd404de451030293c6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98a5ae634e7c907afb10842cfe6d116786"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e9852a27188ff547d2dabda595413bc2261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e98b8e942039fe53690a6aac9e95c45d677"}], "guid": "bfdfe7dc352907fc980b868725387e98ff9ddae4fb9465b51f763ffca342299d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a8ba7b14f47d7bc0c5c72b207d7348e7", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e981470be99f72df85c094d2de19a686b55", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}