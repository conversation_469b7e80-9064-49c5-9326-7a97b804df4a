{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f5703bcaa41fe1f6b87b39296331b1da", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6996a4e630ad56572378198a0dfab55", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0f712ef3271f229d4b1fdba0adc8029", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ac5924bf52a845e6e3a88560bc4250af", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0f712ef3271f229d4b1fdba0adc8029", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/DKPhotoGallery/DKPhotoGallery-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/DKPhotoGallery/DKPhotoGallery.modulemap", "PRODUCT_MODULE_NAME": "DKPhotoGallery", "PRODUCT_NAME": "DKPhotoGallery", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985c2a527c1ff64f1c23c77011ba4fcb87", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981ff995b604636311425eef60aad6e530", "guid": "bfdfe7dc352907fc980b868725387e989d2238698d0e4cf51938242f4e4759f0", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c36d2bc4790f3093cbb5deb17b3c5a1d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988c1de54612901212f31f77852d34d6be", "guid": "bfdfe7dc352907fc980b868725387e98bbf18855aaec85aec09dc43baa90c23d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981574f500f23d16737bb226e6dcfa2882", "guid": "bfdfe7dc352907fc980b868725387e982096df3c714bbef8674c569d07319553"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98814fcd8cb3a634d4a7fa448f286fe20b", "guid": "bfdfe7dc352907fc980b868725387e980a5ee4fba361fac6eb5c8e5b3ea138e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3d9ea623a832427377e662447b47c0f", "guid": "bfdfe7dc352907fc980b868725387e98cf423a07754911671e9306166ac62146"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f248a61a2d62abb5f011e8f5347e8fc4", "guid": "bfdfe7dc352907fc980b868725387e989aa4347e71aefa5022f299813464efbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c744dfab6000f52a60ec61c4935cf27e", "guid": "bfdfe7dc352907fc980b868725387e980f8d839d6d95a89a5c9b0bdd55f5b595"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb0f193af2a58cc45dc96ed0f799756", "guid": "bfdfe7dc352907fc980b868725387e98f74261796297db7bc6cbe6adfbb26407"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa61c888b27c4d7f5df37fb812d6dea", "guid": "bfdfe7dc352907fc980b868725387e98d813df2ccb09fbe91dc6be9793f44d4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e893e86a41704e574a007daa1ca729", "guid": "bfdfe7dc352907fc980b868725387e98ed8d3e2034cd77f283adb9a53d0c2e19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d680792caad65ac109cf2fcc666fccd4", "guid": "bfdfe7dc352907fc980b868725387e987673af12c5193b13cb8a94565bdaff75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3843f8c7d5dd19f7868fbf91692c53c", "guid": "bfdfe7dc352907fc980b868725387e989a54103b534174944f07450528e5a0cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873c5c4541ef3bd01b7a72eea9ced2d20", "guid": "bfdfe7dc352907fc980b868725387e98080a19be240a24ef50877e587ffc8e38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e14d11d0cd2a013d631fa1890578a31f", "guid": "bfdfe7dc352907fc980b868725387e9870fc16d0cd84e0cc1f91609817f88b94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fa989647a8ab97acd5d3c4526ad4c25", "guid": "bfdfe7dc352907fc980b868725387e982fb648f915b36621c6b636b869b3b5c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1eeff28cff394f50d5d1b7a3575a511", "guid": "bfdfe7dc352907fc980b868725387e98fca6ffda6d1d8a1de0fe8e275ba16546"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f62902536efea11f9a5c73dcb3ed7207", "guid": "bfdfe7dc352907fc980b868725387e98fb707e6ba37120ada03de08542022a09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d879a1f918509763063f1d9696026c65", "guid": "bfdfe7dc352907fc980b868725387e9891df69faaaa2ab7ea0fe7dab979fb8ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd496217a2d82893de08888655c98bdb", "guid": "bfdfe7dc352907fc980b868725387e981a6a690bc2caefdc8e85faa7d33ec333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e4504235ded0cbe630567cd675217f", "guid": "bfdfe7dc352907fc980b868725387e98704ce8de0f254f9ba0b7e342e1deae8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d7cbf6e714cc024bdf9f4b712ea09e2", "guid": "bfdfe7dc352907fc980b868725387e98ecfd8ac5bda4406c50775812d8783990"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6c87f9fa69754d53bfef7b9be830a16", "guid": "bfdfe7dc352907fc980b868725387e98bd8fba6e937da6c35269a9ebb665f3dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cec3091e971c14e8d9c679117699ff82", "guid": "bfdfe7dc352907fc980b868725387e98b1c387f83c38cdef246c80e6bdc4534a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a873a93c5aa3776d5a70b87d886aa2", "guid": "bfdfe7dc352907fc980b868725387e985dbce9f0b438ba340b3c91fbbbc22ad3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836babb5be08c49b9f0f3f61d6a67723b", "guid": "bfdfe7dc352907fc980b868725387e98d187a4e42cb6eaa0efcacac496e78b8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98811a86b9fdbdd2327c0bbdf58bfac6a5", "guid": "bfdfe7dc352907fc980b868725387e9857ef3e378f2a0533a08b2bb426e57f52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e14abf5a6af1e6da0b528b7a7cbda4e", "guid": "bfdfe7dc352907fc980b868725387e98a87b65427ce26f1ab908abf736069a6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a425b1ccb931edfb4e4ed639c159c02a", "guid": "bfdfe7dc352907fc980b868725387e98493d1337e02f3998f6d61dff6b228cbf"}], "guid": "bfdfe7dc352907fc980b868725387e98bf87f6c56b488d8460b65a36f7e181a8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98709db05cbcba8a38b5fff878acd3dc96", "guid": "bfdfe7dc352907fc980b868725387e98ecb6a494445066a256cb808a1410a098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986296ed536311979929249249546219ab", "guid": "bfdfe7dc352907fc980b868725387e986b4bf868f4cc7357c2da77aaadb0a2d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e9807972d2ceb58e4f94807df30b4d4e8a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee0e87b873ad96ad0a06a562ccbc5f5", "guid": "bfdfe7dc352907fc980b868725387e9884bc86680cec56ddff00bf6e438e333f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e989bcd18f315b41240611fcb6d945169f5"}], "guid": "bfdfe7dc352907fc980b868725387e98c7546a10c6c8c1d6e4d7c29b59c7df65", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98eafef28039345635564e459ad37c85cc", "targetReference": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937"}], "guid": "bfdfe7dc352907fc980b868725387e98cbaaba683aff8efaf3fa98c503ac92ee", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d3f65728b12dd217475d1283ee417937", "name": "DKPhotoGallery-DKPhotoGallery"}, {"guid": "bfdfe7dc352907fc980b868725387e98c46180aea4e87057640961e6db37df0d", "name": "SDWebImage"}, {"guid": "bfdfe7dc352907fc980b868725387e9872eabefc63c14dfe52fb0c95ad90294e", "name": "SwiftyGif"}], "guid": "bfdfe7dc352907fc980b868725387e989d0a1858a86fd6e6731ed20f88a1e515", "name": "DKPhotoGallery", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986e90c628ccd44af657bee5ff4af2f692", "name": "DKPhotoGallery.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}