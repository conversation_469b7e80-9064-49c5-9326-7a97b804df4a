{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980b8568b7e88b7e35906023d318775432", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824015d96af6460266b7930742c2e102b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884bb6024dc5821350b4ba3b02c4febfd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987bbaa154f4c400e89e7a26f8a69fb740", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9884bb6024dc5821350b4ba3b02c4febfd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseMessaging/FirebaseMessaging.modulemap", "PRODUCT_MODULE_NAME": "FirebaseMessaging", "PRODUCT_NAME": "FirebaseMessaging", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98920599965eab10fb801d90d0918843f6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b9ae8bee3f361cb16c288d51048603ec", "guid": "bfdfe7dc352907fc980b868725387e98d438b83bec79893b4d9af957abefe14c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f78c1ad96d9b1c59fe32ab8bb31d036d", "guid": "bfdfe7dc352907fc980b868725387e9870122996a205b14d372e677a9f222d21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d26f6e5c0562dc1a3e52ef65be2dc14", "guid": "bfdfe7dc352907fc980b868725387e98d1cebf15d3681a7bf47ec00476c0c2a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dc5de1adf01ba183bd37e245faea8de", "guid": "bfdfe7dc352907fc980b868725387e98dd7795fe356956fd5311f9026ee75e7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef1635415c66c24a14ebf61f58c34aaa", "guid": "bfdfe7dc352907fc980b868725387e98de497ca8e1118c9437801d779d58c2c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833c5fbbf94f64d02d9b43ec4f709a5da", "guid": "bfdfe7dc352907fc980b868725387e98ea19361c4925fcd86e2674b681ff6068"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ccf906c2c1495ed4735922a0963e4c3", "guid": "bfdfe7dc352907fc980b868725387e98042f689f1c39bec5515c46b8f02725e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842f13f7070f5d9429321b404982b3202", "guid": "bfdfe7dc352907fc980b868725387e9826837ba0616aa190877368ba657b6166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839dd771fab3410e39d020851a2842d7d", "guid": "bfdfe7dc352907fc980b868725387e987ba8521758ebb0231f2e4d97c12db8bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8d9ddb8d516d57616017d9c53dd507", "guid": "bfdfe7dc352907fc980b868725387e9848cbbc773eb7b1f8b381d98ec1eeb01c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d646aa4462145345c4bc4a38602cde31", "guid": "bfdfe7dc352907fc980b868725387e9884268403f711b766d61445ef893e2242", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859357b481014b02045543b7444936842", "guid": "bfdfe7dc352907fc980b868725387e98e419285610d7ddf3fd9c9ca027feca31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c3ac4c2bd716361138b2102bfdaac2", "guid": "bfdfe7dc352907fc980b868725387e98bccca51c0163997b71a964ef62987242"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a473755425832fa4bbdd946f97f434e4", "guid": "bfdfe7dc352907fc980b868725387e98f92a16d54c48660c90efef5738022240"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f4a9297d1e7e6f9899dde11fb7b21e", "guid": "bfdfe7dc352907fc980b868725387e9823d9ad30af008e478945a84259b2d9e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98844b3852e207d4315a446567e57ec22d", "guid": "bfdfe7dc352907fc980b868725387e9807904aefcaaff467fa80b7511f1f7d29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858c1d3a550f6751af812291b40a5af1a", "guid": "bfdfe7dc352907fc980b868725387e98cc262e2b57ca21f7c855635c36a25b9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b8446f7c3cd9da6ff04e492916fd613", "guid": "bfdfe7dc352907fc980b868725387e98f53f40285bfdadd11576bbfd2ec2d1bc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b4821956b004f7b0ba2c2a316517663", "guid": "bfdfe7dc352907fc980b868725387e989f9adba6bd22ffb3829268a7a1e13399"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d433664bfe2d206f9e544b4564e035c", "guid": "bfdfe7dc352907fc980b868725387e981492e36d5bf69f5dc9b8aa5752287f72"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a521eeda9a85330b9342eca36a1fd668", "guid": "bfdfe7dc352907fc980b868725387e982829a518eb017bf1085530c91a9db6c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce49e409dc98399ce87719552327022d", "guid": "bfdfe7dc352907fc980b868725387e9870f87f1ec16965b724d074a63b59e8f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d29b1849191f77ed39aa25dea5ec8b74", "guid": "bfdfe7dc352907fc980b868725387e98907434c1e88230defe83ab58a876976e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823e673d745a6ec5d53a624135ea4ed65", "guid": "bfdfe7dc352907fc980b868725387e98f372733c9047987c9910b568016820e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d46ba06d1c4448032725911097f76bd7", "guid": "bfdfe7dc352907fc980b868725387e985e50ad4f46451f1b7ef9b9f95a296766"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880310191907ea6dd99cbf44d5bf9267d", "guid": "bfdfe7dc352907fc980b868725387e98f20770b6f1fb69a28454d7f72fda8c1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de2f4609d0cb88621d083fc448b5d314", "guid": "bfdfe7dc352907fc980b868725387e989316a3b83a9c7e42a3ee15feec7899b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a879defbc3fae73ec7dbe757033d7d89", "guid": "bfdfe7dc352907fc980b868725387e98ea2f9cf5cf21e34ae43732b509196acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d2d9594f57bfdd7fd28c7f9cc37f0ab", "guid": "bfdfe7dc352907fc980b868725387e9844db83d96165651847ca7189513c3c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831cf92a325367e6acf32b9575c2c3426", "guid": "bfdfe7dc352907fc980b868725387e9885b5588cdafbedc7cd3e1775433937f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc4a0b8fa6392a47bf4ff4067a0436c", "guid": "bfdfe7dc352907fc980b868725387e98211e00eed07fd3169dad29bb58609afd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956eb6d718e029318194cb9732a062d3", "guid": "bfdfe7dc352907fc980b868725387e9850608b49a438ac3ef4baee6341a646f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981865a631bc9b06a5838c5153d33b6115", "guid": "bfdfe7dc352907fc980b868725387e983fca3992d0d97f1a6d702e75b3a3c81f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844222e732818b43223a79074beaf72bc", "guid": "bfdfe7dc352907fc980b868725387e98db5e765892a156d3671993f7faf0ff59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffef402e443d20463cdc361580a7ae79", "guid": "bfdfe7dc352907fc980b868725387e98d5fb85a56092e64310bd644a3306efc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fd8f95484f12050a02895e8ef5fd013", "guid": "bfdfe7dc352907fc980b868725387e98c5a61a03af456c4c5acd840176d2d913"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c976b1ba0ccb0d15db887925772f5229", "guid": "bfdfe7dc352907fc980b868725387e9860574608d4a7b3973b57b9f252a734f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb1f424eeac8db815c891e5122ce3e38", "guid": "bfdfe7dc352907fc980b868725387e9810ffe50fecfec6fc6d5b8992ed9bb1e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eba0555d391a51b4d2a969c8bdda3ea", "guid": "bfdfe7dc352907fc980b868725387e987c300381ea00a7ae415102052049108f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bf8325869fe759145f31f650cccda97", "guid": "bfdfe7dc352907fc980b868725387e98a03e6febfab9fda3d587a70ce19aef54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaa938938f85c975511e976595ebe669", "guid": "bfdfe7dc352907fc980b868725387e988baca19b0ae1047c4a8f7bdced6eb64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afa1430ad9dc8b82706dd421e6141844", "guid": "bfdfe7dc352907fc980b868725387e9887533ff15ae0d190baf7189950a22b26"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d9593e802cfd789c33591267b7d3a28", "guid": "bfdfe7dc352907fc980b868725387e980ef9bccaf8715762e0170349cf9ce670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d6a962dcf059d43d3ef22269b99fcc6", "guid": "bfdfe7dc352907fc980b868725387e98da24e1ac240c3df65545948e87449f1c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616ff370acdac7c129f729a0353763ff", "guid": "bfdfe7dc352907fc980b868725387e986dae486ce7240dcc66f197e8c89b931b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85d571dd7c9ff4be95d26e5526a84e1", "guid": "bfdfe7dc352907fc980b868725387e98fc318ff69ca553e6fa84e78645fa1107"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ae49e666fe5bbf134a53c4af4279c2f", "guid": "bfdfe7dc352907fc980b868725387e986c1b354c0499470709582a3486803a98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba0813ffb7616a068b0516f89a2bd7c1", "guid": "bfdfe7dc352907fc980b868725387e98a4e160c6e58569c4b63d0c02b4d7469e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892047070cb1cb5f8dd851bf6e4c44935", "guid": "bfdfe7dc352907fc980b868725387e98dbf146bc28a839f842c53bcaa1282c9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989561626e1d547939cd2ee157aa79bd79", "guid": "bfdfe7dc352907fc980b868725387e984a69ea8a6be2fcf5ab0c1c329fa956f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982000bf0a49ea5f25a38aec2581a8d37e", "guid": "bfdfe7dc352907fc980b868725387e983f748dd20929c0473fdf64d7d3767d64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853d5e4e9e05f4ce3a907882a06c86083", "guid": "bfdfe7dc352907fc980b868725387e987173c8eda4d7675bdd583653bd8d42fb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810afa2cdadff6da49236057fef455a52", "guid": "bfdfe7dc352907fc980b868725387e98496f5bf049b7cfea24ec2e4de9ff8f7a"}], "guid": "bfdfe7dc352907fc980b868725387e98fb32f64330ce5d056b9ff947110a268b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b690cdb0a75f26aaa3a9f766ec261c22", "guid": "bfdfe7dc352907fc980b868725387e983682a6f7341169743f1dd5defc4aac84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc96e3e2ccb0a3442d12d2d8b95501a5", "guid": "bfdfe7dc352907fc980b868725387e98b03bf8c583c1952591e7dc343ada3531"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989544b911db3fbe756fbd9e6de9ec9e39", "guid": "bfdfe7dc352907fc980b868725387e98305701fa09a4a2df296185c6fb688c8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98016548305d0070778bfb6561bfe60cc2", "guid": "bfdfe7dc352907fc980b868725387e98ce579865c8fc88e9eeeabe2ffab83d4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d278f86f73f97818bcd7d76878b8966c", "guid": "bfdfe7dc352907fc980b868725387e98332346ebb8f0f7463394b3145feb0588"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98148b3ea84787fd497c2e341c299e8431", "guid": "bfdfe7dc352907fc980b868725387e98eba9ee81f51eeba99d682e5dd2f1cd82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98509574a8e6d7cf0fc0774eaa43eb4ce6", "guid": "bfdfe7dc352907fc980b868725387e981fdedf809d5b9487252b1f8f26063e1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982356898916d15f05d7cc097ea2db6009", "guid": "bfdfe7dc352907fc980b868725387e98cc8d9c195aca49e565db793e409e2296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982270c5b71b0ebca9340cec701bae827e", "guid": "bfdfe7dc352907fc980b868725387e985bf0af844038b51cad4207edf6f1b9e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c445b8892f2c971ce910f7b5a8db9c9e", "guid": "bfdfe7dc352907fc980b868725387e98fb0db03e9ae91e2f72451094a64ddf41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a17aedf0c208f24a8abfb35f95bf478f", "guid": "bfdfe7dc352907fc980b868725387e981f6871a1ca10d366d1437e5bb1a511b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e44300a0b26fc1d82fea0a848cd2233b", "guid": "bfdfe7dc352907fc980b868725387e98d604ba7fc374f93a4996fd23b316d755"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9841d724e8a01ba8368c5f35b186c72520", "guid": "bfdfe7dc352907fc980b868725387e983f4974bfe22dc095d3378d5895aaba91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bc7ba54a8284863e6cbe99322721a89", "guid": "bfdfe7dc352907fc980b868725387e9892ffbebfc57f468300e9bfb874a5fc5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e2af53065bf9418e66000911b2d6861", "guid": "bfdfe7dc352907fc980b868725387e98a50c711fdcecce98f64f0facaa552b67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4b2344c561191fcd20d45aa19225339", "guid": "bfdfe7dc352907fc980b868725387e9830ae370fa898c8e4868c0d0dbcb6f258"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3171194d8b8f6571f323d220fdad0ef", "guid": "bfdfe7dc352907fc980b868725387e98df6e87cd2772f3d1491fe52d02b3dd62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98175c2c4c00fd01efa671fb2efd14eed2", "guid": "bfdfe7dc352907fc980b868725387e98435ddce9917e40a0899d849e9e7dc586"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857f845cec0fc96620ae252eb5c919542", "guid": "bfdfe7dc352907fc980b868725387e989c6631c08e8595fde01853e208c9241a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d85dfe8813a906b829250ef7b5100928", "guid": "bfdfe7dc352907fc980b868725387e9862bae76947cf800a7ae720e2506c60f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d622487fe5b198e87075d38e3080d66c", "guid": "bfdfe7dc352907fc980b868725387e9852782976041092801bafabd49026fb0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af763edb1b8c558ded8dcf56ebefb3d7", "guid": "bfdfe7dc352907fc980b868725387e98bc27b6b6f3153a886a063af4b8e2ba30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989939b8166f489485b4796d218723a552", "guid": "bfdfe7dc352907fc980b868725387e982d7df05fd59fb5183d8f32915100fb5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878acc094cb324a2e0201b4d548f30347", "guid": "bfdfe7dc352907fc980b868725387e98ad7e55cfba1d048e4dc60c90009bb846"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efd23f6ab45f7d0a32c97e3f03a85dda", "guid": "bfdfe7dc352907fc980b868725387e98b913fb21808a6e20258491b5840a5a8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819d5bb0404cb282ef96c03da8b6531bd", "guid": "bfdfe7dc352907fc980b868725387e9803a6c17831c7a8bc45046a1eafbeea6a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98462405c2f1a90fc94748c2288df11776", "guid": "bfdfe7dc352907fc980b868725387e98d1960a1a3c07de06604645aba3056b02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98962923e285c1d90eea3e1bba1f650ad1", "guid": "bfdfe7dc352907fc980b868725387e983cf10cee74ddb35fa49ecac6c506804b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982716d102d8fd3eb6e90bff46eca15aa9", "guid": "bfdfe7dc352907fc980b868725387e98e4746112d51e4933927ed7aa14a335c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2e16cf6f959ed566fb7ffd149870f86", "guid": "bfdfe7dc352907fc980b868725387e98cba827c361852880e91977fb7ceb7042"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5f7f992f351a118a7dce2cd613f191f", "guid": "bfdfe7dc352907fc980b868725387e98b539bb798b6e422b4e7abb939b687084"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fe52da33a46386c6e25a91972697c75", "guid": "bfdfe7dc352907fc980b868725387e98e0ee063426ebc22f5b2860410d45b64b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e343f598336bb0940a376539e4f08b9f", "guid": "bfdfe7dc352907fc980b868725387e98d79842ddaa3cfc35eda18f23fec9e1f8"}], "guid": "bfdfe7dc352907fc980b868725387e98d6079a00401d100b0a651941b7a97e9a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e980ae8fb040e0065aefb247b582c1ac236"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e56187f5d1ece87c9a12e28d3c1a5427", "guid": "bfdfe7dc352907fc980b868725387e98d71388b102f89e6183c0199aea150b19"}], "guid": "bfdfe7dc352907fc980b868725387e989d712272e171d35cee0b2fa1fc85a28d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9879d468f259ca88011a19f443f5d822f7", "targetReference": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab"}], "guid": "bfdfe7dc352907fc980b868725387e981ecc4f4c7f07c0a548ee43eed7a81bba", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98974c3b2447afb83a5c25c38d101a48ab", "name": "FirebaseMessaging-FirebaseMessaging_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e983da17a3564c774dfaa331fa07754d2bc", "name": "FirebaseMessaging", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b3b0fadaedeb0138a07668440d83e3b3", "name": "FirebaseMessaging.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}