{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98aab3e45482bdf0d16368d72634d28c09", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98986546a9dd19ee25fef5242e5f2953e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98948fe8be7d6b67a22ff357d4c0401297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f60eca81a3a3b4110839cbc5357856f7", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98948fe8be7d6b67a22ff357d4c0401297", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985ee17a9cfb1d8c13c76cf8bd2d90217a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9880832b38f740e6030978ab6d17e03db4", "guid": "bfdfe7dc352907fc980b868725387e980ef9ddb0b934c7f46cec9d19deb3d4e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241ed61cf0a01d2de663b32c5b9f942c", "guid": "bfdfe7dc352907fc980b868725387e98f1f2d5604a639917e664d753b70226f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff6ceb0bb8fe608c2040ec07123401f9", "guid": "bfdfe7dc352907fc980b868725387e98316a74b389ff6f9b5034404734b5e17f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0844fc78d1e9343d7a2ca158f32bbf4", "guid": "bfdfe7dc352907fc980b868725387e98b422f1753e3b34f36cb7554cdd9b37ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb578c488115ebd5b97d4e228e710258", "guid": "bfdfe7dc352907fc980b868725387e983e5d837b874a0459bb467fde74a3437e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ea7031e9724200ae325b53977c7913", "guid": "bfdfe7dc352907fc980b868725387e989a7ac2e0f4b9f4b99e198a08df65a35f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1616ac7c5fd5b642936acae750e4080", "guid": "bfdfe7dc352907fc980b868725387e98d20bc88c508848c012c4d4a8fdb2bcff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a84a3256cc5f401d26393654b676430d", "guid": "bfdfe7dc352907fc980b868725387e9812be42ca8d31ae061e3467dcddd09f73", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d16d5152dc0932c2049044ec36d61c7", "guid": "bfdfe7dc352907fc980b868725387e98b45e10afdb9185d682f9d40cc6434404"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98321512195b3e04637c6800657c180f22", "guid": "bfdfe7dc352907fc980b868725387e981664d12c40f10fa695132793c2aa4a34"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830c282272120d0bac380b48e8cc4df62", "guid": "bfdfe7dc352907fc980b868725387e985b1b7b9b74cbd63544bb4149da31dd8a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cabbaf7445158ba8dd1226938fb3bd1", "guid": "bfdfe7dc352907fc980b868725387e98cfc4d4bc8b11df81444647c637b22385"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848a6c1bd55f680dc6ff1edffa2d58748", "guid": "bfdfe7dc352907fc980b868725387e98e103c6724bfcf5f83264db091308097b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066f7f07376192579d7d9feb09207d1f", "guid": "bfdfe7dc352907fc980b868725387e98a285b0b4cda89b7a683f19fa9e4cedb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfb7a1149cedd07805395f7d5c11a0d", "guid": "bfdfe7dc352907fc980b868725387e98a16a4d6039fdf679a86c49d22aca6c59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab093d50ae216ecc517f00c325b472b9", "guid": "bfdfe7dc352907fc980b868725387e98d9712324c7b76bf0499dcb6164428aea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857722323c45b5352590cba83db337d70", "guid": "bfdfe7dc352907fc980b868725387e98229b490d07bb21fe40fbe29dd4bcca99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98053f21e61c9d0d4e5e9a71b04c357a6e", "guid": "bfdfe7dc352907fc980b868725387e98cae6cf04bc94aaf449babe9457abc97a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839f58c3b3d298128606c585c7c2a4b51", "guid": "bfdfe7dc352907fc980b868725387e98467175cbd6f86fb5305dadcc14fdf487"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98408572ae56ab59f555df7a564a36544b", "guid": "bfdfe7dc352907fc980b868725387e98f0db3889e6a8a68d21175d89d76a69b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8a6cb084c12d011d3dfbb31362e64ac", "guid": "bfdfe7dc352907fc980b868725387e98154f95c05343055bf9b36c8653df0ba1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879821b4c47145436d7bc0898c06b8d6c", "guid": "bfdfe7dc352907fc980b868725387e98a179fcbc76d653c21bb88d5d34cbca4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf927828c049f76499971406961db975", "guid": "bfdfe7dc352907fc980b868725387e981a8dfbb3eec6ea00c0ec86b603ecd20d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a41e7bc4bc4302592c7c7492a9bd016", "guid": "bfdfe7dc352907fc980b868725387e9825cb2f28b8986e25244ba267f701d739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be891ed4502aca8fad687680f6fe59cf", "guid": "bfdfe7dc352907fc980b868725387e98def750f1f912a63de2aaffb6beab5647"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d69044827bf7cf3d2c71ba538c5036f", "guid": "bfdfe7dc352907fc980b868725387e98c14eeef7b2d62756ed6a80618d57ae3b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98752e9ccf133e5b41ad78c9b10d577620", "guid": "bfdfe7dc352907fc980b868725387e98b4ca59a2acc0c495ad70d2427b0c8e16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844c3f4cf9a8c3e3d6ddcb699254b0983", "guid": "bfdfe7dc352907fc980b868725387e98cf4ec91344b17f7926d6a5e7aae97666"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889aafd95d7ad02e54f543b535c5f0616", "guid": "bfdfe7dc352907fc980b868725387e98836680e6ee21a10a98d04b7eb322ab12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824e8a7e1c3e9650c278ff2287fee8ca4", "guid": "bfdfe7dc352907fc980b868725387e985733217ab15acdb3e92e4986589478e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e75c919eeead5af80ce17cf0f97773f", "guid": "bfdfe7dc352907fc980b868725387e98baa85bdc655541d275580ef8611becc6"}], "guid": "bfdfe7dc352907fc980b868725387e9848969d416b0b4ec6251b004f82fbe018", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98262f98b8f6bb99647f2975c3ad5c3b63", "guid": "bfdfe7dc352907fc980b868725387e98ddbbb4620c556a691700540f2860c15f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849c882015de43deba4ce3f69a3c883ff", "guid": "bfdfe7dc352907fc980b868725387e988236e83a1445641ee56a49bc743fa908"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f00476c749030d3ecc35c6c0fb7122", "guid": "bfdfe7dc352907fc980b868725387e984ccea511811bbb31aa4107a65efcd4de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7d9fb34f7e809d4b74e3a898c36019f", "guid": "bfdfe7dc352907fc980b868725387e989330fe16107c13acfe96c449d3d7e522"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5963d244723b7a0a050b369540c65ae", "guid": "bfdfe7dc352907fc980b868725387e98164963357044a01625e705c43253498a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806f22bca0046a2f2c038ad93170fc6dd", "guid": "bfdfe7dc352907fc980b868725387e9833b1bcbaa5cecffb15532f29e845778b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824e95f237a0635244e33c98d73dfb82c", "guid": "bfdfe7dc352907fc980b868725387e98ac8d377f3f5d232cc4b837b40a0bb75f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98febb3576dbae508a4da6ebe13e26ec38", "guid": "bfdfe7dc352907fc980b868725387e98e6eb11ada1937a61de75c5f1bda8409f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ba337b5549942c0cc7a21d2d4a1e94", "guid": "bfdfe7dc352907fc980b868725387e983076ac7df43a21a36fa24394c8accf95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815f9a9a09e3f94255240cf98d6a52f4c", "guid": "bfdfe7dc352907fc980b868725387e9833137b48679f11a17f42553f774c6c5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884e24c3e93eed66ff44444f9d0030c8f", "guid": "bfdfe7dc352907fc980b868725387e98ffcec92e0a5d35e10af2fb656b0a5dd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa7132f4dd364c1d28451b92ee906ce", "guid": "bfdfe7dc352907fc980b868725387e98dacd1377205f5083040522e4edcc3909"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988751ce2333a49c7ceaeb3fc60dabcd0e", "guid": "bfdfe7dc352907fc980b868725387e98690afbcb415f39009f839677ef31c0b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988205bff269399fb86d0cbeb0c639560d", "guid": "bfdfe7dc352907fc980b868725387e9885b99c561da794773f496bf18846dbf1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a67f2934e669acab320a44f88ff28c0", "guid": "bfdfe7dc352907fc980b868725387e982045ddcf045c4d91c690231e5db44c95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982719cecd8593213a271f82f0b634cc87", "guid": "bfdfe7dc352907fc980b868725387e98a12fe18d0290f58affb5deb8ba564e70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98826ee1e4317dea4ca3809342a7f6d96c", "guid": "bfdfe7dc352907fc980b868725387e985306948379fffcc1bfc3265f898145fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837b7bbc08eae7a7e72db298c1ab54c72", "guid": "bfdfe7dc352907fc980b868725387e98c3dc303b610238fff85b6f63c681a66a"}], "guid": "bfdfe7dc352907fc980b868725387e98d149618e668538929e8f7edd869fb9a6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98d317c75e15db2eb6ba9d5188684c5d10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c66b5324530b90fee7ec191645484220", "guid": "bfdfe7dc352907fc980b868725387e987538e42d0a385942ade1b8b1cd08336e"}], "guid": "bfdfe7dc352907fc980b868725387e9899fc88565e3261b08448c80a749e1fa3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985a3df3a13e3e844cf37a5efb11d07ede", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e98c2f82d3875ef1f34573a4dda7487996c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}