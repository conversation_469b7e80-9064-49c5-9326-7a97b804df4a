{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a03a89c6f321c3993348bac314442086", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a2306391936207ff3a77bf532265ba12", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825080dd9ccd39b3c0803a73a0e083dc4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98487ed0955f6ee94f3b149ed57f321128", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9825080dd9ccd39b3c0803a73a0e083dc4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985f8dc2559c8635ba7bcf52e3f977ac5f", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9801f48480cf1962617d75a362a13aa11e", "guid": "bfdfe7dc352907fc980b868725387e98ce154a8e9b94645ff1a0431655f40ed3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9096eb676e66e9b2b8cee49e53aa3db", "guid": "bfdfe7dc352907fc980b868725387e98924f494883e492dd44b16ee75e2746e8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893de4a6539c05220368772390e2b565e", "guid": "bfdfe7dc352907fc980b868725387e98b13987e183ac833f9dd46006288f2edc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982de7d1f3fd7c75e0a224a8a4e78607b9", "guid": "bfdfe7dc352907fc980b868725387e9808b00a9e9543218f58e669ebd6a809b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981909d7860d5d91390e01b46210095c38", "guid": "bfdfe7dc352907fc980b868725387e98c30626d1095774ac86ee7da68030496b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986324527433c2f4ff7ef48e6c81d611cd", "guid": "bfdfe7dc352907fc980b868725387e98cbb4ecb344934b5fcceb199b76f4f465", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98886669d94d43dff43fcb18866dfe97d4", "guid": "bfdfe7dc352907fc980b868725387e98b31c0a3c15690c4556e4de5101218a0d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fec0378f575cefd13d8d993b2b434a86", "guid": "bfdfe7dc352907fc980b868725387e981817df4c97df004455192f5a18d4b633", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb89d316edb6bdafa6fb36b063383ca9", "guid": "bfdfe7dc352907fc980b868725387e98f4f4abd577a65ea12366e9b3140aaa6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ccd433abb2e4dbf9e48184163d56a84", "guid": "bfdfe7dc352907fc980b868725387e98762ba2e6464fd0c3aa7ac5c8c4f2a21f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858f4b94707dc211889dff212b2514422", "guid": "bfdfe7dc352907fc980b868725387e981beb44c1019d074692664395d5d64893", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844cf48e33470caea144dc0295ff59a21", "guid": "bfdfe7dc352907fc980b868725387e98d85e49eaf4c3cc4c0241e0b3fb92e4b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98758eab5a7b8a36e2bc16af5ab27e110e", "guid": "bfdfe7dc352907fc980b868725387e988d5bac75b1b3aa1df7ff8a5af8adf1ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f970c0358d5e043fcb9f55240f235efc", "guid": "bfdfe7dc352907fc980b868725387e9834441b8fd38f7f7af82b3e6af572f1b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a6b7b6cba00b5446b6c7d3cb8fc5bb7", "guid": "bfdfe7dc352907fc980b868725387e982f1b6d6c3e214bcf71d983f14c3d74bc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989daa79ba156b851ae14af46906b3a0a9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988629e8f50b0336de501604c6bf055ecd", "guid": "bfdfe7dc352907fc980b868725387e98b0fa831daec91e16da4dc551b8abb40d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876fc4a5d3e96d944c6eb7f875d074c74", "guid": "bfdfe7dc352907fc980b868725387e98764d50cc71d9a7529bdc50e5a700a43b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c5432bbd6d64b1d4f03bd14911caed7", "guid": "bfdfe7dc352907fc980b868725387e984a2e813e52a1ab46a2d0cdc2e94444dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988595013bbb280760065f71622e53277a", "guid": "bfdfe7dc352907fc980b868725387e986f81677235c0a164f79e3119b5c5d6f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840bcd3c92125b639bf53662df8277082", "guid": "bfdfe7dc352907fc980b868725387e98122c55e7f4ff32736415ad5b5f66e2ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e794a33c71557a393687b9ddc410d524", "guid": "bfdfe7dc352907fc980b868725387e9866b8ccfbc45b6204e63a85b519bbc1a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c7ec4b19f62c12228d175b739b8202f", "guid": "bfdfe7dc352907fc980b868725387e98740c40fcc9022b4cae98e12c30d7d4cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3154d3a4bed2486cd5b061f2253f478", "guid": "bfdfe7dc352907fc980b868725387e989434c440eae986eb6de039e4b017314d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a09548e112c7151c40e9cc213ba34af8", "guid": "bfdfe7dc352907fc980b868725387e987a1e1f9d7ebad4fc7d39b208b18bf280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815da6af122b4928d4aa2adcfab8add31", "guid": "bfdfe7dc352907fc980b868725387e98e93f7edc7065cc82ed723d9d20538a00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5f22382d3c7c229601c81a5e99cb425", "guid": "bfdfe7dc352907fc980b868725387e987592ecf8a4348c33d1b745e57abd4589"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862917eb02053c91a5a5da6cc4e9dff9f", "guid": "bfdfe7dc352907fc980b868725387e984a1a2774f6b2777dc237bb18ccd2b00f"}], "guid": "bfdfe7dc352907fc980b868725387e98badc07441c3d3f800ea824e69bb5beff", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98e3f6bfa9b742e772ecd92d7503f55e33"}], "guid": "bfdfe7dc352907fc980b868725387e98d7f82e88a905b7e353a4e6f9718eb7f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98cc8ac38168987ce84f85d8beb6728369", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98558074b4aea693c85351e88b3b5ba2ae", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}