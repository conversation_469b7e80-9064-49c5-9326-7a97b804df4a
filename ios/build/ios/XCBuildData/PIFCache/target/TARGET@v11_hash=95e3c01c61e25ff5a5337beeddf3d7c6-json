{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982d8614377054c08c1834ff02c5b09e30", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f9d1007e825d77c0c012df441741677", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a8b03f1066a08de4d93e81e54b80431", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bda729b872d181b0404809fc38bfc196", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a8b03f1066a08de4d93e81e54b80431", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9824f1d10501dde51aa6fe4e8887bc92a6", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fd7e084d1ca168eadd8464e6953d35e2", "guid": "bfdfe7dc352907fc980b868725387e98be65d2f6d2023a46029c25e549b9f4a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc8e11942f6902ac60698bd91c0bbba7", "guid": "bfdfe7dc352907fc980b868725387e98abb0f13e6ffbefdb3947ca5b0040449f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984152e4fc89558336521b0896357dbf5c", "guid": "bfdfe7dc352907fc980b868725387e98bafe2fe43cbad2fe0cbcf1e6ef43e5ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5ab1228cb295c050cd1992908199be", "guid": "bfdfe7dc352907fc980b868725387e9866f9ed752ec8ecf5d3a1edd02ed138b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d9305755532332ef172ebd7c11e3cf4", "guid": "bfdfe7dc352907fc980b868725387e985396210f46b7c55c20e08a645849aa92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815aef15bbad33bac1e529ed3c955ab22", "guid": "bfdfe7dc352907fc980b868725387e98f62e1d668d9755a74f2e72e8e1eb5cbb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ddb10daf8e57e0aa9859914b5b4dfb2", "guid": "bfdfe7dc352907fc980b868725387e9826ddbf371ab07489508e2029ad420da6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989581929ad650793f63dbf14b422e03e8", "guid": "bfdfe7dc352907fc980b868725387e98214c33ca0d18b1dfc4c0ac6890e44e69"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c5fab2545b98d9780212ae2f8d8d419", "guid": "bfdfe7dc352907fc980b868725387e987a047586eb455bc84ccbf1f359227ae2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d72026cf8ce15d00679e4252ae22108", "guid": "bfdfe7dc352907fc980b868725387e989e5df209ed46f08fe307c32b6a8b975f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809494349edda4e68d06531e939c23918", "guid": "bfdfe7dc352907fc980b868725387e98d28d8e1f88470aa4b25f02687ad8325a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f8a7b6bf19cc6201d83d4e68d5a4580", "guid": "bfdfe7dc352907fc980b868725387e98f9719f22ff90e6fb916db31d4e78a287", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98560a854e2455d308aebc0baad96a64bd", "guid": "bfdfe7dc352907fc980b868725387e9833a9096971e10c5da2a3bbe06b200b3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7f7c455a2e9ad7a2e44a572d523d1a2", "guid": "bfdfe7dc352907fc980b868725387e9827c31d9ba8546fe4e9c476755839735d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d38b7d71d1738a6eac1e4d27acbf1b07", "guid": "bfdfe7dc352907fc980b868725387e9858e1645724ef60e7126b19e4872b9d3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc486858ee74961e036590eb5c78b01a", "guid": "bfdfe7dc352907fc980b868725387e98ddd9a23934b9ffc3045ae0e88e8a9b52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4404c46c8d2fc80f3dad984626b19a0", "guid": "bfdfe7dc352907fc980b868725387e98f2eeb1915b5878db54716fb0d9c9a3fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984fe2310f579a99cdf897929f2910312d", "guid": "bfdfe7dc352907fc980b868725387e9819512dfa9a191fcee72c931500b9ae84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d5b308699919637530c55de37cf963d", "guid": "bfdfe7dc352907fc980b868725387e98fec5beac37b0501cab6cd53be1ab4ce7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867740ead147863f3e99b25c5f3ab7b0d", "guid": "bfdfe7dc352907fc980b868725387e98f53d50dc8a4d8ae6014de0db02b25880"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98103ead659f81fb71b7074de86c7c77ec", "guid": "bfdfe7dc352907fc980b868725387e984284462367623dfb6f50d0377e6e51b8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb4fbba0a757601658b807c72748880", "guid": "bfdfe7dc352907fc980b868725387e98a51b818d3a6c929628c7be4736451b6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c383b4b11c3a57cf3b51ea673e6b396", "guid": "bfdfe7dc352907fc980b868725387e986a5ce54f55d427ce03572740f1634d77", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a52198c074d80c7d0bdf99fb65cca60", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d44d40229809c63cb8e3267e8c719497", "guid": "bfdfe7dc352907fc980b868725387e98e02b7997930a2b6f4dda6978a35f088a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eaa558d7ccb8be0c928895935c3a0783", "guid": "bfdfe7dc352907fc980b868725387e989ba596b936fdab458d638e44d0cd5562"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb123d3d180f0f5ba160f2f61567d22c", "guid": "bfdfe7dc352907fc980b868725387e98358938028b35e6d37260d10775b1b728"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5ecfeb18d2ffe3bb2a0244b3ad1192e", "guid": "bfdfe7dc352907fc980b868725387e9851dcaf0dcc512ec908a6e45feb266313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db5dbbe73e75f46e5c6f83650f3ec697", "guid": "bfdfe7dc352907fc980b868725387e984f758c4d78589563d1f90b12cffdec65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98171106afd85d9dc0607062bdbc0ff58d", "guid": "bfdfe7dc352907fc980b868725387e98cfbd5c2d51c0dd1585d333299981ed54"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fd62dcbde31dd0bdb2684b445c5f018", "guid": "bfdfe7dc352907fc980b868725387e98a16e09096dcaff9f95564b7d9eca970f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc8d45421e8f6690f81482b21b4bbd5c", "guid": "bfdfe7dc352907fc980b868725387e98af88aeb347b45bfa8d0bd0d6b070dccc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a0c8a00e5296396772f351482ce2a9b", "guid": "bfdfe7dc352907fc980b868725387e98fefb33fe43c4eb0e2dc29d6f2122fba7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b329e546b3ff195c143a8ba089c0242", "guid": "bfdfe7dc352907fc980b868725387e98a6a4d44b1a011ad308bd567f85661c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c961be8bfb9b96a0bd22552fa5822b4d", "guid": "bfdfe7dc352907fc980b868725387e98b7b22a7113fc522a10d4dd2dd761e7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bfd8cf7f1929974111b81e4e6c19899", "guid": "bfdfe7dc352907fc980b868725387e98edce411c1ab446936d889d8d145e53cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7061e33b6ea22398016bde5e1f54f6e", "guid": "bfdfe7dc352907fc980b868725387e98497347e464679d3bc3fed2bad1598e16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850a0f69572d6c0fe9bc3e294b7e6e5bf", "guid": "bfdfe7dc352907fc980b868725387e985e5a7040e3315d97d7188c0c81225f52"}], "guid": "bfdfe7dc352907fc980b868725387e98bf99ac753bcb31f623291855e647b4e9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bdb0d13a7f91ea48c50166f907454443", "guid": "bfdfe7dc352907fc980b868725387e98d1e27510fee811f7b889cc3bec557a5b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04dcb6e648519c233ae8161727f1d09", "guid": "bfdfe7dc352907fc980b868725387e986b6c42287b63e4e8364995c60f74847c"}], "guid": "bfdfe7dc352907fc980b868725387e9864550fee738fec24a2058b4c9be790d1", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e982fbbc45fa7428e27b37ec870d24540d9", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e9899236a591e48613b11d288425468e1f3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}