// Status Enum
enum TaskStatus {
  working,
  open,
  pendingReview,
  overdue,
  template,
  completed,
  cancelled,
}

// Extension to get the string values if needed
extension TaskStatusExtension on TaskStatus {
  String get displayName {
    switch (this) {
      case TaskStatus.working:
        return 'Working';
      case TaskStatus.open:
        return 'Open';
      case TaskStatus.pendingReview:
        return 'Pending Review';
      case TaskStatus.overdue:
        return 'Overdue';
      case TaskStatus.template:
        return 'Template';
      case TaskStatus.completed:
        return 'Completed';
      case TaskStatus.cancelled:
        return 'Cancelled';
    }
  }
}

extension TaskStatusX on String {
  TaskStatus toTaskStatus() {
    switch (this) {
      case 'Working':
        return TaskStatus.working;
      case 'Open':
        return TaskStatus.open;
      case 'Pending Review':
        return TaskStatus.pendingReview;
      case 'Overdue':
        return TaskStatus.overdue;
      case 'Template':
        return TaskStatus.template;
      case 'Completed':
        return TaskStatus.completed;
      case 'Cancelled':
        return TaskStatus.cancelled;
      default:
        return TaskStatus.working;
    }
  }
}
