// ignore_for_file: unreachable_switch_default

enum ExpenseStatus {
  approved,
  cancelled,
  draft,
}

extension ExpenseStatusExtension on ExpenseStatus {
  String get name {
    switch (this) {
      case ExpenseStatus.approved:
        return 'Approved';
      case ExpenseStatus.cancelled:
        return 'Cancelled';
      case ExpenseStatus.draft:
        return 'Draft';
      default:
        return 'Unknown';
    }
  }
}

// convert string to ExpenseStatus
extension StringToExpenseStatusExtension on String {
  ExpenseStatus toExpenseStatus() {
    switch (this) {
      case 'Approved' || 'approved':
        return ExpenseStatus.approved;
      case 'Cancelled' || 'cancelled':
        return ExpenseStatus.cancelled;
      case 'Draft' || 'draft':
        return ExpenseStatus.draft;
      default:
        return ExpenseStatus.draft;
    }
  }
}
