// ignore_for_file: unreachable_switch_default

enum UserRolesEnum {
  admin,
  system_manager,
  user,
  employee,
  supervisor,
  hr_manager,
  accounts_manager,
  marketing,
  cashier,
  hod,
  doctor,
}

extension UserRolesEnumExtension on UserRolesEnum {
  String get name {
    switch (this) {
      case UserRolesEnum.admin:
        return 'Admin';
      case UserRolesEnum.system_manager:
        return 'System Manager';
      case UserRolesEnum.user:
        return 'User';
      case UserRolesEnum.employee:
        return 'Employee';
      case UserRolesEnum.supervisor:
        return 'Supervisor';
      case UserRolesEnum.hr_manager:
        return 'HR Manager';
      case UserRolesEnum.accounts_manager:
        return 'Accounts Manager';
      case UserRolesEnum.marketing:
        return 'Marketing';
      case UserRolesEnum.cashier:
        return 'Cashier';
      case UserRolesEnum.hod:
        return 'HOD';
      case UserRolesEnum.doctor:
        return 'Doctor';
      default:
        return '';
    }
  }
}

class UserRolesHelper {
  /// Returns true if the user has the role of admin
  bool isAdmin(List<String> userRoles) {
    return userRoles.contains(UserRolesEnum.admin.name) ||
        userRoles.contains(UserRolesEnum.system_manager.name);
  }

  /// Returns true if the user has the role of accounts manager or admin
  bool canShowApprovalButton(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.accounts_manager.name);
  }

  /// Returns true if the user has the role of hod or admin
  bool canViewAndCreateTasks(List<String> userRoles) {
    // bool canManageTasks(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.hod.name);
  }

  /// Returns true if the user has the role of accounts manager or hr manager or admin
  bool canViewAllPayrolls(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.accounts_manager.name) ||
            userRoles.contains(UserRolesEnum.hr_manager.name);
  }

  /// Returns true if the user has the role of marketing or admin
  bool canViewAllVisits(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.marketing.name);
  }

  /// Returns true if the user has the role of cashier or admin
  bool canViewOrdersAndPayments(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.cashier.name);
  }

  /// Returns true if the user has the role of hr manager or admin
  bool canViewAllLeaves(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.hr_manager.name);
  }

  /// Returns true if the user has the role of accounts manager or admin
  bool canViewAllExpenses(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.accounts_manager.name);
  }

  /// Returns true if the user has the role of marketing or admin
  bool canAssignIssues(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.hr_manager.name);
  }

  /// Returns true if the user has the role of hr manager or admin
  bool canViewAllAttendance(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.hr_manager.name);
  }

  bool canViewAppointments(List<String> userRoles) {
    return
        // isAdmin(userRoles) ||
        userRoles.contains(UserRolesEnum.doctor.name);
  }
}
