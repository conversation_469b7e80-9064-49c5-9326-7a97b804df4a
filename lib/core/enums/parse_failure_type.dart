// ignore_for_file: unreachable_switch_default

enum ParsingFailureType {
  jsonParsingError,
  xmlParsingError,
  typeConversionError,
  unknown,
}

/// Extension for ParsingFailureType to provide user-friendly messages
extension ParsingFailureTypeEx on ParsingFailureType {
  String getErrorMessage() {
    switch (this) {
      case ParsingFailureType.jsonParsingError:
        return 'Failed to parse JSON data. Please check the data format.';
      case ParsingFailureType.xmlParsingError:
        return 'Failed to parse XML data. Please check the data format.';
      case ParsingFailureType.typeConversionError:
        return 'Failed to convert data to the expected type. Please check the data format.';
      case ParsingFailureType.unknown:
      default:
        return 'An unknown parsing error occurred. Please check the data format.';
    }
  }
}
