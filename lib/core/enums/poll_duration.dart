enum PollDuration {
  oneDay,
  twoDays,
  threeDays,
  fiveDays,
  sevenDays,
}

extension PollDurationExtension on PollDuration {
  int get inMinutes {
    switch (this) {
      case PollDuration.oneDay:
        return 1 * 24 * 60;
      case PollDuration.twoDays:
        return 2 * 24 * 60;
      case PollDuration.threeDays:
        return 3 * 24 * 60;
      case PollDuration.fiveDays:
        return 5 * 24 * 60;
      case PollDuration.sevenDays:
        return 7 * 24 * 60;
    }
  }

  /// Optional: user-friendly label
  String get name {
    switch (this) {
      case PollDuration.oneDay:
        return "1 Day";
      case PollDuration.twoDays:
        return "2 Days";
      case PollDuration.threeDays:
        return "3 Days";
      case PollDuration.fiveDays:
        return "5 Days";
      case PollDuration.sevenDays:
        return "7 Days";
    }
  }
}
