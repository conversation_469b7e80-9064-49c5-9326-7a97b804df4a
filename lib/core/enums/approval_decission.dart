enum ApprovalDecision {
  approve,
  reject;
}

extension ApprovalDecisionExtension on ApprovalDecision {
  String get expenseApprovalDecision {
    switch (this) {
      case ApprovalDecision.approve:
        return 'Approved';
      case ApprovalDecision.reject:
        return 'Rejected';
    }
  }

  String get leaveApprovalDecision {
    switch (this) {
      case ApprovalDecision.approve:
        return 'Approved';
      case ApprovalDecision.reject:
        return 'Rejected';
    }
  }

  String get salesInvoiceApprovalDecision {
    switch (this) {
      case ApprovalDecision.approve:
        return 'approve';
      case ApprovalDecision.reject:
        return 'reject';
    }
  }
}
