enum EncounterType {
  consultation,
  followUp,
  emergency,
  routine;

  String get displayName {
    switch (this) {
      case EncounterType.consultation:
        return 'Consultation';
      case EncounterType.followUp:
        return 'Follow Up';
      case EncounterType.emergency:
        return 'Emergency';
      case EncounterType.routine:
        return 'Routine';
    }
  }
}

enum TestPriority {
  urgent,
  stat,
  normal,
  routine;

  String get displayName {
    switch (this) {
      case TestPriority.urgent:
        return 'Urgent';
      case TestPriority.stat:
        return 'STAT';
      case TestPriority.normal:
        return 'Normal';
      case TestPriority.routine:
        return 'Routine';
    }
  }
}