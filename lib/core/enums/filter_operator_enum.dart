enum FilterOperator {
  equals,
  notEquals,
  greaterThan,
  lessThan,
}

extension FilterOperatorExtension on FilterOperator {
  String get displayName {
    switch (this) {
      case FilterOperator.equals:
        return 'Equals to';
      case FilterOperator.notEquals:
        return 'Not Equals to';
      case FilterOperator.greaterThan:
        return 'Greater than';
      case FilterOperator.lessThan:
        return 'Less than';
    }
  }

  // Optionally, you can add more dynamic behavior if needed
  String get symbol {
    switch (this) {
      case FilterOperator.equals:
        return '=';
      case FilterOperator.notEquals:
        return '!=';
      case FilterOperator.greaterThan:
        return '>';
      case FilterOperator.lessThan:
        return '<';
    }
  }
}
