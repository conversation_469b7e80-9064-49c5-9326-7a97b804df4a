part of 'visit_bloc.dart';

abstract class VisitState {}

class VisitInitialState extends VisitState {}

class VisitLoadingState extends VisitState {}

class VisitCreationLoadingState extends VisitState {}

class VisitTypeLoadingState extends VisitState {}

class VisitLoadedState extends VisitState {
  final List<VisitModel> visits;

  VisitLoadedState({required this.visits});
}

class VisitTypeLoadedState extends VisitState {
  final List<VisitTypeModel> visitTypes;

  VisitTypeLoadedState({required this.visitTypes});
}

class VisitSuccessState extends VisitState {
  final String message;

  VisitSuccessState({required this.message});
}

class VisitTypeSuccessState extends VisitState {
  final String message;

  VisitTypeSuccessState({required this.message});
}

class VisitErrorState extends VisitState {
  final AppFailure appFailure;

  VisitErrorState({
    required this.appFailure,
  });
}

class VisitTypeErrorState extends VisitState {
  final AppFailure appFailure;

  VisitTypeErrorState({
    required this.appFailure,
  });
}
