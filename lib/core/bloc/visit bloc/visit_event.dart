part of 'visit_bloc.dart';

abstract class VisitEvent {}

class CreateVisitEvent extends VisitEvent {
  final String visitType;
  final String description;

  CreateVisitEvent({
    required this.visitType,
    required this.description,
  });
}

class GetAllVisitsEvent extends VisitEvent {
  final String employee;
  final List<String> userRoles;

  GetAllVisitsEvent({required this.employee, required this.userRoles});
}

class GetAllVisitTypesEvent extends VisitEvent {}
