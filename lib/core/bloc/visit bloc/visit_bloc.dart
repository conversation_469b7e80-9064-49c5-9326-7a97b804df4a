import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/visit_model.dart';
import 'package:rasiin_tasks_app/core/models/visit_type_model.dart';
import 'package:rasiin_tasks_app/core/repository/visit_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'visit_event.dart';
part 'visit_state.dart';

class VisitBloc extends Bloc<VisitEvent, VisitState> {
  final VisitRepository visitRepository;

  VisitBloc({required this.visitRepository}) : super(VisitInitialState()) {
    on<CreateVisitEvent>(
      _onCreateVisitEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
    on<GetAllVisitsEvent>(
      _onGetAllVisitsEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
    on<GetAllVisitTypesEvent>(
      _onGetAllVisitTypesEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
  }

  List<VisitModel> _allVisits = [];
  List<VisitModel> _filteredVisits = [];
  List<VisitModel> get visits => _filteredVisits;

  List<VisitTypeModel> _allVisitTypes = [];
  List<VisitTypeModel> _filteredVisitTypes = [];
  List<VisitTypeModel> get visitTypes => _filteredVisitTypes;

  // Handle visit errors and emit appropriate state
  void _emitVisitError(AppFailure failure, Emitter<VisitState> emit) {
    emit(VisitErrorState(
      appFailure: failure,
    ));
  }

  // Handle visit type errors and emit appropriate state
  void _emitVisitTypeError(AppFailure failure, Emitter<VisitState> emit) {
    emit(VisitTypeErrorState(
      appFailure: failure,
    ));
  }

  // Emit loaded state for visits
  void _emitVisitsLoaded(List<VisitModel> visits, Emitter<VisitState> emit) {
    _allVisits = visits;
    _filteredVisits = List.from(_allVisits);
    emit(VisitLoadedState(visits: _filteredVisits));
  }

  // Emit loaded state for visit types
  void _emitVisitTypesLoaded(
      List<VisitTypeModel> visitTypes, Emitter<VisitState> emit) {
    _allVisitTypes = visitTypes;
    _filteredVisitTypes = List.from(_allVisitTypes);
    emit(VisitTypeLoadedState(visitTypes: _filteredVisitTypes));
  }

  FutureOr<void> _onCreateVisitEvent(
      CreateVisitEvent event, Emitter<VisitState> emit) async {
    emit(VisitCreationLoadingState());
    final response = await visitRepository.createVisit(
      visitType: event.visitType,
      description: event.description,
    );
    await response.fold(
      (failure) {
        _emitVisitError(failure, emit);
      },
      (success) {
        emit(VisitSuccessState(message: success));
      },
    );
  }

  FutureOr<void> _onGetAllVisitsEvent(
      GetAllVisitsEvent event, Emitter<VisitState> emit) async {
    emit(VisitLoadingState());
    final response = await visitRepository.getAllVisits(
      employee: event.employee,
      userRoles: event.userRoles,
    );
    await response.fold(
      (failure) {
        _allVisits.clear();
        _emitVisitError(failure, emit);
      },
      (success) {
        _emitVisitsLoaded(success, emit);
      },
    );
  }

  FutureOr<void> _onGetAllVisitTypesEvent(
      GetAllVisitTypesEvent event, Emitter<VisitState> emit) async {
    emit(VisitTypeLoadingState());
    final response = await visitRepository.getAllVisitTypes();
    await response.fold(
      (failure) {
        _allVisitTypes.clear();
        _emitVisitTypeError(failure, emit);
      },
      (success) {
        _emitVisitTypesLoaded(success, emit);
      },
    );
  }
}
