part of 'authentication_bloc.dart';

abstract class AuthenticationState {}

class AuthenticationStateInitial extends AuthenticationState {}

class AuthenticationStateLoading extends AuthenticationState {}

class AuthenticationStateChecking extends AuthenticationState {}

class AuthenticationStateError extends AuthenticationState {
  final AppFailure appFailure;

  AuthenticationStateError({
    required this.appFailure,
  });
}

class AuthenticationStateAuthenticated extends AuthenticationState {}
