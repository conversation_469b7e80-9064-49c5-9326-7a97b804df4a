import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/repository/auth_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'authentication_event.dart';
part 'authentication_state.dart';

class AuthenticationBloc
    extends Bloc<AuthenticationEvent, AuthenticationState> {
  final AuthRepository authRepository;

  AuthenticationBloc({
    required this.authRepository,
  }) : super(AuthenticationStateInitial()) {
    on<AuthenticationLoginEvent>(
      _onAuthenticationLoginEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AuthenticationLogoutEvent>(
      _onAuthenticationLogoutEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
    on<AuthenticationCheckEvent>(
      _onAuthenticationCheckEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
  }

  //
  final GlobalKey<FormState> loginFormKey = GlobalKey<FormState>();
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  @override
  Future<void> close() {
    usernameController.dispose();
    passwordController.dispose();
    return super.close();
  }

  void _clearField() {
    usernameController.clear();
    passwordController.clear();
  }

  void _handleError(AppFailure error, Emitter<AuthenticationState> emit) {
    emit(AuthenticationStateError(appFailure: error));
  }

  //
  FutureOr<void> _onAuthenticationLoginEvent(
      AuthenticationLoginEvent event, Emitter<AuthenticationState> emit) async {
    emit(AuthenticationStateLoading());

    final response = await authRepository.login(
      username: usernameController.text,
      password: passwordController.text,
    );

    response.fold(
      (left) {
        _handleError(left, emit);
      },
      (right) {
        // currentUser = right;
        _clearField();
        emit(AuthenticationStateAuthenticated());
      },
    );
  }

  FutureOr<void> _onAuthenticationLogoutEvent(AuthenticationLogoutEvent event,
      Emitter<AuthenticationState> emit) async {
    emit(AuthenticationStateLoading());
    final response = await authRepository.logoutUser();
    response.fold(
      (left) {
        // _handleError(left, emit);
        emit(AuthenticationStateInitial());
      },
      (right) {
        emit(AuthenticationStateInitial());
      },
    );
  }

  FutureOr<void> _onAuthenticationCheckEvent(
      AuthenticationCheckEvent event, Emitter<AuthenticationState> emit) async {
    emit(AuthenticationStateChecking());
    final response = await authRepository.checkUser();
    if (response) {
      emit(AuthenticationStateAuthenticated());
    } else {
      emit(AuthenticationStateInitial());
    }
  }
}
