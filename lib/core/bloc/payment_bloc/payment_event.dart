
part of 'payment_bloc.dart';

abstract class PaymentEvent extends Equatable {
  const PaymentEvent();

  @override
  List<Object?> get props => [];
}

class GetAllPaymentsEvent extends PaymentEvent {
  final List<String> userRoles;

  const GetAllPaymentsEvent({
    required this.userRoles,
  });

  @override
  List<Object?> get props => [userRoles];
}

class CreatePaymentEvent extends PaymentEvent {
  final PaymentModel payment;

  const CreatePaymentEvent(this.payment);

  @override
  List<Object?> get props => [payment];
}
