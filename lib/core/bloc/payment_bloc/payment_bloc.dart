import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/payment_model.dart';
import 'package:rasiin_tasks_app/core/repository/payment_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'payment_event.dart';
part 'payment_state.dart';

class PaymentBloc extends Bloc<PaymentEvent, PaymentState> {
  final PaymentRepository paymentRepository;

  PaymentBloc({
    required this.paymentRepository,
  }) : super(PaymentInitial()) {
    on<GetAllPaymentsEvent>(
      _onGetAllPayments,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
    on<CreatePaymentEvent>(
      _onCreatePayment,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
  }

  List<PaymentModel> _payments = [];
  List<PaymentModel> get payments => _payments;

  Future<void> _onGetAllPayments(
    GetAllPaymentsEvent event,
    Emitter<PaymentState> emit,
  ) async {
    emit(PaymentLoading());

    final result = await paymentRepository.getAllPayments(
      userRoles: event.userRoles,
    );

    result.fold(
      (failure) {
        _payments.clear();
        emit(PaymentFailure(failure));
      },
      (payments) {
        _payments = payments;
        print('Payments: ${payments.length}');
        emit(PaymentsLoaded(payments));
      },
    );
  }

  Future<void> _onCreatePayment(
    CreatePaymentEvent event,
    Emitter<PaymentState> emit,
  ) async {
    emit(PaymentCreationLoading());

    final result = await paymentRepository.createPayment(
      payment: event.payment,
    );

    result.fold(
      (failure) => emit(PaymentFailure(failure)),
      (message) => emit(PaymentCreatedSuccess(message)),
    );
  }
}
