import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/appointment_model.dart';
import 'package:rasiin_tasks_app/core/repository/appointment_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'appointment_event.dart';
part 'appointment_state.dart';

class AppointmentBloc extends Bloc<AppointmentEvent, AppointmentState> {
  final AppointmentRepository appointmentRepository;

  AppointmentBloc({required this.appointmentRepository})
      : super(AppointmentInitial()) {
    on<LoadAppointmentsEvent>(
      _onLoadAppointmentsEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<AppointmentModel> _appointments = [];
  List<AppointmentModel> get appointments => _appointments;

  FutureOr<void> _onLoadAppointmentsEvent(
      LoadAppointmentsEvent event, Emitter<AppointmentState> emit) async {
    emit(AppointmentLoading());
    
    final result = await appointmentRepository.getDoctorAppointments(
      doctorIdentifier: event.doctorIdentifier,
    );

    result.fold(
      (failure) {
        _appointments.clear();
        emit(AppointmentError(appFailure: failure));
      },
      (appointmentsList) {
        _appointments = appointmentsList;
        emit(AppointmentLoaded(appointments: appointmentsList));
      },
    );
  }
}