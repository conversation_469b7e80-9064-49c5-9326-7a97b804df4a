part of 'dialog_cubit.dart';

abstract class DialogState extends Equatable {
  const DialogState();

  @override
  List<Object?> get props => [];
}

class DialogClosed extends DialogState {}

class DialogOpened extends DialogState {
  final String title;
  final String message;
  final DialogType dialogType;
  final String? confirmButtonText;
  final String? cancelButtonText;
  final bool barrierDismissible;
  final bool disableBackBtn;
  final bool enableAutoClose;
  final Duration? autoCloseDuration;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool? showConfirmBtn;
  final bool? showCancelBtn;
  final double? cancelButtonWidth;
  final double? confirmButtonWidth;
  final double? cancelButtonHeight;
  final double? confirmButtonHeight;

  const DialogOpened({
    required this.title,
    required this.message,
    required this.dialogType,
    this.confirmButtonText,
    this.cancelButtonText,
    this.barrierDismissible = true,
    this.autoCloseDuration,
    this.enableAutoClose = false,
    required this.onConfirm,
    required this.onCancel,
    this.showConfirmBtn = true,
    this.showCancelBtn = true,
    this.disableBackBtn = false,
    this.cancelButtonWidth,
    this.confirmButtonWidth,
    this.cancelButtonHeight,
    this.confirmButtonHeight,
  });

  @override
  List<Object?> get props => [
        title,
        message,
        dialogType,
        confirmButtonText,
        cancelButtonText,
        barrierDismissible,
        autoCloseDuration,
        onConfirm,
        onCancel,
        showConfirmBtn,
        showCancelBtn,
        disableBackBtn,
        cancelButtonWidth,
        confirmButtonWidth,
        cancelButtonHeight,
        confirmButtonHeight,
      ];
}
