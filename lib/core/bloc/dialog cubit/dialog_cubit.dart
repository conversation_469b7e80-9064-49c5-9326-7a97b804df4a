import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/enums/dialog_type.dart';

part 'dialog_cubit_state.dart';

class DialogCubit extends Cubit<DialogState> {
  Timer? autoCloseTimer;

  DialogCubit() : super(DialogClosed());

  @override
  Future<void> close() {
    autoCloseTimer?.cancel();
    autoCloseTimer = null;
    return super.close();
  }

  /// Open a dialog
  void showDialog({
    required String title,
    required String message,
    required DialogType dialogType,
    String? confirmButtonText,
    String? cancelButtonText,
    bool barrierDismissible = true,
    bool enableAutoClose = false,
    required Duration autoCloseDuration,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    required double? confirmButtonwidth,
    required double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    /// Close any existing dialog before opening a new one
    closeDialog();

    /// Reset Timer
    autoCloseTimer?.cancel();
    autoCloseTimer = null;

    emit(DialogOpened(
      title: title,
      message: message,
      dialogType: dialogType,
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      barrierDismissible: barrierDismissible,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      onConfirm: onConfirm,
      onCancel: onCancel,
      cancelButtonWidth: cancelButtonwidth,
      cancelButtonHeight: cancelButtonHeight ?? 30,
      confirmButtonWidth: confirmButtonwidth,
      confirmButtonHeight: confirmButtonHeight ?? 30,
    ));

    /// Handle auto-close
    if (enableAutoClose) {
      autoCloseTimer = Timer(autoCloseDuration, () {
        closeDialog();
      });
    }
  }

  /// Close the dialog
  void closeDialog() {
    if (state is DialogOpened) {
      autoCloseTimer?.cancel();
      autoCloseTimer = null;

      /// Only emit state if dialog is currently open
      if (state is! DialogClosed) {
        emit(DialogClosed());
      }
    }
  }

  // 🔹 Helper Methods for Common Dialogs 🔹

  /// Show Loading Dialog
  void showLoadingDialog({
    String title = "Processing",
    String message = "Please wait...",
    bool barrierDismissible = false,
    Duration autoCloseDuration = const Duration(seconds: 60),
    bool enableAutoClose = false,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.loading,
      barrierDismissible: barrierDismissible,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }

  /// Show Success Dialog
  void showSuccessDialog({
    String title = "Success 🎉",
    String message = "Operation completed successfully.",
    String confirmButtonText = "OK",
    bool barrierDismissible = true,
    VoidCallback? onConfirm,
    Duration autoCloseDuration = const Duration(seconds: 10),
    bool enableAutoClose = true,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.success,
      confirmButtonText: confirmButtonText,
      barrierDismissible: barrierDismissible,
      onConfirm: onConfirm,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }

  /// Show Error Dialog
  void showErrorDialog({
    String title = "Error 😱",
    required String message,
    String confirmButtonText = "OK",
    VoidCallback? onConfirm,
    bool barrierDismissible = true,
    Duration autoCloseDuration = const Duration(seconds: 10),
    bool enableAutoClose = true,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.error,
      confirmButtonText: confirmButtonText,
      barrierDismissible: barrierDismissible,
      onConfirm: onConfirm,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }

  /// Show Confirmation Dialog
  void showConfirmDialog({
    required String title,
    required String message,
    String confirmButtonText = "OK",
    String cancelButtonText = "Cancel",
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = false,
    Duration autoCloseDuration = const Duration(seconds: 10),
    bool enableAutoClose = true,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.confirmation,
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      barrierDismissible: barrierDismissible,
      onConfirm: onConfirm,
      onCancel: onCancel,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }

  /// Show Information Dialog
  void showInfoDialog({
    String title = "Information",
    required String message,
    String confirmButtonText = "OK",
    String? cancelButtonText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
    Duration autoCloseDuration = const Duration(seconds: 10),
    bool enableAutoClose = true,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.info,
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      barrierDismissible: barrierDismissible,
      onConfirm: onConfirm,
      onCancel: onCancel,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }

  /// Show Warning Dialog
  void showWarningDialog({
    String title = "Warning ⚠️",
    required String message,
    String confirmButtonText = "OK",
    String cancelButtonText = "Cancel",
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
    Duration autoCloseDuration = const Duration(seconds: 10),
    bool enableAutoClose = true,
    double? confirmButtonwidth,
    double? cancelButtonwidth,
    double? cancelButtonHeight,
    double? confirmButtonHeight,
  }) {
    showDialog(
      title: title,
      message: message,
      dialogType: DialogType.warning,
      confirmButtonText: confirmButtonText,
      cancelButtonText: cancelButtonText,
      barrierDismissible: barrierDismissible,
      onConfirm: onConfirm,
      onCancel: onCancel,
      autoCloseDuration: autoCloseDuration,
      enableAutoClose: enableAutoClose,
      cancelButtonwidth: cancelButtonwidth,
      confirmButtonwidth: confirmButtonwidth,
      cancelButtonHeight: cancelButtonHeight,
      confirmButtonHeight: confirmButtonHeight,
    );
  }
}
