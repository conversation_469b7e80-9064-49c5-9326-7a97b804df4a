part of 'issue_bloc.dart';

abstract class IssueEvent {}

class GetAllIssueEvent extends IssueEvent {}

class GetAllIssueTypesEvent extends IssueEvent {}

class CreateIssueEvent extends IssueEvent {
  final String subject;
  final String raisedBy;
  final String priority;
  final String issueType;
  final String decription;

  CreateIssueEvent({
    required this.subject,
    required this.raisedBy,
    required this.priority,
    required this.issueType,
    required this.decription,
  });
}

class AssignIssueEvent extends IssueEvent {
  //
  final String issueId;
  final String issueType;
  final List<String> usersEmail;
  final String description;
  final String assignedDate;
  final String assignedBy;

  AssignIssueEvent({
    required this.issueId,
    required this.issueType,
    required this.usersEmail,
    required this.description,
    required this.assignedDate,
    required this.assignedBy,
  });
}

class ChangeIssueStatus extends IssueEvent {
  final String issueId;
  final String issueStatus;

  ChangeIssueStatus({
    required this.issueId,
    required this.issueStatus,
  });
}

class GetIssueCommentsEvent extends IssueEvent {
  final String issueId;

  GetIssueCommentsEvent({required this.issueId});
}

class AddIssueCommentEvent extends IssueEvent {
  final String issueId;
  final String comment;
  final String userEmail;

  AddIssueCommentEvent({
    required this.issueId,
    required this.comment,
    required this.userEmail,
  });
}
