import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_type_model.dart';
import 'package:rasiin_tasks_app/core/repository/issue_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'issue_event.dart';
part 'issue_state.dart';

class IssueBloc extends Bloc<IssueEvent, IssueState> {
  final IssueRepository issueRepository;
  IssueBloc({
    required this.issueRepository,
  }) : super(IssueStateInitial()) {
    on<GetAllIssueEvent>(
      _onGetAllIssueEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllIssueTypesEvent>(
      _onGetAllIssueTypesEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<CreateIssueEvent>(
      _onCreateIssueEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AssignIssueEvent>(
      _onAssignIssueEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ChangeIssueStatus>(
      _onChangeIssueStatus,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetIssueCommentsEvent>(
      _onGetIssueCommentsEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AddIssueCommentEvent>(
      _onAddIssueCommentEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<IssueModel> issues = [];

  List<IssueTypeModel> _allIssueTypes = [];
  List<IssueTypeModel> get issueTypes => _allIssueTypes;

  List<CommentModel> _allIssueComments = [];
  List<CommentModel> get issueComments => _allIssueComments;

  _handleCommonError(AppFailure apiFailure, Emitter<IssueState> emit) {
    emit(IssueStateError(
      appFailure: apiFailure,
    ));
  }

  FutureOr<void> _onCreateIssueEvent(
      CreateIssueEvent event, Emitter<IssueState> emit) async {
    emit(IssueCreationStateLoading());
    final resposne = await issueRepository.createIssue(
      subject: event.subject,
      raisedBy: event.raisedBy,
      priority: event.priority,
      issueType: event.issueType,
      desc: event.decription,
    );
    resposne.fold(
      (appFailure) {
        //
        emit(IssueCreationStateError(appFailure: appFailure));
      },
      (right) {
        //
        emit(IssueCreationStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onAssignIssueEvent(
      AssignIssueEvent event, Emitter<IssueState> emit) async {
    emit(IssueStateLoading());

    final response = await issueRepository.assignIssue(
      issueId: event.issueId,
      issueType: event.issueType,
      usersEmail: event.usersEmail,
      description: event.description,
      assignedDate: event.assignedDate,
      assignedBy: event.assignedBy,
    );
    response.fold(
      (left) {
        //
        _handleCommonError(left, emit);
      },
      (right) {
        //
        emit(IssueStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onGetAllIssueEvent(
      GetAllIssueEvent event, Emitter<IssueState> emit) async {
    emit(IssueStateIssuesLoading());
    final response = await issueRepository.getAllIssues();
    response.fold(
      (left) {
        //
        issues.clear();
        _handleCommonError(left, emit);
      },
      (right) {
        issues = right;
        emit(IssueStateIssuesLoaded(issues: issues));
      },
    );
  }

  FutureOr<void> _onGetAllIssueTypesEvent(
      GetAllIssueTypesEvent event, Emitter<IssueState> emit) async {
    emit(IssueStateIssueTypesLoading());
    final response = await issueRepository.getAllIssueTypes();
    response.fold(
      (appFailure) {
        //
        _allIssueTypes.clear();
        emit(IssueStateIssuesTypeError(appFailure: appFailure));
      },
      (comingIssueTypes) {
        //
        _allIssueTypes = comingIssueTypes;
        emit(IssueStateIssueTypesLoaded(issueTypes: _allIssueTypes));
      },
    );
  }

  FutureOr<void> _onChangeIssueStatus(
      ChangeIssueStatus event, Emitter<IssueState> emit) async {
    emit(IssueStateLoading());
    final response = await issueRepository.changeIssueStatus(
      issueId: event.issueId,
      issueStatus: event.issueStatus,
    );
    response.fold(
      (left) {
        //
        _handleCommonError(left, emit);
      },
      (right) {
        //
        emit(IssueStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onGetIssueCommentsEvent(
      GetIssueCommentsEvent event, Emitter<IssueState> emit) async {
    emit(IssueCommentsStateLoading());
    final response = await issueRepository.getIssueComments(
      issueId: event.issueId,
    );
    response.fold(
      (left) {
        //
        _allIssueComments.clear();
        emit(IssueCommentsStateError(appFailure: left));
      },
      (right) {
        //
        _allIssueComments = right;
        emit(IssueCommentsStateLoaded(comments: _allIssueComments));
      },
    );
  }

  FutureOr<void> _onAddIssueCommentEvent(
      AddIssueCommentEvent event, Emitter<IssueState> emit) async {
    emit(IssueCommentCreationStateLoading());
    final response = await issueRepository.addIssueComment(
      issueId: event.issueId,
      comment: event.comment,
      userEmail: event.userEmail,
    );
    response.fold(
      (left) {
        //
        emit(IssueCommentCreationStateError(appFailure: left));
      },
      (right) {
        //
        emit(IssueCommentCreationStateSuccess(message: right));
      },
    );
  }
}
