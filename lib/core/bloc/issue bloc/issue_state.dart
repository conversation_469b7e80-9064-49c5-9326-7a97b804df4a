part of 'issue_bloc.dart';

abstract class IssueState extends Equatable {
  @override
  List<Object?> get props => [];
}

class IssueStateInitial extends IssueState {}

class IssueStateLoading extends IssueState {}

class IssueStateIssuesLoading extends IssueState {}

class IssueStateIssueTypesLoading extends IssueState {}

class IssueStateProjectLoading extends IssueState {}

class IssueCreationStateLoading extends IssueState {}

class IssueStateIssuesLoaded extends IssueState {
  final List<IssueModel> issues;

  IssueStateIssuesLoaded({required this.issues});

  @override
  List<Object?> get props => [issues];
}

class IssueStateIssueTypesLoaded extends IssueState {
  final List<IssueTypeModel> issueTypes;

  IssueStateIssueTypesLoaded({required this.issueTypes});

  @override
  List<Object?> get props => [issueTypes];
}

class IssueStateError extends IssueState {
  final AppFailure appFailure;

  IssueStateError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class IssueStateIssuesTypeError extends IssueState {
  final AppFailure appFailure;

  IssueStateIssuesTypeError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class IssueCreationStateError extends IssueState {
  final AppFailure appFailure;

  IssueCreationStateError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class IssueStateSuccess extends IssueState {
  final String message;

  IssueStateSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class IssueCreationStateSuccess extends IssueState {
  final String message;

  IssueCreationStateSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class IssueCommentsStateLoading extends IssueState {}

class IssueCommentsStateLoaded extends IssueState {
  final List<CommentModel> comments;

  IssueCommentsStateLoaded({required this.comments});

  @override
  List<Object?> get props => [comments];
}

class IssueCommentsStateError extends IssueState {
  final AppFailure appFailure;

  IssueCommentsStateError({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

class IssueCommentCreationStateLoading extends IssueState {}

class IssueCommentCreationStateSuccess extends IssueState {
  final String message;

  IssueCommentCreationStateSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class IssueCommentCreationStateError extends IssueState {
  final AppFailure appFailure;

  IssueCommentCreationStateError({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}
