part of 'users_bloc.dart';

abstract class UsersState extends Equatable {
  const UsersState();

  @override
  List<Object?> get props => [];
}

class UsersStateInitial extends UsersState {}

class UsersStateLoading extends UsersState {}

class UsersStateDataLoading extends UsersState {}

class UsersStateDataLoaded extends UsersState {
  final UserModel? user;

  UsersStateDataLoaded({required this.user});

  @override
  List<Object?> get props => [user];
}

class UsersStateDataError extends UsersState {
  final AppFailure appFailure;

  UsersStateDataError({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

class UsersStateProfileImageFetched extends UsersState {
  final String profileImage;

  UsersStateProfileImageFetched({required this.profileImage});
}

class UsersStateProfileImageUpdated extends UsersState {
  final String profileImage;
  final String message;

  UsersStateProfileImageUpdated({
    required this.profileImage,
    required this.message,
  });

  @override
  List<Object?> get props => [profileImage, message];
}

class UsersStateProfileImageError extends UsersState {
  final String message;

  UsersStateProfileImageError({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class UsersStateProfileImagePicked extends UsersState {}

class UsersStateUsersLoaded extends UsersState {
  final List<UserModel> users;

  UsersStateUsersLoaded({required this.users});

  @override
  List<Object?> get props => [users];
}

class UsersStateAllUserLoaded extends UsersState {
  final List<AllUsersModel> users;

  UsersStateAllUserLoaded({required this.users});

  @override
  List<Object?> get props => [users];
}

class UsersStateAllUserLoading extends UsersState {}

class UsersStateAllUserError extends UsersState {
  final AppFailure appFailure;

  UsersStateAllUserError({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

class UsersStateError extends UsersState {
  final AppFailure appFailure;

  UsersStateError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class UsersStateUsersSuccess extends UsersState {
  final String message;

  UsersStateUsersSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

///

class ThemeModedLoaded extends UsersState {}

class ThemeModeUpdated extends UsersState {
  final ThemeMode themeMode;

  ThemeModeUpdated({required this.themeMode});

  @override
  List<Object?> get props => [themeMode];
}

// dashboard states
class UsersStateDashboardLoading extends UsersState {}

class UsersStateDashboardLoaded extends UsersState {
  final DashboardModel dashboard;

  UsersStateDashboardLoaded({required this.dashboard});

  @override
  List<Object?> get props => [dashboard];
}

class UsersStateDashboardError extends UsersState {
  final AppFailure appFailure;

  UsersStateDashboardError({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}
