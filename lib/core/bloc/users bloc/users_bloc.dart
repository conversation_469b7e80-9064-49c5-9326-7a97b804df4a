import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/core/repository/users_repository.dart';
import 'package:rasiin_tasks_app/core/services/flutter_secure_storage_services.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

import '../../models/dashboard_model.dart';

part 'users_event.dart';
part 'users_state.dart';

class UsersBloc extends Bloc<UsersEvent, UsersState> {
  final UsersRepository usersRepository;
  final FlutterSecureStorageServices flutterSecureStorageServices;
  //
  ThemeMode currentThemeMode = ThemeMode.light; // Default theme mode
  UsersBloc({
    required this.usersRepository,
    required this.flutterSecureStorageServices,
  }) : super(UsersStateInitial()) {
    on<GetAuthUserEvent>(
      _onGetAuthUserEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllUsersEvent>(
      _onGetAllUsersEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ChangeUserPasswordEvent>(
      _onChangeUserPasswordEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<PickProfileImageEvent>(
      _onPickProfileImageEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ChangeProfileImageEvent>(
      _onChangeProfileImageEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetProfileImageEvent>(
      _onGetProfileImageEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );

    on<GetDashboardEvent>(
      _onGetDashboardEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );

    // theme
    on<LoadUserThemeEvent>(_onLoadUserThemeEvent);
    on<ToggleUserThemeEvent>(
      _onToggleUserThemeEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );

    add(LoadUserThemeEvent());
    add(GetDashboardEvent());
  }

  List<AllUsersModel> allUsers = [];
  XFile? profileImage;

  UserModel? currentUser;

  DashboardModel? _dashboard;
  DashboardModel? get dashboardData => _dashboard;

  //
  _handlerError(AppFailure appFailure, Emitter<UsersState> emit) {
    emit(UsersStateError(
      appFailure: appFailure,
    ));
  }

  FutureOr<void> _onPickProfileImageEvent(
      PickProfileImageEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateLoading());
    try {
      final ImagePicker _picker = ImagePicker();
      final XFile? pickedFile =
          await _picker.pickImage(source: event.imageSource);
      if (pickedFile != null) {
        profileImage = pickedFile;
        emit(UsersStateProfileImagePicked());
        return;
      } else {
        emit(UsersStateProfileImageError(message: 'No image selected'));
      }
    } catch (error) {
      emit(UsersStateProfileImageError(
          message: "Error Picking profile image : ${error.toString()}"));
    }
  }

  FutureOr<void> _onGetProfileImageEvent(
      GetProfileImageEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateLoading());
    final response =
        await usersRepository.getProfileImage(employeeId: event.employeeId);
    await response.fold(
      (left) {
        //
        _handlerError(left, emit);
      },
      (right) async {
        print("right is : $right");
        // Update the currentUser's profile image
        currentUser?.profileImage = right;

        // Emit success with the updated user data
        // emit(UsersStateDataLoaded(user: currentUser));
        emit(UsersStateProfileImageFetched(
          profileImage: right,
        ));
      },
    );
  }

  FutureOr<void> _onChangeProfileImageEvent(
      ChangeProfileImageEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateLoading());
    if (profileImage == null) {
      emit(UsersStateProfileImageError(
        message: "Please Pick Image To Update Your profile picture",
      ));
      return;
    }

    final response = await usersRepository.changeProfileImage(
      employeeId: event.employeeId,
      profileImage: profileImage!,
    );

    response.fold(
      (left) {
        _handlerError(left, emit);
        profileImage = null;
      },
      (profileUrl) async {
        // Update profileImageUrl with the new URL received from backend
        currentUser?.profileImage = profileUrl;
        // Reset profileImage to null to avoid using the old image path
        profileImage = null;

        emit(UsersStateProfileImageUpdated(
          profileImage: profileUrl,
          message: "Profile Image Changed Successfully",
        ));
      },
    );
  }

  FutureOr<void> _onChangeUserPasswordEvent(
      ChangeUserPasswordEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateLoading());
    final response = await usersRepository.changePassword(
      newPassword: event.newPassword,
      email: event.email,
    );
    response.fold(
      (left) {
        //
        _handlerError(left, emit);
      },
      (msg) {
        //
        emit(UsersStateUsersSuccess(message: msg));
      },
    );
  }

  FutureOr<void> _onGetAllUsersEvent(
      GetAllUsersEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateAllUserLoading());
    final response = await usersRepository.getAllUsers();
    response.fold(
      (left) {
        //
        allUsers.clear();
        emit(UsersStateAllUserError(appFailure: left));
      },
      (users) {
        //
        allUsers = users;

        emit(UsersStateAllUserLoaded(users: users));
      },
    );
  }

  FutureOr<void> _onGetAuthUserEvent(
      GetAuthUserEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateDataLoading());
    final response = await usersRepository.getAuthUser(
      forceFetch: event.forceFetch,
    );
    response.fold(
      (left) {
        //
        currentUser = null;
        emit(UsersStateDataError(appFailure: left));
      },
      (user) {
        //
        currentUser = user;
        emit(UsersStateDataLoaded(user: user));
      },
    );
  }

  //
  FutureOr<void> _onToggleUserThemeEvent(
      ToggleUserThemeEvent event, Emitter<UsersState> emit) async {
    if (currentThemeMode == ThemeMode.light) {
      currentThemeMode = ThemeMode.dark;
    } else {
      currentThemeMode = ThemeMode.light;
    }

    await flutterSecureStorageServices.storeData(
        'themeMode', currentThemeMode == ThemeMode.light ? 'light' : 'dark');

    add(LoadUserThemeEvent());
    emit(ThemeModeUpdated(themeMode: currentThemeMode));
  }

  FutureOr<void> _onLoadUserThemeEvent(
      LoadUserThemeEvent event, Emitter<UsersState> emit) async {
    String? themeMode =
        await flutterSecureStorageServices.readData('themeMode');
    if (themeMode != null) {
      currentThemeMode =
          themeMode == 'light' ? ThemeMode.light : ThemeMode.dark;
    }
    emit(ThemeModedLoaded());
  }

  FutureOr<void> _onGetDashboardEvent(
      GetDashboardEvent event, Emitter<UsersState> emit) async {
    emit(UsersStateDashboardLoading());
    final response = await usersRepository.getDashboard();
    await response.fold((left) {
      //
      _dashboard = null;
      emit(UsersStateDashboardError(appFailure: left));
    }, (dashboardModel) {
      //
      _dashboard = dashboardModel;
      emit(UsersStateDashboardLoaded(dashboard: dashboardModel));
    });
  }
}
