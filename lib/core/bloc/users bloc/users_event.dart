part of 'users_bloc.dart';

abstract class UsersEvent extends Equatable {
  const UsersEvent();

  @override
  List<Object?> get props => [];
}

class GetAllUsersEvent extends UsersEvent {}

class GetAuthUserEvent extends UsersEvent {
  final bool forceFetch;

  GetAuthUserEvent({this.forceFetch = false});

  @override
  List<Object?> get props => [forceFetch];
}

class ToggleUserThemeEvent extends UsersEvent {}

class LoadUserThemeEvent extends UsersEvent {}

class PickProfileImageEvent extends UsersEvent {
  final ImageSource imageSource;

  PickProfileImageEvent({this.imageSource = ImageSource.gallery});

  @override
  List<Object?> get props => [imageSource];
}

class ChangeProfileImageEvent extends UsersEvent {
  final String employeeId;

  ChangeProfileImageEvent({required this.employeeId});

  @override
  List<Object?> get props => [employeeId];
}

class GetProfileImageEvent extends UsersEvent {
  final String employeeId;

  GetProfileImageEvent({required this.employeeId});

  @override
  List<Object?> get props => [employeeId];
}

class ChangeUserPasswordEvent extends UsersEvent {
  final String newPassword;
  final String email;

  ChangeUserPasswordEvent({
    required this.newPassword,
    required this.email,
  });

  @override
  List<Object?> get props => [newPassword, email];
}


class GetDashboardEvent extends UsersEvent {}
