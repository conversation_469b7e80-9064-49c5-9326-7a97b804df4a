part of 'notification_bloc.dart';

abstract class NotificationEvent extends Equatable {
  const NotificationEvent();

  @override
  List<Object?> get props => [];
}

class SendNotificationEvent extends NotificationEvent {
  final List<String> title;
  final String message;
  final List<String> fcmToken;
  final String userId;

  SendNotificationEvent({
    required this.title,
    required this.message,
    required this.fcmToken,
    required this.userId,
  });

  @override
  List<Object?> get props => [title, message, fcmToken, userId];
}

class GetAllNotifications extends NotificationEvent {}

class DeleteNotifications extends NotificationEvent {
  final String notificationId;

  DeleteNotifications({required this.notificationId});

  @override
  List<Object?> get props => [notificationId];
}
