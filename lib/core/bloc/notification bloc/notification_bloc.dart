import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/notification_model.dart';
import 'package:rasiin_tasks_app/core/repository/notification_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'notification_event.dart';
part 'notification_state.dart';

class NotificationBloc extends Bloc<NotificationEvent, NotificationState> {
  final NotificationRepository notificationRepository;
  NotificationBloc({
    required this.notificationRepository,
  }) : super(NotificationStateInitial()) {
    on<SendNotificationEvent>(_onSendNotificationEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllNotifications>(
      _onGetAllNotifications,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
    on<DeleteNotifications>(
      _onDeleteNotifications,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
  }

  List<NotificationModel> notifications = [];

  FutureOr<void> _onSendNotificationEvent(
      SendNotificationEvent event, Emitter<NotificationState> emit) async {
    emit(NotificationStateLoading());
    final response = await notificationRepository.sendNotification(
      title: event.title,
      message: event.message,
      fcmTokens: event.fcmToken,
      userId: event.userId,
    );
    response.fold(
      (left) {
        //
        emit(NotificationStateError(appFailure: left));
      },
      (right) {
        //
        emit(NotificationStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onGetAllNotifications(
      GetAllNotifications event, Emitter<NotificationState> emit) async {
    emit(NotificationStateLoading());
    final response = await notificationRepository.getAllNotifications();
    response.fold(
      (left) {
        notifications.clear();
        emit(NotificationStateError(appFailure: left));
      },
      (right) {
        notifications = right;
        emit(NotificationStateLoaded());
      },
    );
  }

  FutureOr<void> _onDeleteNotifications(
      DeleteNotifications event, Emitter<NotificationState> emit) async {
    emit(NotificationStateLoading());
    final response = await notificationRepository.deleteSingleNotification(
        id: event.notificationId);
    response.fold(
      (left) {
        emit(NotificationStateError(appFailure: left));
      },
      (right) {
        notifications.removeWhere(
            (element) => element.notificationId == event.notificationId);
        emit(NotificationStateLoaded());
        emit(NotificationStateSuccess(message: right));
      },
    );
  }
}
