part of 'notification_bloc.dart';

abstract class NotificationState extends Equatable {
  const NotificationState();

  @override
  List<Object?> get props => [];
}

class NotificationStateInitial extends NotificationState {}

 class NotificationStateLoading extends NotificationState {}

 class NotificationStateProjectLoading extends NotificationState {}

 class NotificationStateLoaded extends NotificationState {}

 class NotificationStateError extends NotificationState {
  final AppFailure appFailure;

  NotificationStateError({required this.appFailure});

  @override
  List<Object?> get props => [appFailure];
}

 class NotificationStateSuccess extends NotificationState {
  final String message;

  NotificationStateSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}
