part of 'leave_bloc.dart';

// Base class for all Leave states
abstract class LeaveState {}

// Initial state of the LeaveBloc
class LeaveInitialState extends LeaveState {}

// Loading state for leave creation
class LeaveCreationLoadingState extends LeaveState {}

// State when leaves are being loaded
class LeaveLoadingState extends LeaveState {}

// State when leave types are being loaded
class LeaveTypeLoadingState extends LeaveState {}

// State when leave creation is successful
class LeaveSuccessState extends LeaveState {
  final String message;

  LeaveSuccessState({required this.message});
}

// State when leaves are successfully loaded
class LeaveLoadedState extends LeaveState {
  final List<LeaveModel> leaves;

  LeaveLoadedState({required this.leaves});
}

// State when leave types are successfully loaded
class LeaveTypeLoadedState extends LeaveState {
  final List<LeaveTypeModel> leaveTypes;

  LeaveTypeLoadedState({required this.leaveTypes});
}

// Error state for leaves
class LeaveErrorState extends LeaveState {
  final AppFailure appFailure;

  LeaveErrorState({
    required this.appFailure,
  });
}

// Error state for leave types
class LeaveTypeErrorState extends LeaveState {
  final AppFailure appFailure;

  LeaveTypeErrorState({
    required this.appFailure,
  });
}

// Leave creation error state
class LeaveCreationErrorState extends LeaveState {
  final AppFailure appFailure;

  LeaveCreationErrorState({
    required this.appFailure,
  });
}

// Leave Creation Success state
class LeaveCreationSuccessState extends LeaveState {
  final String message;

  LeaveCreationSuccessState({required this.message});
}

// Approve Leave Loading state
class LeaveApprovalLoadingState extends LeaveState {}

// Approve Leave Success state
class LeaveApprovalSuccessState extends LeaveState {
  final String message;

  LeaveApprovalSuccessState({required this.message});
}

// Approve Leave Error state
class LeaveApprovalErrorState extends LeaveState {
  final AppFailure appFailure;

  LeaveApprovalErrorState({required this.appFailure});
}
