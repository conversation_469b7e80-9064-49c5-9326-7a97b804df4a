import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/leave_model.dart';
import 'package:rasiin_tasks_app/core/models/leave_type_model.dart';
import 'package:rasiin_tasks_app/core/repository/leave_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'leave_event.dart';
part 'leave_state.dart';

class LeaveBloc extends Bloc<LeaveEvent, LeaveState> {
  final LeaveRepository leaveRepository;

  LeaveBloc({required this.leaveRepository}) : super(LeaveInitialState()) {
    on<CreateLeaveEvent>(
      _onCreateLeaveEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllLeavesEvent>(
      _onGetAllLeavesEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllLeaveTypesEvent>(
      _onGetAllLeaveTypesEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ApproveLeaveEvent>(
      _onApproveLeaveEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<LeaveModel> _allLeaves = [];
  List<LeaveModel> _filteredLeaves = [];
  List<LeaveModel> get leaves => _filteredLeaves;

  List<LeaveTypeModel> _allLeaveTypes = [];
  List<LeaveTypeModel> _filteredLeaveTypes = [];
  List<LeaveTypeModel> get leaveTypes => _filteredLeaveTypes;

  // Handle leave errors and emit appropriate state
  void _emitLeaveError(AppFailure failure, Emitter<LeaveState> emit) {
    _allLeaves.clear();
    emit(LeaveErrorState(appFailure: failure));
  }

  // Handle leave type errors and emit appropriate state
  void _emitLeaveTypeError(AppFailure failure, Emitter<LeaveState> emit) {
    _allLeaveTypes.clear();
    emit(LeaveTypeErrorState(appFailure: failure));
  }

  // Emit loaded state for leaves
  void _emitLeavesLoaded(List<LeaveModel> leaves, Emitter<LeaveState> emit) {
    _allLeaves = leaves;
    _filteredLeaves = List.from(_allLeaves);
    emit(LeaveLoadedState(leaves: _filteredLeaves));
  }

  // Emit loaded state for leave types
  void _emitLeaveTypesLoaded(
      List<LeaveTypeModel> leaveTypes, Emitter<LeaveState> emit) {
    _allLeaveTypes = leaveTypes;
    _filteredLeaveTypes = List.from(_allLeaveTypes);
    emit(LeaveTypeLoadedState(leaveTypes: _filteredLeaveTypes));
  }

  FutureOr<void> _onCreateLeaveEvent(
      CreateLeaveEvent event, Emitter<LeaveState> emit) async {
    emit(LeaveCreationLoadingState());
    final response = await leaveRepository.createLeave(
      employeeId: event.employeeId,
      leaveType: event.leaveType,
      fromDate: event.fromDate,
      toDate: event.toDate,
      reason: event.reason,
    );
    response.fold(
      (failure) {
        emit(LeaveCreationErrorState(appFailure: failure));
      },
      (success) {
        emit(LeaveCreationSuccessState(message: success));
      },
    );
  }

  FutureOr<void> _onGetAllLeavesEvent(
      GetAllLeavesEvent event, Emitter<LeaveState> emit) async {
    emit(LeaveLoadingState());
    final response = await leaveRepository.getAllLeaves(
      employee: event.employee,
      userRoles: event.userRoles,
    );
    response.fold(
      (failure) {
        _emitLeaveError(failure, emit);
      },
      (success) {
        _emitLeavesLoaded(success, emit);
      },
    );
  }

  FutureOr<void> _onGetAllLeaveTypesEvent(
      GetAllLeaveTypesEvent event, Emitter<LeaveState> emit) async {
    emit(LeaveTypeLoadingState());
    final response = await leaveRepository.getAllLeaveTypes();
    response.fold(
      (failure) {
        _emitLeaveTypeError(failure, emit);
      },
      (success) {
        _emitLeaveTypesLoaded(success, emit);
      },
    );
  }

  FutureOr<void> _onApproveLeaveEvent(
      ApproveLeaveEvent event, Emitter<LeaveState> emit) async {
    emit(LeaveApprovalLoadingState());
    final response = await leaveRepository.approveLeave(
      leaveId: event.leaveId,
      userRoles: event.userRoles,
    );
    response.fold(
      (failure) {
        emit(LeaveApprovalErrorState(appFailure: failure));
      },
      (success) {
        emit(LeaveApprovalSuccessState(message: success));
      },
    );
  }
}
