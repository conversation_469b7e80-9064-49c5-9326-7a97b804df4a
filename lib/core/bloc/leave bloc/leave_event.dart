part of 'leave_bloc.dart';

// Base class for all Leave events
abstract class LeaveEvent {}

// Event to create a leave
class CreateLeaveEvent extends LeaveEvent {
  final String employeeId;
  final String leaveType;
  final String fromDate;
  final String toDate;
  final String reason;

  CreateLeaveEvent({
    required this.employeeId,
    required this.leaveType,
    required this.fromDate,
    required this.toDate,
    required this.reason,
  });
}

// Event to fetch all leaves
class GetAllLeavesEvent extends LeaveEvent {
  final String employee;
  final List<String> userRoles;

  GetAllLeavesEvent({
    required this.employee,
    required this.userRoles,
  });
}

// Event to fetch all leave types
class GetAllLeaveTypesEvent extends LeaveEvent {}

// Event to approve a leave
class ApproveLeaveEvent extends LeaveEvent {
  final String leaveId;
  final List<String> userRoles;

  ApproveLeaveEvent({
    required this.leaveId,
    required this.userRoles,
  });
}
