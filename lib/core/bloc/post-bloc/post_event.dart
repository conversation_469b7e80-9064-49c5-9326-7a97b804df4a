part of 'post_bloc.dart';

sealed class PostEvent extends Equatable {
  const PostEvent();

  @override
  List<Object> get props => [];
}

final class PostVideoPickEvent extends PostEvent {
  final Duration? maxDuration;

  const PostVideoPickEvent({
    this.maxDuration,
  });
}

final class PostMultipleImagesPickEvent extends PostEvent {}

final class PostRemoveImageEvent extends PostEvent {
  final XFile image;

  const PostRemoveImageEvent({
    required this.image,
  });

  @override
  List<Object> get props => [image];
}

final class PostRemoveVideoEvent extends PostEvent {
  final XFile video;

  const PostRemoveVideoEvent({
    required this.video,
  });

  @override
  List<Object> get props => [video];
}

// Add load more event
final class PostLoadMoreEvent extends PostEvent {
  final String userEmail;
  final int limit;
  final int offset;

  const PostLoadMoreEvent({
    required this.userEmail,
    required this.limit,
    required this.offset,
  });

  @override
  List<Object> get props => [userEmail, limit, offset];
}

// get all posts
final class PostGetAllEvent extends PostEvent {
  final bool forceRefresh;
  final String userEmail;
  final int limit;
  final int offset;

  const PostGetAllEvent({
    this.forceRefresh = false,
    required this.userEmail,
    this.limit = 50,
    this.offset = 0,
  });

  @override
  List<Object> get props => [forceRefresh, userEmail, limit, offset];
}

// Like Post
final class PostToggleLikeEvent extends PostEvent {
  final String postId;
  final String userEmail;

  const PostToggleLikeEvent({
    required this.postId,
    required this.userEmail,
  });

  @override
  List<Object> get props => [postId, userEmail];
}

// Vote on Poll
final class PostVoteEvent extends PostEvent {
  final String postId;
  final String userEmail;
  final String optionId;

  const PostVoteEvent({
    required this.postId,
    required this.userEmail,
    required this.optionId,
  });

  @override
  List<Object> get props => [postId, userEmail, optionId];
}

// create poll
final class PostCreatePollEvent extends PostEvent {
  final String userEmail;
  final String question;
  final String option1;
  final String option2;
  final String? option3;
  final String? option4;
  final int? durationMins;

  const PostCreatePollEvent({
    required this.userEmail,
    required this.question,
    required this.option1,
    required this.option2,
    this.option3,
    this.option4,
    this.durationMins,
  });

  @override
  List<Object> get props => [
        userEmail,
        question,
        option1,
        option2,
        option3 ?? '',
        option4 ?? '',
        durationMins ?? 0,
      ];
}

// create media post
final class PostCreatePostEvent extends PostEvent {
  final String userEmail;
  final String content;
  final XFile? pickedVideo;
  final List<XFile> pickedImages;

  const PostCreatePostEvent({
    required this.userEmail,
    required this.content,
    required this.pickedVideo,
    required this.pickedImages,
  });

  @override
  List<Object> get props => [
        userEmail,
        content,
        pickedVideo ?? '',
        pickedImages,
      ];
}
