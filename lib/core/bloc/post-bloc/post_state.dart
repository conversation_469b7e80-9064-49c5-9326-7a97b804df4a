part of 'post_bloc.dart';

sealed class PostState extends Equatable {
  const PostState();

  @override
  List<Object> get props => [];
}

final class PostInitial extends PostState {}

final class PostVideoPickLoading extends PostState {}

final class PostVideoPickFailure extends PostState {
  final AppFailure failure;

  const PostVideoPickFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostVideoPicked extends PostState {
  final XFile video;

  const PostVideoPicked({
    required this.video,
  });

  @override
  List<Object> get props => [video];
}

final class PostMultipleImagesPickLoading extends PostState {}

final class PostMultipleImagesPickFailure extends PostState {
  final AppFailure failure;

  const PostMultipleImagesPickFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostMultipleImagesPicked extends PostState {
  final List<XFile> images;

  const PostMultipleImagesPicked({
    required this.images,
  });

  @override
  List<Object> get props => [images];
}

final class PostRemoveImageLoading extends PostState {}

final class PostRemoveImageFailure extends PostState {
  final AppFailure failure;

  const PostRemoveImageFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostRemoveImageSuccess extends PostState {}

final class PostRemoveVideoLoading extends PostState {}

final class PostRemoveVideoFailure extends PostState {
  final AppFailure failure;

  const PostRemoveVideoFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostRemoveVideoSuccess extends PostState {}

final class PostCreateLoading extends PostState {}

final class PostCreateFailure extends PostState {
  final AppFailure failure;

  const PostCreateFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostCreated extends PostState {
  final String message;

  const PostCreated({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

final class PostGetAllLoading extends PostState {}

final class PostGetAllFailure extends PostState {
  final AppFailure failure;

  const PostGetAllFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostGetAllLoaded extends PostState {
  final List<PostModel> posts;

  const PostGetAllLoaded({
    required this.posts,
  });

  @override
  List<Object> get props => [posts];
}

final class PostGetAllEmpty extends PostState {
  final String message;

  const PostGetAllEmpty({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// Like Post
final class PostToggleLikeLoading extends PostState {}

final class PostToggleLikeFailure extends PostState {
  final AppFailure failure;

  const PostToggleLikeFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostToggleLikeSuccess extends PostState {
  final String message;

  const PostToggleLikeSuccess({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// Vote on Poll
final class PostVoteLoading extends PostState {
  final String postId;
  final String optionId;

  PostVoteLoading({required this.postId, required this.optionId});

  @override
  List<Object> get props => [postId, optionId];
}

final class PostVoteFailure extends PostState {
  final AppFailure failure;

  const PostVoteFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostVoteSuccess extends PostState {
  final String message;

  const PostVoteSuccess({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// create poll
final class PostCreatePollLoading extends PostState {}

final class PostCreatePollFailure extends PostState {
  final AppFailure failure;

  const PostCreatePollFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostCreatePollSuccess extends PostState {
  final String message;

  const PostCreatePollSuccess({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// create post
final class PostCreatePostLoading extends PostState {}

final class PostCreatePostFailure extends PostState {
  final AppFailure failure;

  const PostCreatePostFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

final class PostCreatePostSuccess extends PostState {
  final String message;

  const PostCreatePostSuccess({
    required this.message,
  });

  @override
  List<Object> get props => [message];
}

// Add load more states
final class PostLoadMoreLoaded extends PostState {
  final List<PostModel> newPosts;
  final bool hasMoreData;

  const PostLoadMoreLoaded({
    required this.newPosts,
    required this.hasMoreData,
  });

  @override
  List<Object> get props => [newPosts, hasMoreData];
}

final class PostLoadMoreFailure extends PostState {
  final AppFailure failure;

  const PostLoadMoreFailure({
    required this.failure,
  });

  @override
  List<Object> get props => [failure];
}

