import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';
import 'package:rasiin_tasks_app/core/repository/post_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'post_event.dart';
part 'post_state.dart';

class PostBloc extends Bloc<PostEvent, PostState> {
  final PostRepository postRepository;
  PostBloc({
    required this.postRepository,
  }) : super(PostInitial()) {
    on<PostVideoPickEvent>(
      _onPostVideoPick,
      transformer: DebounceHelper.blocDebouncer(),
    );
    on<PostMultipleImagesPickEvent>(
      _onPostMultipleImagesPick,
      transformer: DebounceHelper.blocDebouncer(),
    );
    on<PostRemoveImageEvent>(_onPostRemoveImage,
        transformer: DebounceHelper.blocDebouncer());
    on<PostRemoveVideoEvent>(_onPostRemoveVideo,
        transformer: DebounceHelper.blocDebouncer());
    on<PostGetAllEvent>(_onPostGetAll,
        transformer: DebounceHelper.blocDebouncer());
    on<PostToggleLikeEvent>(_onPostToggleLike,
        transformer: DebounceHelper.blocDebouncer());
    on<PostVoteEvent>(_onPostVoteEvent,
        transformer: DebounceHelper.blocDebouncer());
    on<PostCreatePostEvent>(_onPostCreatePostEvent,
        transformer: DebounceHelper.blocDebouncer());
    on<PostCreatePollEvent>(_onPostCreatePollEvent,
        transformer: DebounceHelper.blocDebouncer());
    on<PostLoadMoreEvent>(_onPostLoadMore,
        transformer: DebounceHelper.blocDebouncer());
  }

  XFile? video;
  List<XFile> images = [];

  List<PostModel> _posts = [];
  List<PostModel> get posts => _posts;

  // _onPostGetAll
  void _onPostGetAll(PostGetAllEvent event, Emitter<PostState> emit) async {
    emit(PostGetAllLoading());
    final result = await postRepository.getAllPosts(
      forceRefresh: event.forceRefresh,
      userEmail: event.userEmail,
      limit: event.limit,
      offset: event.offset,
    );
    result.fold(
      (failure) => emit(PostGetAllFailure(failure: failure)),
      (allPosts) {
        print('all posts: ${allPosts.length}');
        if (allPosts.isEmpty) {
          emit(PostGetAllEmpty(message: 'No posts found'));
          return;
        }
        _posts = allPosts;
        emit(PostGetAllLoaded(posts: allPosts));
      },
    );
  }

  void _onPostToggleLike(
      PostToggleLikeEvent event, Emitter<PostState> emit) async {
    emit(PostToggleLikeLoading());
    final result = await postRepository.toggleLikePost(
      postId: event.postId,
      userEmail: event.userEmail,
    );
    result.fold(
      (failure) => emit(PostToggleLikeFailure(failure: failure)),
      (message) {
        emit(PostToggleLikeSuccess(message: message));
      },
    );
  }

  _onPostVoteEvent(PostVoteEvent event, Emitter<PostState> emit) async {
    emit(PostVoteLoading(postId: event.postId, optionId: event.optionId));
    final result = await postRepository.voteOnPoll(
      postId: event.postId,
      optionId: event.optionId,
      userEmail: event.userEmail,
    );
    result.fold(
      (failure) => emit(PostVoteFailure(failure: failure)),
      (message) {
        emit(PostVoteSuccess(message: message));
      },
    );
  }

  void _onPostVideoPick(
      PostVideoPickEvent event, Emitter<PostState> emit) async {
    emit(PostVideoPickLoading());
    final result =
        await postRepository.pickVideo(maxDuration: event.maxDuration);
    result.fold(
      (failure) => emit(PostVideoPickFailure(failure: failure)),
      (video) {
        images.clear();
        this.video = video;
        emit(PostVideoPicked(video: video));
      },
    );
  }

  void _onPostMultipleImagesPick(
      PostMultipleImagesPickEvent event, Emitter<PostState> emit) async {
    emit(PostMultipleImagesPickLoading());
    final result = await postRepository.pickMultipleImages();
    result.fold(
      (failure) => emit(PostMultipleImagesPickFailure(failure: failure)),
      (images) {
        video = null;
        this.images.addAll(images);
        emit(PostMultipleImagesPicked(images: images));
      },
    );
  }

  void _onPostRemoveImage(PostRemoveImageEvent event, Emitter<PostState> emit) {
    emit(PostRemoveImageLoading());
    images.remove(event.image);
    emit(PostRemoveImageSuccess());
  }

  void _onPostRemoveVideo(PostRemoveVideoEvent event, Emitter<PostState> emit) {
    emit(PostRemoveVideoLoading());
    video = null;
    emit(PostRemoveVideoSuccess());
  }

  void _onPostCreatePostEvent(
      PostCreatePostEvent event, Emitter<PostState> emit) async {
    emit(PostCreatePostLoading());
    final result = await postRepository.createPost(
      userEmail: event.userEmail,
      content: event.content,
      pickedVideo: video,
      pickedImages: images,
    );
    result.fold(
      (failure) {
        images.clear();
        video = null;
        emit(PostCreatePostFailure(failure: failure));
      },
      (message) {
        images.clear();
        video = null;
        emit(PostCreatePostSuccess(message: message));
      },
    );
  }

  void _onPostCreatePollEvent(
      PostCreatePollEvent event, Emitter<PostState> emit) async {
    emit(PostCreatePollLoading());
    final result = await postRepository.createPollPost(
      userEmail: event.userEmail,
      question: event.question,
      option1: event.option1,
      option2: event.option2,
      option3: event.option3,
      option4: event.option4,
      durationMins: event.durationMins,
    );
    result.fold(
      (failure) => emit(PostCreatePollFailure(failure: failure)),
      (message) {
        emit(PostCreatePollSuccess(message: message));
      },
    );
  }

  void _onPostLoadMore(PostLoadMoreEvent event, Emitter<PostState> emit) async {
    final result = await postRepository.getAllPosts(
      forceRefresh: false,
      userEmail: event.userEmail,
      limit: event.limit,
      offset: event.offset,
    );

    result.fold(
      (failure) => emit(PostLoadMoreFailure(failure: failure)),
      (newPosts) {
        _posts.addAll(newPosts);
        emit(PostLoadMoreLoaded(
          newPosts: newPosts,
          hasMoreData: newPosts.length >= event.limit,
        ));
      },
    );
  }
}
