import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:equatable/equatable.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/attendence_model.dart';
import 'package:rasiin_tasks_app/core/repository/attendence_repository.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'attendence_event.dart';
part 'attendence_state.dart';

class AttendanceBloc extends Bloc<AttendanceEvent, AttendanceState> {
  final AttendanceRepository attendanceRepository;

  AttendanceBloc({required this.attendanceRepository})
      : super(AttendanceInitialState()) {
    on<AttendanceFetchEvent>(
      _onAttendanceFetchEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AttendanceFilterEvent>(
      _onAttendanceFilterEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<AttendenceModel> _attendances = [];
  List<AttendenceModel> get allAttendances => _attendances;
  List<AttendenceModel> _filteredAttendances = [];
  List<AttendenceModel> get filteredAttendances => _filteredAttendances;

  FutureOr<void> _onAttendanceFetchEvent(
      AttendanceFetchEvent event, Emitter<AttendanceState> emit) async {
    emit(AttendanceLoadingState());
    final result = await attendanceRepository.getAllAttendances(
      employeeId: event.employeeId,
      userRoles: event.userRoles,
      forceRefresh: event.forceRefresh,
    );

    result.fold(
      (failure) {
        _attendances.clear();
        emit(AttendanceErrorState(appFailure: failure));
      },
      (success) {
        _attendances = success;
        _filteredAttendances = List.from(_attendances);
        emit(AttendanceLoadedState(attendances: _filteredAttendances));
      },
    );
  }

  FutureOr<void> _onAttendanceFilterEvent(
      AttendanceFilterEvent event, Emitter<AttendanceState> emit) async {
    emit(AttendenceFilterLoadingState());
    final result = await attendanceRepository.filterAttendances(
      year: event.year,
      month: event.month,
      employeeIds: event.employeeIds,
    );

    result.fold(
      (failure) {
        _filteredAttendances.clear();
        emit(AttendanceFilterErrorState(appFailure: failure));
      },
      (success) {
        _filteredAttendances = List.from(success);
        emit(AttendanceFilteredState(attendances: _filteredAttendances));
      },
    );
  }
}
