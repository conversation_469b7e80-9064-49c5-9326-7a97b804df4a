part of 'attendence_bloc.dart';

abstract class AttendanceEvent extends Equatable {
  const AttendanceEvent();

  @override
  List<Object?> get props => [];
}

class AttendanceFetchEvent extends AttendanceEvent {
  final String employeeId;
  final List<String> userRoles;
  final bool forceRefresh;

  AttendanceFetchEvent({
    required this.employeeId,
    required this.userRoles,
    this.forceRefresh = false,
  });

  @override
  List<Object?> get props => [employeeId, userRoles];
}

class AttendanceFilterEvent extends AttendanceEvent {
  final Month month;
  final int year;
  final List<String>? employeeIds;
  AttendanceFilterEvent({
    required this.month,
    required this.year,
    this.employeeIds,
  });

  @override
  List<Object?> get props => [month, year, employeeIds];
}
