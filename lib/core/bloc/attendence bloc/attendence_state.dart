part of 'attendence_bloc.dart';

abstract class AttendanceState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AttendanceInitialState extends AttendanceState {}

class AttendanceLoadingState extends AttendanceState {}

class AttendanceLoadedState extends AttendanceState {
  final List<AttendenceModel> attendances;

  AttendanceLoadedState({required this.attendances});

  @override
  List<Object?> get props => [attendances];
}

class AttendenceFilterLoadingState extends AttendanceState {}

class AttendanceFilteredState extends AttendanceState {
  final List<AttendenceModel> attendances;

  AttendanceFilteredState({required this.attendances});

  @override
  List<Object?> get props => [attendances];
}

class AttendanceFilterErrorState extends AttendanceState {
  final AppFailure appFailure;

  AttendanceFilterErrorState({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class AttendanceErrorState extends AttendanceState {
  final AppFailure appFailure;

  AttendanceErrorState({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}
