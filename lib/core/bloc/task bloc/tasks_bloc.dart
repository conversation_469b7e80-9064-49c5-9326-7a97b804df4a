import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';
import 'package:rasiin_tasks_app/core/models/project_model.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/repository/notification_repository.dart';
import 'package:rasiin_tasks_app/core/repository/task_repository.dart';
import 'package:rasiin_tasks_app/core/enums/filter_field_enum.dart';
import 'package:rasiin_tasks_app/core/enums/filter_operator_enum.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'tasks_event.dart';
part 'tasks_state.dart';

class TasksBloc extends Bloc<TasksEvent, TasksState> {
  final NotificationRepository notificationRepository;
  final TaskRepository taskRepository;
  TasksBloc({
    required this.taskRepository,
    required this.notificationRepository,
  }) : super(TasksStateInitial()) {
    on<CreateTaskEvent>(
      _onCreateTaskEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllProjectsEvent>(
      _onGetAllProjectsEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllTasksEvent>(
      _onGetAllTasksEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    // on<FilterAllTasks>(_onFilterAllTasks);
    // on<FilterAssignedTasks>(_onFilterAssignedTasks);
    // on<SortAllTasks>(_onSortAllTasks);
    // on<SortAssignedTasks>(_onSortAssignedTasks);
    on<GetAllAssignedTasksEvent>(
      _onGetAllAssignedTasksEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AssignTaskEvent>(
      _onAssignTaskEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<SendNotificationEvent>(
      _onSendNotificationEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ChangeTaskStatus>(
      _onChangeTaskStatus,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetTasksCommentEvent>(
      _onGetTasksCommentEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<SendCommentInAllTasksEvent>(
      _onSendCommentInAllTasksEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  // notificationError
  _handleNotificationError(AppFailure apiFailure, Emitter<TasksState> emit) {
    emit(
      TasksStateNotificationError(appFailure: apiFailure),
    );
  }

  //! notification
  FutureOr<void> _onSendNotificationEvent(
      SendNotificationEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateNotitifcationLoading());
    final response = await notificationRepository.sendNotification(
      title: event.title,
      message: event.message,
      fcmTokens: event.fcmToken,
      userId: event.userId,
    );
    response.fold(
      (left) {
        //
        _handleNotificationError(left, emit);
      },
      (right) {
        //
        emit(TasksStateNotificationSuccess(message: right));
      },
    );
  }

  //
  List<TaskModel> _allTasks = [];
  List<TaskModel> _filteredTasks = [];
  List<TaskModel> get tasks => _filteredTasks;

  List<ProjectModel> _allProjects = [];
  List<ProjectModel> _filteredProjects = [];
  List<ProjectModel> get projects => _filteredProjects;

  List<AssignedTasksModel> _allAssignedTasks = [];
  List<AssignedTasksModel> _filteredAssignedTasks = [];
  List<AssignedTasksModel> get assignedTasks => _filteredAssignedTasks;

  List<CommentModel> _allcomments = [];
  List<CommentModel> _filteredcomments = [];
  List<CommentModel> get comments => _filteredcomments;

  String? assigngTaskId;

  _handleError(AppFailure apiFailure, Emitter<TasksState> emit) {
    emit(TasksStateError(appFailure: apiFailure));
  }

  _handleAllTaksError(AppFailure apiFailure, Emitter<TasksState> emit) {
    emit(TasksStateAllTasksError(appFailure: apiFailure));
  }

  _handleAssignedTasksError(AppFailure apiFailure, Emitter<TasksState> emit) {
    emit(
      TasksStateAssignedTasksError(
        appFailure: apiFailure,
      ),
    );
  }

  FutureOr<void> _onCreateTaskEvent(
      CreateTaskEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateLoading());
    final task = TaskModel(
      projectId: event.selectedProjectId,
      projectName: event.selectedProjectName,
      dueDate: event.selectedTime,
      prior: event.selectedPriority,
      subj: event.subject,
      taskStatus: event.selectedStatus,
      desc: event.description,
      taskId: '',
      customerName: '',
      userEmail: '',
      commentCount: 0,
    );
    debugPrint("sended task is : ${task.toJson()}");
    final response =
        await taskRepository.createTask(task: task, userRoles: event.userRoles);
    response.fold(
      (left) {
        //
        _handleError(left, emit);
      },
      (right) {
        //
        emit(TasksStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onGetAllProjectsEvent(
      GetAllProjectsEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateProjectLoading());
    final response = await taskRepository.getAllProjects();
    response.fold(
      (left) {
        //
        _allProjects.clear();
        _handleError(left, emit);
      },
      (right) {
        //
        _allProjects = right;
        _filteredProjects = List.from(_allProjects);
        emit(TasksStateLoaded());
      },
    );
  }

  FutureOr<void> _onGetAllTasksEvent(
      GetAllTasksEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateAllTasksLoading());
    final response = await taskRepository.getAllTasks(
      userRoles: event.userRoles,
    );
    response.fold(
      (left) {
        //
        _allTasks.clear();
        _handleAllTaksError(left, emit);
      },
      (right) {
        //
        _allTasks = right;
        _filteredTasks = List.from(_allTasks);
        emit(TasksStateAllTasksLoaded(tasks: _allTasks));
      },
    );
  }

  FutureOr<void> _onGetAllAssignedTasksEvent(
      GetAllAssignedTasksEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateAssignedTasksLoading());
    final response = await taskRepository.getAllAssignedTasks(
      currentUserEmail: event.currentUserEmail,
    );
    response.fold(
      (left) {
        //
        _allAssignedTasks.clear();
        _handleAssignedTasksError(left, emit);
      },
      (right) {
        //
        _allAssignedTasks = right;
        _filteredAssignedTasks = List.from(_allAssignedTasks);
        emit(TasksStateAssignedTasksLoaded(
          assignedTasks: _allAssignedTasks,
        ));
      },
    );
  }

  // FutureOr<void> _onFilterAllTasks(
  //     FilterAllTasks event, Emitter<TasksState> emit) async {
  //   emit(TasksStateFiltering());
  //   final List<TaskModel> response = await taskRepository.filterTasks(
  //     field: event.field,
  //     operator: event.operator,
  //     value: event.value,
  //   );
  //   if (response.isEmpty) {
  //     return;
  //   }
  //   _filteredTasks = response;
  //   emit(TasksStateAllTasksLoaded(
  //     tasks: _filteredTasks,
  //     showSnackBar: false,
  //   ));
  // }

  // FutureOr<void> _onFilterAssignedTasks(
  //     FilterAssignedTasks event, Emitter<TasksState> emit) async {
  //   emit(TasksStateFiltering());
  //   final List<AssignedTasksModel> response =
  //       await taskRepository.filterAssignedTasks(
  //     field: event.field,
  //     operator: event.operator,
  //     value: event.value,
  //   );
  //   if (response.isEmpty) {
  //     return;
  //   }
  //   _filteredAssignedTasks = response;
  //   emit(TasksStateAssignedTasksLoaded(
  //     assignedTasks: _filteredAssignedTasks,
  //     showSnackBar: false,
  //   ));
  // }

  // FutureOr<void> _onSortAllTasks(
  //     SortAllTasks event, Emitter<TasksState> emit) async {
  //   emit(TasksStateFiltering());
  //   final List<TaskModel> response = await taskRepository.sortTasks(
  //     field: event.field,
  //     isAscending: event.isAscending,
  //   );
  //   if (response.isEmpty) {
  //     return;
  //   }
  //   _filteredTasks = response;
  //   emit(TasksStateAllTasksLoaded(
  //     tasks: _filteredTasks,
  //     showSnackBar: false,
  //   ));
  // }

  // FutureOr<void> _onSortAssignedTasks(
  //     SortAssignedTasks event, Emitter<TasksState> emit) async {
  //   emit(TasksStateFiltering());
  //   final List<AssignedTasksModel> response =
  //       await taskRepository.sortAssignedTasks(
  //     field: event.field,
  //     isAscending: event.isAscending,
  //   );
  //   if (response.isEmpty) {
  //     return;
  //   }
  //   _filteredAssignedTasks = response;
  //   emit(TasksStateAssignedTasksLoaded(
  //     assignedTasks: _filteredAssignedTasks,
  //     showSnackBar: false,
  //   ));
  // }

  FutureOr<void> _onAssignTaskEvent(
      AssignTaskEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateLoading());

    final response = await taskRepository.assignTask(
      taskId: event.taskId,
      usersEmail: event.usersEmail,
      description: event.description,
      assignedDate: event.assignedDate,
      assignedBy: event.assignedBy,
    );
    response.fold(
      (left) {
        //
        assignedTasks.clear();
        _handleError(left, emit);
      },
      (right) {
        //
        emit(TasksStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onChangeTaskStatus(
      ChangeTaskStatus event, Emitter<TasksState> emit) async {
    emit(TasksStateLoading());
    final response = await taskRepository.changeTaskStatus(
      taskId: event.taksId,
      taskStatus: event.taskStatus,
    );
    response.fold(
      (left) {
        //
        _handleError(left, emit);
      },
      (right) {
        //
        emit(TasksStateSuccess(message: right));
      },
    );
  }

  FutureOr<void> _onGetTasksCommentEvent(
      GetTasksCommentEvent event, Emitter<TasksState> emit) async {
    if (event.load) {
      emit(TasksStateCommentsLoading());
    }
    final response = await taskRepository.getAllCommentsByReferenceType(
      referenceType: event.refrenceType,
      taskId: event.taskId,
    );
    response.fold(
      (left) {
        //
        _allcomments.clear();
        comments.clear();
        _filteredcomments.clear();
        _handleError(left, emit);
      },
      (right) {
        //
        _allcomments = right;
        _filteredcomments = List.from(_allcomments);
        emit(TasksStateLoaded());
      },
    );
  }

  FutureOr<void> _onSendCommentInAllTasksEvent(
      SendCommentInAllTasksEvent event, Emitter<TasksState> emit) async {
    emit(TasksStateLoading());
    final response = await taskRepository.sendCommentInAllTasks(
      taskId: event.taskId,
      userEmail: event.userEmail,
      comment: event.comment,
      taskType: event.referenceType,
    );
    response.fold(
      (left) {
        _handleError(left, emit);
      },
      (right) {
        //
        emit(TasksStateSuccess(message: right));
      },
    );
  }
}
