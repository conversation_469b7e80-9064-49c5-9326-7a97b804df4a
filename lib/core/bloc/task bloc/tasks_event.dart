part of 'tasks_bloc.dart';

abstract class TasksEvent extends Equatable {
  const TasksEvent();

  @override
  List<Object?> get props => [];
}

class GetTasksCommentEvent extends TasksEvent {
  final bool load;
  final String refrenceType;
  final String taskId;

  GetTasksCommentEvent({
    this.load = false,
    required this.refrenceType,
    required this.taskId,
  });

  @override
  List<Object?> get props => [load, refrenceType, taskId];
}

class GetAllTasksEvent extends TasksEvent {
  final List<String> userRoles;

  GetAllTasksEvent({
    required this.userRoles,
  });

  @override
  List<Object?> get props => [userRoles];
}

class GetAllAssignedTasksEvent extends TasksEvent {
  final String currentUserEmail;

  GetAllAssignedTasksEvent({
    required this.currentUserEmail,
  });

  @override
  List<Object?> get props => [currentUserEmail];
}

class FilterAllTasks extends TasksEvent {
  final FilterField field;
  final FilterOperator operator;
  final String value;

  FilterAllTasks(
      {required this.field, required this.operator, required this.value});

  @override
  List<Object?> get props => [field, operator, value];
}

class FilterAssignedTasks extends TasksEvent {
  final FilterField field;
  final FilterOperator operator;
  final String value;

  FilterAssignedTasks(
      {required this.field, required this.operator, required this.value});

  @override
  List<Object?> get props => [field, operator, value];
}

class SortAllTasks extends TasksEvent {
  final FilterField field;
  final bool isAscending;

  SortAllTasks({
    required this.field,
    this.isAscending = false,
  });

  @override
  List<Object?> get props => [field, isAscending];
}

class SortAssignedTasks extends TasksEvent {
  final FilterField field;
  final bool isAscending;

  SortAssignedTasks({
    required this.field,
    this.isAscending = false,
  });

  @override
  List<Object?> get props => [field, isAscending];
}

class SendCommentInAllTasksEvent extends TasksEvent {
  final String userEmail;
  final String referenceType;
  final String comment;
  final String taskId;

  SendCommentInAllTasksEvent({
    required this.referenceType,
    required this.comment,
    required this.taskId,
    required this.userEmail,
  });

  @override
  List<Object?> get props => [referenceType, comment, taskId, userEmail];
}

class SendNotificationEvent extends TasksEvent {
  final List<String> title;
  final String message;
  final List<String> fcmToken;
  final String userId;

  SendNotificationEvent({
    required this.title,
    required this.message,
    required this.fcmToken,
    required this.userId,
  });

  @override
  List<Object?> get props => [title, message, fcmToken, userId];
}

class GetAllProjectsEvent extends TasksEvent {}

class ChangeTaskStatus extends TasksEvent {
  final String taksId;
  final String taskStatus;

  ChangeTaskStatus({
    required this.taksId,
    required this.taskStatus,
  });

  @override
  List<Object?> get props => [taksId, taskStatus];
}

class CreateTaskEvent extends TasksEvent {
  //
  final String selectedProjectId;
  final String selectedProjectName;
  final String selectedPriority;
  final String selectedStatus;
  final String selectedTime;
  final String subject;
  final String description;
  final List<String> userRoles;

  CreateTaskEvent({
    required this.selectedProjectId,
    required this.selectedProjectName,
    required this.selectedPriority,
    required this.selectedStatus,
    required this.selectedTime,
    required this.subject,
    required this.description,
    required this.userRoles,
  });

  @override
  List<Object?> get props => [
        selectedProjectId,
        selectedProjectName,
        selectedPriority,
        selectedStatus,
        selectedTime,
        subject,
        description,
        userRoles,
      ];
}

class AssignTaskEvent extends TasksEvent {
  //
  final String taskId;
  final List<String> usersEmail;
  final String description;
  final String assignedDate;
  final String assignedBy;

  AssignTaskEvent({
    required this.taskId,
    required this.usersEmail,
    required this.description,
    required this.assignedDate,
    required this.assignedBy,
  });

  @override
  List<Object?> get props =>
      [taskId, usersEmail, description, assignedDate, assignedBy];
}
