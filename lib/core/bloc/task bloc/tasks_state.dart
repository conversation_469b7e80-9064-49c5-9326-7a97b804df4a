part of 'tasks_bloc.dart';

abstract class TasksState extends Equatable {
  @override
  List<Object?> get props => [];
}

class TasksStateInitial extends TasksState {}

class TasksStateLoading extends TasksState {}

class TasksStateAllTasksLoading extends TasksState {}

class TasksStateAssignedTasksLoading extends TasksState {}

class TasksStateFiltering extends TasksState {}

class TasksStateProjectLoading extends TasksState {}

class TasksStateLoaded extends TasksState {}

class TasksStateAllTasksLoaded extends TasksState {
  final List<TaskModel> tasks;

  TasksStateAllTasksLoaded({required this.tasks});

  @override
  List<Object?> get props => [tasks];
}

class TasksStateAssignedTasksLoaded extends TasksState {
  final List<AssignedTasksModel> assignedTasks;

  TasksStateAssignedTasksLoaded({
    required this.assignedTasks,
  });

  @override
  List<Object?> get props => [assignedTasks];
}

class TasksStateCommentsLoading extends TasksState {}

class TasksStateError extends TasksState {
  final AppFailure appFailure;

  TasksStateError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class TasksStateAllTasksError extends TasksState {
  final AppFailure appFailure;

  TasksStateAllTasksError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class TasksStateAssignedTasksError extends TasksState {
  final AppFailure appFailure;

  TasksStateAssignedTasksError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class TasksStateSuccess extends TasksState {
  final String message;

  TasksStateSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}

class TasksStateNotitifcationLoading extends TasksState {}

class TasksStateNotificationError extends TasksState {
  final AppFailure appFailure;

  TasksStateNotificationError({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

class TasksStateNotificationSuccess extends TasksState {
  final String message;

  TasksStateNotificationSuccess({required this.message});

  @override
  List<Object?> get props => [message];
}
