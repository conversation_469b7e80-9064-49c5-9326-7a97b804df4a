part of 'sales_invoice_bloc.dart';

sealed class SalesInvoiceState extends Equatable {
  const SalesInvoiceState();

  @override
  List<Object> get props => [];
}

final class SalesInvoiceInitial extends SalesInvoiceState {}

final class SalesInvoiceLoading extends SalesInvoiceState {}

final class SalesInvoiceLoaded extends SalesInvoiceState {
  final List<SalesInvoiceModel> salesInvoices;

  const SalesInvoiceLoaded({required this.salesInvoices});

  @override
  List<Object> get props => [salesInvoices];
}

final class SalesInvoiceError extends SalesInvoiceState {
  final AppFailure appFailure;

  const SalesInvoiceError({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

final class SalesInvoiceUpdateLoading extends SalesInvoiceState {}

final class SalesInvoiceUpdatedSuccessfully extends SalesInvoiceState {
  final String message;

  const SalesInvoiceUpdatedSuccessfully({required this.message});

  @override
  List<Object> get props => [message];
}

final class SalesInvoiceUpdateError extends SalesInvoiceState {
  final AppFailure appFailure;

  const SalesInvoiceUpdateError({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}

final class SalesInvoicePdfLoading extends SalesInvoiceState {
  final String salesInvoiceId;

  const SalesInvoicePdfLoading({required this.salesInvoiceId});

  @override
  List<Object> get props => [salesInvoiceId];
}

final class SalesInvoicePdfLoaded extends SalesInvoiceState {
  final Uint8List pdfBytes;

  const SalesInvoicePdfLoaded({required this.pdfBytes});
}

final class SalesInvoicePdfError extends SalesInvoiceState {
  final AppFailure appFailure;

  const SalesInvoicePdfError({required this.appFailure});

  @override
  List<Object> get props => [appFailure];
}
