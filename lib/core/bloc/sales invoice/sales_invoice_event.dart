part of 'sales_invoice_bloc.dart';

sealed class SalesInvoiceEvent extends Equatable {
  const SalesInvoiceEvent();

  @override
  List<Object> get props => [];
}

class GetSalesInvoices extends SalesInvoiceEvent {
  final List<String> userRoles;

  const GetSalesInvoices({required this.userRoles});

  @override
  List<Object> get props => [userRoles];
}

class UpdateSalesInvoiceWorkState extends SalesInvoiceEvent {
  final String salesInvoiceId;
  final ApprovalDecision approvalDecision;
  final List<String> userRoles;

  const UpdateSalesInvoiceWorkState({
    required this.salesInvoiceId,
    required this.approvalDecision,
    required this.userRoles,
  });

  @override
  List<Object> get props => [salesInvoiceId, approvalDecision, userRoles];
}

class GetSalesInvoicePdf extends SalesInvoiceEvent {
  final String salesInvoiceId;

  const GetSalesInvoicePdf({required this.salesInvoiceId});

  @override
  List<Object> get props => [salesInvoiceId];
}
