import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:rasiin_tasks_app/core/enums/approval_decission.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/sales_invoice_model.dart';
import 'package:rasiin_tasks_app/core/repository/sales_invoice_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'sales_invoice_event.dart';
part 'sales_invoice_state.dart';

class SalesInvoiceBloc extends Bloc<SalesInvoiceEvent, SalesInvoiceState> {
  final SalesInvoiceRepository salesInvoiceRepository;

  SalesInvoiceBloc({required this.salesInvoiceRepository})
      : super(SalesInvoiceInitial()) {
    on<GetSalesInvoices>(
      _onGetSalesInvoices,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 200),
      ),
    );
    on<UpdateSalesInvoiceWorkState>(
      _onUpdateSalesInvoiceWorkState,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 200),
      ),
    );
    on<GetSalesInvoicePdf>(
      _onGetSalesInvoicePdf,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
  }

  List<SalesInvoiceModel> _salesInvoices = [];
  List<SalesInvoiceModel> get salesInvoices => _salesInvoices;

  Uint8List _salesInvoicePdfBytes = Uint8List.fromList([]);
  Uint8List get salesInvoicePdfBytes => _salesInvoicePdfBytes;

  void _onGetSalesInvoices(
      GetSalesInvoices event, Emitter<SalesInvoiceState> emit) async {
    emit(SalesInvoiceLoading());
    final result = await salesInvoiceRepository.getSalesInvoices(
      userRoles: event.userRoles,
    );
    result.fold(
      (failure) {
        _salesInvoices.clear();
        emit(SalesInvoiceError(appFailure: failure));
      },
      (salesInvoices) {
        _salesInvoices = salesInvoices;
        emit(SalesInvoiceLoaded(salesInvoices: salesInvoices));
      },
    );
  }

  void _onUpdateSalesInvoiceWorkState(UpdateSalesInvoiceWorkState event,
      Emitter<SalesInvoiceState> emit) async {
    emit(SalesInvoiceUpdateLoading());
    final result = await salesInvoiceRepository.updateSalesInvoiceWorkState(
      salesInvoiceId: event.salesInvoiceId,
      approvalDecision: event.approvalDecision,
      userRoles: event.userRoles,
    );
    result.fold(
      (failure) => emit(SalesInvoiceUpdateError(appFailure: failure)),
      (message) => emit(SalesInvoiceUpdatedSuccessfully(message: message)),
    );
  }

  void _onGetSalesInvoicePdf(
      GetSalesInvoicePdf event, Emitter<SalesInvoiceState> emit) async {
    emit(SalesInvoicePdfLoading(
      salesInvoiceId: event.salesInvoiceId,
    ));
    final result = await salesInvoiceRepository.getSalesInvoicePdf(
      salesInvoiceId: event.salesInvoiceId,
    );
    result.fold(
      (failure) {
        emit(SalesInvoicePdfError(appFailure: failure));
      },
      (pdfBytes) {
        _salesInvoicePdfBytes = pdfBytes;
        emit(SalesInvoicePdfLoaded(pdfBytes: pdfBytes));
      },
    );
  }
}
