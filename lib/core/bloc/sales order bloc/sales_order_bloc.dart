import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/sales_order_model.dart';
import 'package:rasiin_tasks_app/core/repository/sales_order_repository.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'sales_order_event.dart';
part 'sales_order_state.dart';

class SalesOrderBloc extends Bloc<SalesOrderEvent, SalesOrderState> {
  final SalesOrderRepository salesOrderRepository;

  SalesOrderBloc({required this.salesOrderRepository})
      : super(SalesOrderInitial()) {
    on<GetAllSalesOrders>(
      _onGetAllSalesOrders,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<SalesOrderModel> _salesOrders = [];
  List<SalesOrderModel> get salesOrders => _salesOrders;

  Future<void> _onGetAllSalesOrders(
    GetAllSalesOrders event,
    Emitter<SalesOrderState> emit,
  ) async {
    emit(SalesOrderLoading());

    final result = await salesOrderRepository.getAllSalesOrders(
      userRoles: event.userRoles,
    );

    result.fold(
      (failure) {
        _salesOrders.clear();
        emit(SalesOrderError(appFailure: failure));
      },
      (salesOrders) {
        if (salesOrders.isEmpty) {
          emit(SalesOrderEmpty());
        } else {
          _salesOrders = salesOrders;
          emit(SalesOrderLoaded(salesOrders: salesOrders));
        }
      },
    );
  }
}
