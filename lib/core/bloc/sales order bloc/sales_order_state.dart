part of 'sales_order_bloc.dart';

sealed class SalesOrderState extends Equatable {
  const SalesOrderState();

  @override
  List<Object> get props => [];
}

final class SalesOrderInitial extends SalesOrderState {}

final class SalesOrderLoading extends SalesOrderState {}

final class SalesOrderLoaded extends SalesOrderState {
  final List<SalesOrderModel> salesOrders;

  const SalesOrderLoaded({required this.salesOrders});

  @override
  List<Object> get props => [salesOrders];
}

final class SalesOrderError extends SalesOrderState {
  final AppFailure appFailure;

  const SalesOrderError({required this.appFailure});
}

final class SalesOrderEmpty extends SalesOrderState {}
