import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/payroll_model.dart';
import 'package:rasiin_tasks_app/core/repository/payroll_repository.dart';

import 'package:equatable/equatable.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'payroll_event.dart';
part 'payroll_state.dart';

class PayrollBloc extends Bloc<PayrollEvent, PayrollState> {
  final PayrollRepository payrollRepository;

  PayrollBloc({required this.payrollRepository})
      : super(PayrollInitialState()) {
    on<PayrollFetchEvent>(
      _onPayrollFetchEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 500),
      ),
    );
    on<PayrollFilterEvent>(
      _onPayrollFilterEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<PayrollDownloadPdfEvent>(
      _onPayrollDownloadPdfEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
    on<PayrollFetchPdfEvent>(
      _onPayrollFetchPdfEvent,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 100),
      ),
    );
  }

  List<PayrollModel> _payrolls = [];
  List<PayrollModel> _filteredPayrolls = [];
  List<PayrollModel> get payrolls => _filteredPayrolls;

  FutureOr<void> _onPayrollFetchEvent(
      PayrollFetchEvent event, Emitter<PayrollState> emit) async {
    print("🔄 PayrollBloc: Starting fetch event");
    emit(PayrollLoadingState());
    print('📥 Fetching payrolls for employee: ${event.employee}');

    final result = await payrollRepository.getAllPayrolls(
      employee: event.employee,
      userRoles: event.userRoles,
      forceFetch: event.forceFetch,
    );
    await result.fold(
      (failure) {
        print('❌ Fetch failed: ${failure.message}');
        print("❌ PayrollBloc: Fetch failed - ${failure.message}");
        _payrolls.clear();
        _filteredPayrolls.clear();
        emit(PayrollErrorState(appFailure: failure));
      },
      (success) {
        print('✅ Fetch successful: ${success.length} payrolls loaded');
        for (var payroll in success) {
          print(
              '📄 Payroll: ${payroll.employeeName} - ${payroll.postingDate} - \$${payroll.grossPay}');
        }
        print(
            "✅ PayrollBloc: Fetch success - ${success.length} payrolls received");
        _payrolls.clear();
        _filteredPayrolls.clear();
        _payrolls = success;
        _filteredPayrolls = List.from(_payrolls);
        print("📊 PayrollBloc: _payrolls length: ${_payrolls.length}");
        print(
            "📊 PayrollBloc: _filteredPayrolls length: ${_filteredPayrolls.length}");
        emit(PayrollLoadedState(payrolls: _filteredPayrolls));
      },
    );
  }

  FutureOr<void> _onPayrollFilterEvent(
      PayrollFilterEvent event, Emitter<PayrollState> emit) async {
    print(
        '🔍 Filtering payrolls for year: ${event.year}, month: ${event.month.name} (${event.month.monthNumber})');
    print('📊 Total payrolls before filtering: ${_payrolls.length}');

    print(
        "🔍 PayrollBloc: Starting filter - month: ${event.month.name}, year: ${event.year}");
    print(
        "📊 PayrollBloc: Current _payrolls length before filter: ${_payrolls.length}");

    final result = await payrollRepository.filterPayroll(
      year: event.year,
      month: event.month,
    );

    result.fold(
      (failure) {
        print('❌ Filter failed: ${failure.message}');
        print("❌ PayrollBloc: Filter failed - ${failure.message}");
        _filteredPayrolls.clear();
        emit(PayrollErrorState(appFailure: failure));
      },
      (success) {
        print('✅ Filter successful: ${success.length} payrolls found');
        print(
            "✅ PayrollBloc: Filter success - ${success.length} payrolls found");
        _filteredPayrolls = success;
        emit(PayrollFilteredState(payrolls: _filteredPayrolls));
      },
    );
  }

  FutureOr<void> _onPayrollDownloadPdfEvent(
      PayrollDownloadPdfEvent event, Emitter<PayrollState> emit) async {
    emit(PayrollDownloadPdfLaodingState());
    final result = await payrollRepository.downloadPayrollPdf(
      employeeId: event.employeeId,
      bytes: event.bytes,
    );
    result.fold(
      (failure) {
        emit(PayrollDownloadPdfErrorState(appFailure: failure));
      },
      (success) {
        emit(PayrollDownloadPdfSuccessState(
            message: "PDF downloaded successfully"));
      },
    );
  }

  FutureOr<void> _onPayrollFetchPdfEvent(
      PayrollFetchPdfEvent event, Emitter<PayrollState> emit) async {
    emit(PayrollFetchPdfLoadingState());
    final result = await payrollRepository.getPayrollPdf(
      payrollId: event.payrollId,
    );
    result.fold(
      (failure) {
        emit(PayrollFetchPdfErrorState(appFailure: failure));
      },
      (bytes) {
        emit(PayrollFetchPdfSuccessState(pdfBytes: bytes));
      },
    );
  }
}
