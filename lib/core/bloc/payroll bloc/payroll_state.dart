part of 'payroll_bloc.dart';

abstract class PayrollState extends Equatable {
  @override
  List<Object?> get props => [];
}

class PayrollInitialState extends PayrollState {}

class PayrollLoadingState extends PayrollState {}

class PayrollLoadedState extends PayrollState {
  final List<PayrollModel> payrolls;

  PayrollLoadedState({required this.payrolls});

  @override
  List<Object?> get props => [payrolls];
}

class PayrollFilteredState extends PayrollState {
  final List<PayrollModel> payrolls;

  PayrollFilteredState({required this.payrolls});

  @override
  List<Object?> get props => [payrolls];
}

class PayrollErrorState extends PayrollState {
  final AppFailure appFailure;

  PayrollErrorState({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

// download pdf
class PayrollDownloadPdfLaodingState extends PayrollState {}

class PayrollDownloadPdfSuccessState extends PayrollState {
  final String message;

  PayrollDownloadPdfSuccessState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class PayrollDownloadPdfErrorState extends PayrollState {
  final AppFailure appFailure;

  PayrollDownloadPdfErrorState({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

// fetch pdf
class PayrollFetchPdfLoadingState extends PayrollState {}

class PayrollFetchPdfSuccessState extends PayrollState {
  final Uint8List pdfBytes;

  PayrollFetchPdfSuccessState({
    required this.pdfBytes,
  });

  @override
  List<Object?> get props => [pdfBytes];
}

class PayrollFetchPdfErrorState extends PayrollState {
  final AppFailure appFailure;

  PayrollFetchPdfErrorState({
    required this.appFailure,
  });

  @override
  List<Object?> get props => [appFailure];
}

// share pdf
class PayrollSharePdfLoadingState extends PayrollState {}

class PayrollSharePdfSuccessState extends PayrollState {
  final String message;

  PayrollSharePdfSuccessState({
    required this.message,
  });

  @override
  List<Object?> get props => [message];
}

class PayrollSharePdfErrorState extends PayrollState {
  final AppFailure appFailure;

  PayrollSharePdfErrorState({
    required this.appFailure,
  });
}
