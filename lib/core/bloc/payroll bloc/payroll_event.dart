part of 'payroll_bloc.dart';

abstract class PayrollEvent extends Equatable {
  @override
  List<Object?> get props => [];
}

class PayrollFetchEvent extends PayrollEvent {
  final String employee;
  final List<String> userRoles;
  final bool forceFetch;

  PayrollFetchEvent({
    required this.employee,
    required this.userRoles,
    this.forceFetch = false,
  });

  @override
  List<Object?> get props => [employee, userRoles, forceFetch];
}

class PayrollFilterEvent extends PayrollEvent {
  final Month month;
  final int year;

  PayrollFilterEvent({required this.month, required this.year});

  @override
  List<Object?> get props => [month, year];
}

class PayrollDownloadPdfEvent extends PayrollEvent {
  final String employeeId;
  final Uint8List bytes;

  PayrollDownloadPdfEvent({
    required this.employeeId,
    required this.bytes,
  });

  @override
  List<Object?> get props => [employeeId, bytes];
}

class PayrollFetchPdfEvent extends PayrollEvent {
  final String payrollId;

  PayrollFetchPdfEvent({required this.payrollId});

  @override
  List<Object?> get props => [payrollId];
}
