part of 'expense_bloc.dart';

abstract class ExpenseState {}

class ExpenseInitialState extends ExpenseState {}

class ExpenseLoadingState extends ExpenseState {}

class ExpenseCreationLoadingState extends ExpenseState {}

class ExpenseFilteredState extends ExpenseState {
  final List<CreateExpenseParams> filteredExpenses;

  ExpenseFilteredState({required this.filteredExpenses});
}

class ExpenseLoadedState extends ExpenseState {
  final List<ExpenseModel> expenses;

  ExpenseLoadedState({required this.expenses});
}

class ExpenseTypeLoadedState extends ExpenseState {
  final List<DepartmentModel> departments;

  ExpenseTypeLoadedState({required this.departments});
}

class ExpenseErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseErrorState({
    required this.appFailure,
  });
}

class ExpenseSuccessState extends ExpenseState {
  final String message;

  ExpenseSuccessState({required this.message});
}

class ExpenseCreationSuccessState extends ExpenseState {
  final String message;

  ExpenseCreationSuccessState({required this.message});
}

class ExpenseCreationErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseCreationErrorState({required this.appFailure});
}

class ExpenseApprovalLoadingState extends ExpenseState {}

class ExpenseApprovalSuccessState extends ExpenseState {
  final String message;

  ExpenseApprovalSuccessState({required this.message});
}

class ExpenseApprovalErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseApprovalErrorState({required this.appFailure});
}

class ExpenseAccountsLoadingState extends ExpenseState {}

class ExpenseAccountsLoadedState extends ExpenseState {
  final List<AccountModel> accounts;

  ExpenseAccountsLoadedState({required this.accounts});
}

class ExpenseAccountsErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseAccountsErrorState({required this.appFailure});
}

class ExpenseModificationLoadingState extends ExpenseState {}

class ExpenseModificationSuccessState extends ExpenseState {
  final String message;

  ExpenseModificationSuccessState({required this.message});
}

class ExpenseModificationErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseModificationErrorState({required this.appFailure});
}

class ExpenseCostCentersLoadingState extends ExpenseState {}

class ExpenseCostCentersLoadedState extends ExpenseState {
  final List<CostCenterModel> costCenters;

  ExpenseCostCentersLoadedState({required this.costCenters});
}

class ExpenseCostCentersErrorState extends ExpenseState {
  final AppFailure appFailure;

  ExpenseCostCentersErrorState({required this.appFailure});
}
