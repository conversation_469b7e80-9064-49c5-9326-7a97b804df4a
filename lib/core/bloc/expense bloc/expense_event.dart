part of 'expense_bloc.dart';

abstract class ExpenseEvent {}

class AddExpense extends ExpenseEvent {
  final CreateExpenseParams expense;

  AddExpense({required this.expense});
}

class DeleteExpense extends ExpenseEvent {
  final CreateExpenseParams expense;

  DeleteExpense({required this.expense});
}

class UpdateExpense extends ExpenseEvent {
  final CreateExpenseParams expense;

  UpdateExpense({required this.expense});
}


class CreateExpense extends ExpenseEvent {
  final List<CreateExpenseParams> createExpenseParams;

  CreateExpense({required this.createExpenseParams});
}

class GetAllExpense extends ExpenseEvent {
  final List<String> userRoles;
  final String userEmail;

  GetAllExpense({
    required this.userRoles,
    required this.userEmail,
  });
}

class GetAllExpenseDepartments extends ExpenseEvent {}

class ApproveExpense extends ExpenseEvent {
  final String expenseId;
  final List<String> userRoles;

  ApproveExpense({
    required this.expenseId,
    required this.userRoles,
  });
}

class GetAllExpenseAccounts extends ExpenseEvent {}

class ModifyExpense extends ExpenseEvent {
  final String expenseId;
  final String account;
  final String paidFrom;
  final List<String> userRoles;
  final String costCenter;
  final String remark;

  ModifyExpense({
    required this.expenseId,
    required this.account,
    required this.paidFrom,
    required this.userRoles,
    required this.costCenter,
    required this.remark,
  });
}

class GetAllExpenseCostCenters extends ExpenseEvent {}
