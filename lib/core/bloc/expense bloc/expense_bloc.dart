import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/account_model.dart';
import 'package:rasiin_tasks_app/core/models/cost_center_model.dart'
    show CostCenterModel;
import 'package:rasiin_tasks_app/core/models/expense_model.dart';
import 'package:rasiin_tasks_app/core/models/department_model.dart';
import 'package:rasiin_tasks_app/core/repository/expense_repository.dart';
import 'package:rasiin_tasks_app/core/params/create_expense_params.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/debounce.dart';

part 'expense_event.dart';
part 'expense_state.dart';

class ExpenseBloc extends Bloc<ExpenseEvent, ExpenseState> {
  final ExpenseRepository expenseRepository;
  ExpenseBloc({
    required this.expenseRepository,
  }) : super(ExpenseInitialState()) {
    on<GetAllExpenseDepartments>(
      _onGetAllExpenseDepartments,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllExpense>(
      _onGetAllExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<CreateExpense>(
      _onCreateExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<AddExpense>(
      _onAddExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<UpdateExpense>(
      _onUpdateExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<DeleteExpense>(
      _onDeleteExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );

    on<ApproveExpense>(
      _onApproveExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllExpenseAccounts>(
      _onGetAllExpenseAccounts,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<ModifyExpense>(
      _onModifyExpense,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
    on<GetAllExpenseCostCenters>(
      _onGetAllExpenseCostCenters,
      transformer: DebounceHelper.blocDebouncer(
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  List<CreateExpenseParams> _createdExpenses = [];
  List<CreateExpenseParams> get createdExpenses => _createdExpenses;

  List<ExpenseModel> _expenses = [];
  List<ExpenseModel> get expenses => _expenses;

  List<DepartmentModel> _departments = [];
  List<DepartmentModel> get departments => _departments;

  List<AccountModel> _accounts = [];
  List<AccountModel> get accounts => _accounts;

  List<CostCenterModel> _costCenters = [];
  List<CostCenterModel> get costCenters => _costCenters;

  FutureOr<void> _onAddExpense(
      AddExpense event, Emitter<ExpenseState> emit) async {
    final result = await expenseRepository.addExpense(
      _createdExpenses,
      event.expense,
    );
    _createdExpenses = result;
    emit(ExpenseFilteredState(filteredExpenses: _createdExpenses));
  }

  FutureOr<void> _onUpdateExpense(
      UpdateExpense event, Emitter<ExpenseState> emit) async {
    final result = await expenseRepository.updateExpense(
      _createdExpenses,
      event.expense,
    );
    _createdExpenses = result;
    emit(ExpenseFilteredState(filteredExpenses: _createdExpenses));
  }

  FutureOr<void> _onDeleteExpense(
      DeleteExpense event, Emitter<ExpenseState> emit) async {
    final result = await expenseRepository.deleteExpense(
      _createdExpenses,
      event.expense,
    );
    _createdExpenses = result;
    emit(ExpenseFilteredState(filteredExpenses: _createdExpenses));
  }

  FutureOr<void> _onCreateExpense(
      CreateExpense event, Emitter<ExpenseState> emit) async {
    emit(ExpenseCreationLoadingState());
    //
    final response = await expenseRepository.createExpense(
      createExpenseParams: event.createExpenseParams,
    );
    response.fold(
      (failure) {
        //
        emit(
          ExpenseCreationErrorState(appFailure: failure),
        );
      },
      (success) {
        //
        emit(ExpenseCreationSuccessState(message: success));
      },
    );
  }

  FutureOr<void> _onGetAllExpense(
      GetAllExpense event, Emitter<ExpenseState> emit) async {
    emit(ExpenseLoadingState());
    final response = await expenseRepository.getAllExpenses(
      userRoles: event.userRoles,
      userEmail: event.userEmail,
    );
    response.fold(
      (failure) {
        //
        _expenses.clear();
        emit(ExpenseErrorState(appFailure: failure));
      },
      (success) {
        //
        _expenses = success;
        emit(ExpenseLoadedState(expenses: success));
      },
    );
  }

  FutureOr<void> _onGetAllExpenseDepartments(
      GetAllExpenseDepartments event, Emitter<ExpenseState> emit) async {
    emit(ExpenseLoadingState());
    final response = await expenseRepository.getAllDepartments();
    response.fold(
      (failure) {
        //
        _departments.clear();
        emit(ExpenseErrorState(appFailure: failure));
      },
      (dep) {
        //
        _departments = dep;
        emit(ExpenseTypeLoadedState(departments: dep));
      },
    );
  }

  FutureOr<void> _onApproveExpense(
      ApproveExpense event, Emitter<ExpenseState> emit) async {
    emit(ExpenseApprovalLoadingState());
    final response = await expenseRepository.approveExpense(
      expenseId: event.expenseId,
      userRoles: event.userRoles,
    );
    response.fold(
      (failure) {
        //
        emit(ExpenseApprovalErrorState(appFailure: failure));
      },
      (success) {
        //
        emit(ExpenseApprovalSuccessState(message: success));
      },
    );
  }

  FutureOr<void> _onGetAllExpenseAccounts(
      GetAllExpenseAccounts event, Emitter<ExpenseState> emit) async {
    emit(ExpenseAccountsLoadingState());
    final response = await expenseRepository.getAllAccounts();
    response.fold(
      (failure) {
        //
        _accounts.clear();
        emit(ExpenseAccountsErrorState(appFailure: failure));
      },
      (acc) {
        //
        _accounts = acc;
        emit(ExpenseAccountsLoadedState(accounts: acc));
      },
    );
  }

  FutureOr<void> _onModifyExpense(
      ModifyExpense event, Emitter<ExpenseState> emit) async {
    emit(ExpenseModificationLoadingState());
    final response = await expenseRepository.modifyExpense(
      expenseId: event.expenseId,
      account: event.account,
      paidFrom: event.paidFrom,
      userRoles: event.userRoles,
      costCenter: event.costCenter,
      remark: event.remark,
    );
    response.fold(
      (failure) {
        //
        emit(ExpenseModificationErrorState(appFailure: failure));
      },
      (success) {
        //
        emit(ExpenseModificationSuccessState(message: success));
      },
    );
  }

  FutureOr<void> _onGetAllExpenseCostCenters(
      GetAllExpenseCostCenters event, Emitter<ExpenseState> emit) async {
    emit(ExpenseCostCentersLoadingState());
    final response = await expenseRepository.getAllCostCenters();
    response.fold(
      (failure) {
        //
        _costCenters.clear();
        emit(ExpenseCostCentersErrorState(appFailure: failure));
      },
      (costCentr) {
        //
        _costCenters = costCentr;
        emit(ExpenseCostCentersLoadedState(costCenters: costCentr));
      },
    );
  }
}
