import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class AssignedTasksModel {
  int id = 0;

  final String referenceType;
  final String assignedBy;
  final String referenceName;
  final String allocatedTo;
  final String description;

  final String date;
  final String priority;
  final String fullName;
  final String status;
  final String name;
  final int commentCount;

  //
  String userEmail;

  AssignedTasksModel({
    required this.referenceType,
    required this.assignedBy,
    required this.referenceName,
    required this.allocatedTo,
    required this.description,
    required this.date,
    required this.priority,
    required this.fullName,
    required this.status,
    required this.name,
    required this.userEmail,
    required this.commentCount,
  });

  factory AssignedTasksModel.fromJson(Map<String, dynamic> map) {
    try {
      return AssignedTasksModel(
        referenceType: map['reference_type'] ?? '',
        assignedBy: map['assigned_by'] ?? '',
        referenceName: map['reference_name'] ?? '',
        allocatedTo: map['allocated_to'] ?? '',
        description: map['description'] ?? '',
        date: map['date'] ?? '',
        priority: map['priority'] ?? '',
        fullName: map['full_name'] ?? '',
        status: map['status'] ?? '',
        name: map['name'] ?? '',
        userEmail: map['user_email'] ?? '',
        commentCount:
            int.tryParse(map['comment_count']?.toString() ?? '0') ?? 0,
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse AssignedTasksModel from json: $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse AssignedTasksModel from json: $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AssignedTasksModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AssignedTasksModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => AssignedTasksModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'reference_type': referenceType,
      'assigned_by': assignedBy,
      'reference_name': referenceName,
      'allocated_to': allocatedTo,
      'description': description,
      'date': date,
      'priority': priority,
      'full_name': fullName,
      'status': status,
      'name': name,
    };
  }
}
