import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';

class AppointmentModel {
  final String id;
  final String patient;
  final String patientName;
  final String practitioner;
  final double payableAmount;
  final DateTime creation;
  final String appointmentSource;

  AppointmentModel({
    required this.id,
    required this.patient,
    required this.patientName,
    required this.practitioner,
    required this.payableAmount,
    required this.creation,
    required this.appointmentSource,
  });

  // Get formatted date
  String get formattedDate => creation.toFormattedString();

  // Get formatted time
  String get formattedTime => creation.toTimeString();

  // Get formatted amount
  String get formattedAmount => payableAmount.toMoneyString();

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    try {
      return AppointmentModel(
        id: json['name']?.toString() ?? '',
        patient: json['patient']?.toString() ?? '',
        patientName: json['patient_name']?.toString() ?? '',
        practitioner: json['practitioner']?.toString() ?? '',
        payableAmount:
            double.tryParse(json['payable_amount']?.toString() ?? '0') ?? 0.0,
        creation: DateTime.tryParse(json['creation']?.toString() ?? '') ??
            DateTime.now(),
        appointmentSource: json['appointment_source']?.toString() ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing appointment model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing appointment model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AppointmentModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<AppointmentModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => AppointmentModel.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'name': id,
      'patient': patient,
      'patient_name': patientName,
      'practitioner': practitioner,
      'payable_amount': payableAmount,
      'creation': creation.toIso8601String(),
      'appointment_source': appointmentSource,
    };
  }
}
