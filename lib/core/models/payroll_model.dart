import 'package:objectbox/objectbox.dart';

@Entity()
class PayrollModel {
  @Id()
  int id = 0;

  final String payrollId;
  final String employeeId;
  final String employeeName;
  @Property(type: PropertyType.date)
  DateTime postingDate;
  @Property(type: PropertyType.date)
  DateTime startDate;
  @Property(type: PropertyType.date)
  DateTime endDate;
  final double grossPay;
  final double totalDeduction;
  final double netPay;

  final earnings = ToMany<Earning>();
  final deductions = ToMany<Deduction>();

  PayrollModel({
    required this.payrollId,
    required this.employeeId,
    required this.employeeName,
    required this.postingDate,
    required this.startDate,
    required this.endDate,
    required this.grossPay,
    required this.totalDeduction,
    required this.netPay,
  });

  factory PayrollModel.fromJson(Map<String, dynamic> json) {
    final grossPay = (json['gross_pay'] as num?)?.toDouble() ?? 0;
    final totalDeduction = (json['total_deduction'] as num?)?.toDouble() ?? 0;
    final netPay =
        (json['net_pay'] as num?)?.toDouble() ?? (grossPay - totalDeduction);

    final payroll = PayrollModel(
      payrollId: json['name'] ?? '',
      employeeId: json['employee'] ?? '',
      employeeName: json['employee_name'] ?? '',
      postingDate:
          DateTime.tryParse(json['posting_date'] ?? '') ?? DateTime.now(),
      startDate: DateTime.tryParse(json['start_date'] ?? '') ?? DateTime.now(),
      endDate: DateTime.tryParse(json['end_date'] ?? '') ?? DateTime.now(),
      grossPay: grossPay,
      totalDeduction: totalDeduction,
      netPay: netPay,
    );

    // print('🔍 Parsing payroll: $payroll');

    if (json['earnings'] != null && json['earnings'] is List) {
      payroll.earnings.addAll(Earning.fromJsonList(json['earnings']));
    }
    if (json['deductions'] != null && json['deductions'] is List) {
      payroll.deductions.addAll(Deduction.fromJsonList(json['deductions']));
    }

    return payroll;
  }

  static List<PayrollModel> fromJsonList(List<dynamic> jsonList) {
    print('🔍 Parsing payroll list: ${jsonList.length}');
    return jsonList.map((json) => PayrollModel.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': payrollId,
      'employee': employeeId,
      'employee_name': employeeName,
      'posting_date': postingDate.toIso8601String(),
      'start_date': startDate.toIso8601String(),
      'end_date': endDate.toIso8601String(),
      'gross_pay': grossPay,
      'total_deduction': totalDeduction,
      'net_pay': netPay,
      'earnings': earnings.map((e) => e.toJson()).toList(),
      'deductions': deductions.map((d) => d.toJson()).toList(),
    };
  }

  @override
  String toString() {
    return 'PayrollModel{'
        'id: $id, '
        'payrollId: "$payrollId", '
        'employeeId: "$employeeId", '
        'employeeName: "$employeeName", '
        'postingDate: "${postingDate.toIso8601String()}", '
        'startDate: "${startDate.toIso8601String()}", '
        'endDate: "${endDate.toIso8601String()}", '
        'grossPay: $grossPay, '
        'totalDeduction: $totalDeduction, '
        'netPay: $netPay, '
        'earnings: ${earnings.map((e) => e.toString()).toList()}, '
        'deductions: ${deductions.map((d) => d.toString()).toList()}'
        '}';
  }
}

@Entity()
class Earning {
  @Id()
  int id = 0;

  String salaryComponent;
  double amount;
  double defaultAmount;

  Earning({
    required this.salaryComponent,
    required this.amount,
    required this.defaultAmount,
  });

  factory Earning.fromJson(Map<String, dynamic> json) {
    return Earning(
      salaryComponent: json['salary_component'] ?? '',
      amount: (json['amount'] as num).toDouble(),
      defaultAmount: (json['default_amount'] as num).toDouble(),
    );
  }

  static List<Earning> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Earning.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salary_component': salaryComponent,
      'amount': amount,
      'default_amount': defaultAmount,
    };
  }

  @override
  String toString() {
    return 'Earning{'
        'id: $id, '
        'salaryComponent: "$salaryComponent", '
        'amount: $amount, '
        'defaultAmount: $defaultAmount'
        '}';
  }
}

@Entity()
class Deduction {
  @Id()
  int id = 0;

  String salaryComponent;
  double amount;
  double defaultAmount;

  Deduction({
    required this.salaryComponent,
    required this.amount,
    required this.defaultAmount,
  });

  factory Deduction.fromJson(Map<String, dynamic> json) {
    return Deduction(
      salaryComponent: json['salary_component'] ?? '',
      amount: (json['amount'] as num).toDouble(),
      defaultAmount: (json['default_amount'] as num).toDouble(),
    );
  }

  static List<Deduction> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => Deduction.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'salary_component': salaryComponent,
      'amount': amount,
      'default_amount': defaultAmount,
    };
  }

  @override
  String toString() {
    return 'Deduction{'
        'id: $id, '
        'salaryComponent: "$salaryComponent", '
        'amount: $amount, '
        'defaultAmount: $defaultAmount'
        '}';
  }
}
