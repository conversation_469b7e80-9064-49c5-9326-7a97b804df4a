import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class DepartmentModel {
  final String id;
  final String name;

  DepartmentModel({
    required this.id,
    required this.name,
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> map) {
    try {
      return DepartmentModel(
        id: map['name'] ?? '',
        name: map['department_name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse department model : $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse department model : $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: DepartmentModel,
        actualType: map.runtimeType,
      );
    }
  }

  static List<DepartmentModel> fromJsonList(List<dynamic> data) {
    return data
        .map((department) => DepartmentModel.fromJson(department))
        .toList();
  }
}
