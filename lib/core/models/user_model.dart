import 'dart:convert';
import 'package:objectbox/objectbox.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/role_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';

@Entity()
class UserModel {
  @Id()
  int id = 0;

  String employeeId;
  String employeeName;
  String username;

  String email;

  String gender;

  DateTime? birthDate;

  DateTime? dateOfJoining;

  String employeeNo;

  String fcmToken;

  String profileImage;

  // final List<RoleModel> roles;
  @Backlink()
  final roles = ToMany<RoleModel>();

  UserModel({
    required this.employeeId,
    required this.employeeName,
    required this.username,
    required this.email,
    required this.gender,
    this.birthDate,
    required this.employeeNo,
    required this.fcmToken,
    this.dateOfJoining,
    required this.profileImage,
    // required this.roles,
  });

  // Helper method to get role names
  // List<String> get roleNames => roles.map((role) => role.name).toList();
  @Transient()
  List<String> get roleNames => roles.map((role) => role.name).toList();

  // Parse date from string
  static DateTime? _parseDate(String? date) => date?.toDateTime();

  factory UserModel.fromJson(Map<String, dynamic> map) {
    try {
      final rolesField = map['roles'];

      final user = UserModel(
        employeeId: (map['id'] ?? map['name'] ?? '').toString().safeText,
        employeeName: (map['employee_name'] ?? map['full_name'] ?? '')
            .toString()
            .safeText,
        username: (map['username'] ?? '').toString().safeText,
        email:
            (map['prefered_email'] ?? map['email'] ?? '').toString().safeText,
        gender: (map['gender'] ?? '').toString().safeText,
        birthDate: _parseDate(map['date_of_birth']),
        dateOfJoining: _parseDate(map['date_of_joining']),
        employeeNo: (map['employee_number'] ?? '').toString().safeText,
        fcmToken: (map['token'] ?? map['user_token'] ?? '').toString().safeText,
        profileImage:
            (map['user_image'] ?? '' ?? map['image'] ?? '').toString().safeText,
        // roles: rolesField != null && rolesField is List
        //     ? RoleModel.fromJsonList(rolesField)
        //     : [],
      );

      if (rolesField != null && rolesField is List) {
        user.roles.addAll(RoleModel.fromJsonList(rolesField));
      }

      return user;
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing user model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing user model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: UserModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<UserModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => UserModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'employeeId': employeeId,
      'employeeName': employeeName,
      'username': username,
      'email': email,
      'gender': gender,
      'birthDate': birthDate,
      'employeeNo': employeeNo,
      'fcmToken': fcmToken,
      'dateOfJoining': dateOfJoining,
      'user_image': profileImage,
      'roles': roleNames, // Convert roles to list of names for API
    };
  }

  String toJson() => json.encode(toMap());
}
