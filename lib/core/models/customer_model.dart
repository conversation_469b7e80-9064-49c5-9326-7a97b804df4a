// import 'dart:convert';

// class CustomerModel {
//   final String customerName;

//   CustomerModel({
//     required this.customerName,
//   });

//   factory CustomerModel.fromMap(Map<String, dynamic> map) {
//     return CustomerModel(
//       customerName: map['customer_name'] ?? '',
//     );
//   }

//   static List<CustomerModel> fromMapList(List<dynamic> mapList) {
//     return mapList.map((map) => CustomerModel.fromMap(map)).toList();
//   }

//   Map<String, dynamic> toMap() {
//     return {
//       'customerName': customerName,
//     };
//   }

//   String toJson() => json.encode(toMap());

//   factory CustomerModel.fromJson(String source) =>
//       CustomerModel.fromMap(json.decode(source) as Map<String, dynamic>);
// }
