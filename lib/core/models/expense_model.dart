import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/account_model.dart';
import 'package:rasiin_tasks_app/core/models/cost_center_model.dart';
import 'package:rasiin_tasks_app/core/models/department_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';

class ExpenseModel {
  final String id;
  final String remark;
  final CostCenterModel? costCenter;
  final AccountModel? account;
  final AccountModel? paidFrom;
  final DepartmentModel? department;
  final String status;
  final double amount;
  final int docStatus;
  DateTime? date;

  ExpenseModel({
    required this.id,
    required this.remark,
    required this.status,
    required this.amount,
    required this.costCenter,
    required this.account,
    required this.paidFrom,
    required this.department,
    this.date,
    required this.docStatus,
  });

  // get formatted date
  String get formattedDate => date?.toFormattedString() ?? '';

  // get formatted amount
  String get formattedAmount => amount.toMoneyString();

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    try {
      return ExpenseModel(
        id: json['name'] ?? '',
        remark: json['remark'] ?? '',
        status: json['status'] ?? '',
        amount: json['amount'] ?? 0.0,
        date: json['date'] != null ? DateTime.tryParse(json['date']) : null,
        docStatus: json['docstatus'] ?? 0,
        // Handle cost_center - can be string or object
        costCenter: json['cost_center'] != null
            ? (json['cost_center'] is String
                ? CostCenterModel(
                    id: json['cost_center'], name: json['cost_center'])
                : CostCenterModel.fromJson(json['cost_center']))
            : null,

        // Handle account - can be string or object
        account: json['account'] != null
            ? (json['account'] is String
                ? AccountModel(id: json['account'], name: json['account'])
                : AccountModel.fromJson(json['account']))
            : null,

        // Handle paid_from - can be string or object
        paidFrom: json['paid_from'] != null
            ? (json['paid_from'] is String
                ? AccountModel(id: json['paid_from'], name: json['paid_from'])
                : AccountModel.fromJson(json['paid_from']))
            : null,

        // Handle department - check both 'department' and 'department_name'
        department: json['department'] != null
            ? (json['department'] is String
                ? DepartmentModel(
                    id: json['department'], name: json['department'])
                : DepartmentModel.fromJson(json['department']))
            : (json['department_name'] != null
                ? DepartmentModel(
                    id: json['department_name'], name: json['department_name'])
                : null),
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse expense model : $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse expense model : $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ExpenseModel,
        actualType: json.runtimeType,
      );
    }
  }

  static List<ExpenseModel> fromJsonList(List<dynamic> data) {
    return data.map((expense) => ExpenseModel.fromJson(expense)).toList();
  }
}
