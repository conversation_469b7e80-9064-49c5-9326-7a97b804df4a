import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class VisitTypeModel {
  final String name;
  final String visitType;

  VisitTypeModel({
    required this.name,
    required this.visitType,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'name': name,
      'visit_type': visitType,
    };
  }

  factory VisitTypeModel.fromJson(Map<String, dynamic> map) {
    try {
      return VisitTypeModel(
        name: map['name'] ?? '',
        visitType: map['visit_type'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "Error parsing visit type model from map : $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "Error parsing visit type model from map : $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: VisitTypeModel,
        actualType: map.runtimeType,
      );
    }
  }

  static List<VisitTypeModel> fromJsonList(List<dynamic> list) {
    return list.map((visit) => VisitTypeModel.fromJson(visit)).toList();
  }
}
