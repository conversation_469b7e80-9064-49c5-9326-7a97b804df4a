import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';

class SalesOrderModel {
  final String orderId;
  final String customerName;
  final String patientName;
  final DateTime? transactionDate;
  final double grandTotal;
  final String status;
  final String ipdStatus;
  final String salesOrderType;
  final String orderType;
  final String refPractioner;
  final double totalQty;
  final double total; // Changed from String to double
  final String deliveryStatus;
  final String billingStatus;

  SalesOrderModel({
    required this.orderId,
    required this.customerName,
    required this.patientName,
    required this.transactionDate,
    required this.grandTotal,
    required this.status,
    required this.ipdStatus,
    required this.salesOrderType,
    required this.orderType,
    required this.refPractioner,
    required this.totalQty,
    required this.total, // Changed from String to double
    required this.deliveryStatus,
    required this.billingStatus,
  });

  static DateTime? _handleDates(String? date) {
    return date?.toDateTime();
  }

  // get formatted transaction date
  String get formattedTransactionDate {
    return transactionDate?.toFormattedString() ?? '';
  }

  // get formatted grand total
  String get formattedGrandTotal {
    return grandTotal.toMoneyString();
  }

  // get formatted total
  String get formattedTotal {
    return total.toMoneyString();
  }

  factory SalesOrderModel.fromJson(Map<String, dynamic> json) {
    try {
      return SalesOrderModel(
        orderId: json['name'] ?? '',
        customerName: json['customer_name'] ?? '',
        patientName: json['patient_name'] ?? '',
        transactionDate: _handleDates(json['transaction_date']),
        grandTotal: double.tryParse(json['grand_total'].toString()) ?? 0,
        status: json['status'] ?? '',
        ipdStatus: json['ipd_status'] ?? '',
        salesOrderType: json['so_type'] ?? '',
        orderType: json['order_type'] ?? '',
        refPractioner: json['ref_practioner'] ?? '',
        totalQty: double.tryParse(json['total_qty'].toString()) ?? 0,
        total: double.tryParse(json['total'].toString()) ??
            0, // Changed from String to double
        deliveryStatus: json['delivery_status'] ?? '',
        billingStatus: json['billing_status'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing order model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing order model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SalesOrderModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<SalesOrderModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => SalesOrderModel.fromJson(json)).toList();
  }
}
