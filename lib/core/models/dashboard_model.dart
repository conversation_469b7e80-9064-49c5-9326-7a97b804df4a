  class DashboardModel {
    final PendingExpenses pendingExpenses;
    final AttendanceSummary attendanceSummary;
    final List<LeaveBalance> leaveBalances;
    final SalaryDetails? salaryDetails;

    DashboardModel({
      required this.pendingExpenses,
      required this.attendanceSummary,
      required this.leaveBalances,
      this.salaryDetails,
    });

    factory DashboardModel.fromJson(Map<String, dynamic> json) {
      return DashboardModel(
        pendingExpenses: PendingExpenses.fromJson(json['pending_expenses']),
        attendanceSummary: AttendanceSummary.fromJson(json['attendance_summary']),
        leaveBalances: List<LeaveBalance>.from(
            json['leave_balances'].map((x) => LeaveBalance.fromJson(x))),
        salaryDetails: json['salary_details'] != null
            ? SalaryDetails.fromJson(json['salary_details'])
            : null,
      );
    }

    Map<String, dynamic> toJson() => {
          'pending_expenses': pendingExpenses.toJson(),
          'attendance_summary': attendanceSummary.toJson(),
          'leave_balances':
              List<dynamic>.from(leaveBalances.map((x) => x.toJson())),
          'salary_details': salaryDetails?.toJson(),
        };
  }

  class PendingExpenses {
    final int totalPending;
    final LatestRequest? latestRequest;

    PendingExpenses({
      required this.totalPending,
      this.latestRequest,
    });

    factory PendingExpenses.fromJson(Map<String, dynamic> json) {
      return PendingExpenses(
        totalPending: json['total_pending'] ?? 0,
        latestRequest: json['latest_request'] != null
            ? LatestRequest.fromJson(json['latest_request'])
            : null,
      );
    }

    Map<String, dynamic> toJson() => {
          'total_pending': totalPending,
          'latest_request': latestRequest?.toJson(),
        };
  }

  class LatestRequest {
    final String expenseId;
    final String date;
    final String type;
    final String status;
    final double claimedAmount;
    final double approvedAmount;

    LatestRequest({
      required this.expenseId,
      required this.date,
      required this.type,
      required this.status,
      required this.claimedAmount,
      required this.approvedAmount,
    });

    factory LatestRequest.fromJson(Map<String, dynamic> json) {
      return LatestRequest(
        expenseId: json['expense_id'] ?? '',
        date: json['date'] ?? '',
        type: json['type'] ?? '',
        status: json['status'] ?? '',
        claimedAmount: (json['claimed_amount'] as num?)?.toDouble() ?? 0.0,
        approvedAmount: (json['approved_amount'] as num?)?.toDouble() ?? 0.0,
      );
    }

    Map<String, dynamic> toJson() => {
          'expense_id': expenseId,
          'date': date,
          'type': type,
          'status': status,
          'claimed_amount': claimedAmount,
          'approved_amount': approvedAmount,
        };
  }

  class AttendanceSummary {
    final int present;
    final int absent;
    final int onLeave;
    final int totalDays;

    AttendanceSummary({
      required this.present,
      required this.absent,
      required this.onLeave,
      required this.totalDays,
    });

    factory AttendanceSummary.fromJson(Map<String, dynamic> json) {
      return AttendanceSummary(
        present: json['present'] ?? 0,
        absent: json['absent'] ?? 0,
        onLeave: json['on_leave'] ?? 0,
        totalDays: json['total_days'] ?? 0,
      );
    }

    Map<String, dynamic> toJson() => {
          'present': present,
          'absent': absent,
          'on_leave': onLeave,
          'total_days': totalDays,
        };

    double get attendancePercentage =>
        totalDays > 0 ? (present / totalDays) * 100 : 0;

  }

  class LeaveBalance {
    final String leaveType;
    final int allocated;
    final int used;
    final int remaining;

    LeaveBalance({
      required this.leaveType,
      required this.allocated,
      required this.used,
      required this.remaining,
    });

    factory LeaveBalance.fromJson(Map<String, dynamic> json) {
      return LeaveBalance(
        leaveType: json['leave_type'] ?? '',
        allocated: json['allocated'] ?? 0,
        used: json['used'] ?? 0,
        remaining: json['remaining'] ?? 0,
      );
    }

    Map<String, dynamic> toJson() => {
          'leave_type': leaveType,
          'allocated': allocated,
          'used': used,
          'remaining': remaining,
        };
  }

  class SalaryDetails {
    final String salarySlipId;
    final String monthYear;
    final double grossPay;
    final double netPay;
    final String processedDate;
    final int workingDays;
    final double totalDeduction;

    SalaryDetails({
      required this.salarySlipId,
      required this.monthYear,
      required this.grossPay,
      required this.netPay,
      required this.processedDate,
      required this.workingDays,
      required this.totalDeduction,
    });

    factory SalaryDetails.fromJson(Map<String, dynamic> json) {
      return SalaryDetails(
        salarySlipId: json['salary_slip_id'] ?? '',
        monthYear: json['month_year'] ?? '',
        grossPay: (json['gross_pay'] as num?)?.toDouble() ?? 0.0,
        netPay: (json['net_pay'] as num?)?.toDouble() ?? 0.0,
        processedDate: json['processed_date'] ?? '',
        workingDays: json['working_days'] ?? 0,
        totalDeduction: (json['total_deduction'] as num?)?.toDouble() ?? 0.0,
      );
    }

    Map<String, dynamic> toJson() => {
          'salary_slip_id': salarySlipId,
          'month_year': monthYear,
          'gross_pay': grossPay,
          'net_pay': netPay,
          'processed_date': processedDate,
          'working_days': workingDays,
          'total_deduction': totalDeduction,
        };
  }
