import 'dart:convert';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class TaskModel {
  final String subj;
  final String projectId;
  final String projectName;
  final String desc;
  final String prior;
  final String dueDate;
  final String taskStatus;
  final String taskId;
  final String customerName;
  final String userEmail;
  final int commentCount;

  TaskModel({
    required this.subj,
    required this.projectId,
    required this.projectName,
    required this.desc,
    required this.prior,
    required this.dueDate,
    required this.taskStatus,
    required this.taskId,
    required this.customerName,
    required this.userEmail,
    required this.commentCount,
  });

  factory TaskModel.fromJson(Map<String, dynamic> map) {
    try {
      return TaskModel(
        subj: map['subject'] ?? '',
        projectId: map['project'] ?? '',
        projectName: '', // Not in API response
        desc: map['description'] ?? '',
        prior: map['priority'] ?? '',
        dueDate: map['act_start_date'] ?? '',
        taskStatus: map['status'] ?? '',
        taskId: map['name'] ?? '',
        customerName: '', // Not in API response
        userEmail: '', // Not in API response
        commentCount:
            int.tryParse(map['comment_count']?.toString() ?? '0') ?? 0,
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing task model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing task model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: TaskModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<TaskModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => TaskModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'subj': subj,
      'project': projectId,
      'project_name': projectName,
      'desc': desc,
      'prior': prior,
      'due_date': dueDate,
      'task_status': taskStatus,
      'taskId': taskId,
      'customer_name': customerName,
      'user_email': userEmail,
    };
  }

  String toJson() => json.encode(toMap());
}
