import 'package:objectbox/objectbox.dart';
import 'package:rasiin_tasks_app/core/enums/post_media_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/parse_fields.dart';

@Entity()
class PostModel {
  @Id()
  int id = 0;

  String postId;
  String content;
  String user;
  DateTime creation;
  bool isPoll;
  DateTime? pollExpiry;
  int likeCount;
  int commentCount;
  bool isLiked;
  int totalVotes;
  bool isExpired;

  final media = ToMany<PostMedia>();
  final pollOptions = ToMany<PollOption>();
  final likedPostUserInfo = ToMany<LikedPostUserInfo>();
  final votedPostUserInfo = ToMany<VotedPostUserInfo>();

  final postuserInfo = ToOne<PostUserInfo>();

  PostModel({
    required this.postId,
    required this.content,
    required this.user,
    required this.creation,
    required this.isPoll,
    this.pollExpiry,
    this.likeCount = 0,
    this.commentCount = 0,
    this.isLiked = false,
    this.totalVotes = 0,
    this.isExpired = false,
  });

  // For post creation time
  String get formattedCreatedAt => creation.timeAgo();

  // For poll expiry time
  String get formattedPollExpiry => pollExpiry?.timeLeft() ?? 'Soon';

  bool get hasMedia => media.isNotEmpty;
  bool get hasPoll => isPoll && pollOptions.isNotEmpty;

  // Factory constructor to create a PostModel from JSON
  factory PostModel.fromJson(Map<String, dynamic> json) {
    final post = PostModel(
      postId: json['name'] ?? '',
      content: json['content'] ?? '',
      user: json['user'] ?? '',
      creation: handleDates(json['creation'].toString())!,
      isPoll: json['is_poll'] == 1,
      pollExpiry: handleDates(json['poll_expiry']),
      likeCount: parseInt(json['like_count']),
      commentCount: parseInt(json['comment_count']),
      isLiked: json['liked'] ?? false,
      totalVotes: parseInt(json['total_votes']),
      isExpired: json['is_expired'] ?? false,
    );

    // Add media if exists
    if (json['media'] != null && json['media'] is List) {
      post.media.addAll(PostMedia.fromJsonList(json['media']));
    }

    // Add poll options if exists
    if (json['poll_options'] != null && json['poll_options'] is List) {
      post.pollOptions.addAll(PollOption.fromJsonList(json['poll_options']));
    }

    // Add liked post user info if exists
    if (json['liked_users'] != null && json['liked_users'] is List) {
      post.likedPostUserInfo
          .addAll(LikedPostUserInfo.fromJsonList(json['liked_users']));
    }

    // Add Voted post user info if exists
    if (json['voted_users'] != null && json['voted_users'] is List) {
      post.votedPostUserInfo
          .addAll(VotedPostUserInfo.fromJsonList(json['voted_users']));
    }

    // Add user info if exists
    if (json['user_info'] != null) {
      post.postuserInfo.target = PostUserInfo.fromJson(json['user_info']);
    }

    return post;
  }

  // Convert a list of JSON objects to a list of PostModel objects
  static List<PostModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => PostModel.fromJson(json)).toList();
  }

  // Convert the PostModel to a JSON object
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': postId,
      'content': content,
      'user': user,
      'creation': creation.toIso8601String(),
      'is_poll': isPoll ? 1 : 0,
      'poll_expiry': pollExpiry?.toIso8601String(),
      'media': media.map((m) => m.toJson()).toList(),
      'like_count': likeCount,
      'comment_count': commentCount,
      'liked': isLiked,
      'poll_options': pollOptions.map((o) => o.toJson()).toList(),
      'total_votes': totalVotes,
      'is_expired': isExpired,
      'user_info': postuserInfo.target?.toJson(),
    };
  }

  @override
  String toString() {
    return 'PostModel{id: $id, postId: $postId, content: $content, user: $user, creation: $creation, isPoll: $isPoll, pollExpiry: $pollExpiry, likeCount: $likeCount, commentCount: $commentCount, liked: $isLiked, totalVotes: $totalVotes, isExpired: $isExpired, media: ${media.length}, pollOptions: ${pollOptions.length}}';
  }
}

@Entity()
class PostMedia {
  @Id()
  int id = 0;

  String fileUrl;
  String mediaType; // 'Image' or 'Video'

  PostMedia({
    required this.fileUrl,
    required this.mediaType,
  });

  PostMediaType get type {
    switch (mediaType.toLowerCase()) {
      case 'image':
        return PostMediaType.image;
      case 'video':
        return PostMediaType.video;
      default:
        return PostMediaType.other;
    }
  }

  // getter to check if the media is an image
  bool get isImage => type == PostMediaType.image;

  factory PostMedia.fromJson(Map<String, dynamic> json) {
    return PostMedia(
      fileUrl: json['file_url'] ?? '',
      mediaType: json['media_type'] ?? '',
    );
  }

  static List<PostMedia> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => PostMedia.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'file_url': fileUrl,
      'media_type': mediaType,
    };
  }

  @override
  String toString() {
    return 'PostMedia{id: $id, fileUrl: $fileUrl, mediaType: $mediaType}';
  }
}

@Entity()
class PollOption {
  @Id()
  int id = 0;

  String pollOptionId;
  String optionText;
  int votes;
  double percentage;
  bool isVoted;

  PollOption({
    required this.pollOptionId,
    required this.optionText,
    this.votes = 0,
    this.percentage = 0,
    this.isVoted = false,
  });

  factory PollOption.fromJson(Map<String, dynamic> json) {
    return PollOption(
      pollOptionId: json['name'] ?? '',
      optionText: json['option_text'] ?? '',
      votes: parseInt(json['votes']),
      percentage: parseDouble(json['percentage']),
      isVoted: json['voted'] ?? false,
    );
  }

  static List<PollOption> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => PollOption.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'name': pollOptionId,
      'option_text': optionText,
      'votes': votes,
      'percentage': percentage,
    };
  }

  @override
  String toString() {
    return 'PollOption{id: $id, name: $pollOptionId, optionText: $optionText, votes: $votes, percentage: $percentage}';
  }
}

@Entity()
class PostUserInfo {
  @Id()
  int id = 0;

  String email;
  String firstName;
  String lastName;
  String fullName;
  String userImage;

  PostUserInfo({
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.fullName,
    required this.userImage,
  });

  factory PostUserInfo.fromJson(Map<String, dynamic> json) {
    return PostUserInfo(
      email: json['email'] ?? '',
      firstName: json['first_name'],
      lastName: json['last_name'] ?? '',
      fullName: json['full_name'] ?? '',
      userImage: json['user_image'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'first_name': firstName,
      'last_name': lastName,
      'full_name': fullName,
      'user_image': userImage,
    };
  }

  @override
  String toString() {
    return 'PostUserInfo{id: $id, email: $email, fullName: $fullName}';
  }
}

@Entity()
class LikedPostUserInfo {
  @Id()
  int id = 0;

  String email;
  String fullName;
  String userImage;

  LikedPostUserInfo({
    required this.email,
    required this.fullName,
    required this.userImage,
  });

  factory LikedPostUserInfo.fromJson(Map<String, dynamic> json) {
    return LikedPostUserInfo(
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? '',
      userImage: json['user_image'] ?? '',
    );
  }

  static List<LikedPostUserInfo> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => LikedPostUserInfo.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'full_name': fullName,
      'user_image': userImage,
    };
  }

  @override
  String toString() {
    return 'LikedPostUserInfo{id: $id, email: $email, fullName: $fullName}';
  }
}

@Entity()
class VotedPostUserInfo {
  @Id()
  int id = 0;

  String email;
  String fullName;
  String userImage;
  @Property(type: PropertyType.date)
  DateTime? voteDate;

  VotedPostUserInfo({
    required this.email,
    required this.fullName,
    required this.userImage,
    this.voteDate,
  });

  String get formattedVoteDate => voteDate?.timeAgo() ?? 'Not voted yet';

  factory VotedPostUserInfo.fromJson(Map<String, dynamic> json) {
    return VotedPostUserInfo(
      email: json['email'] ?? '',
      fullName: json['full_name'] ?? '',
      userImage: json['user_image'] ?? '',
      voteDate: (json['voted_on'] as String?)?.toDateTime(),
    );
  }

  static List<VotedPostUserInfo> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => VotedPostUserInfo.fromJson(json)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'full_name': fullName,
      'user_image': userImage,
      'voted_on': voteDate?.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'VotedPostUserInfo{id: $id, email: $email, fullName: $fullName votedOn: $voteDate}';
  }
}
