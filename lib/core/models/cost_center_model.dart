import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class CostCenterModel {
  final String id;
  final String name;

  const CostCenterModel({required this.id, required this.name});

  factory CostCenterModel.fromJson(Map<String, dynamic> json) {
    try {
      return CostCenterModel(
        id: json['name'] ?? '',
        name: json['cost_center_name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing cost center model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "Error parsing cost center model : $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: CostCenterModel,
        actualType: json.runtimeType,
      );
    }
  }

  static List<CostCenterModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => CostCenterModel.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'CostCenterModel(id: $id, name: $name)';
  }
}
