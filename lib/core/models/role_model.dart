import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:objectbox/objectbox.dart';

import 'user_model.dart';

@Entity()
class RoleModel {
  @Id()
  int id = 0;

  String name;

  final user = ToOne<UserModel>(); // 👈 forward relation

  RoleModel({
    required this.name,
  });

  factory RoleModel.fromJson(Map<String, dynamic> json) {
    try {
      return RoleModel(
        name: json['name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing role model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing role model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: RoleModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  // Convert a list of role strings into RoleModel instances
  static List<RoleModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((role) => RoleModel(name: role.toString())).toList();
  }

  Map<String, dynamic> toJson() {
    return {'name': name};
  }

  @override
  String toString() {
    return 'RoleModel(name: $name)';
  }
}
