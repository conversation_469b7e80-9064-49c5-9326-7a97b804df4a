import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';

class SalesInvoiceModel {
  final String id;
  final String customer;
  final DateTime? posting_date;
  final DateTime? due_date;
  final double total_amount;
  final double outstanding_amount;
  final double paid_amount;
  final int docstatus;
  final String status;

  SalesInvoiceModel({
    required this.id,
    required this.customer,
    required this.posting_date,
    required this.due_date,
    required this.total_amount,
    required this.outstanding_amount,
    required this.paid_amount,
    required this.docstatus,
    required this.status,
  });

  // get formatted date
  String get formattedPostingDate => posting_date?.toFormattedString() ?? '';
  String get formattedDueDate => due_date?.toFormattedString() ?? '';

  // Parse date from string
  static DateTime? _parseDate(String? date) => date?.toDateTime();

  // get formatted amount
  String get formattedTotalAmount => total_amount.toMoneyString();
  String get formattedOutstandingAmount => outstanding_amount.toMoneyString();
  String get formattedPaidAmount => paid_amount.toMoneyString();

  // get status color
  Color get outstandingStatusColor =>
      outstanding_amount > 0 ? Colors.red : Colors.green;


  factory SalesInvoiceModel.fromJson(Map<String, dynamic> json) {
    try {
      return SalesInvoiceModel(
        id: json['name'] ?? '',
        customer: json['customer'] ?? '',
        docstatus: json['docstatus'] ?? 0,
        status: json['status'] ?? '',
        posting_date: _parseDate(json['posting_date']),
        due_date: _parseDate(json['due_date']),
        total_amount: double.tryParse(json['total']?.toString() ?? '0') ?? 0,
        outstanding_amount:
            double.tryParse(json['outstanding_amount']?.toString() ?? '0') ?? 0,
        paid_amount:
            double.tryParse(json['paid_amount']?.toString() ?? '0') ?? 0,
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing sales invoice model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing sales invoice model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: SalesInvoiceModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<SalesInvoiceModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => SalesInvoiceModel.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'SalesInvoiceModel(id: $id, customer: $customer, posting_date: $posting_date, due_date: $due_date, total_amount: $total_amount, outstanding_amount: $outstanding_amount, paid_amount: $paid_amount)';
  }
}
