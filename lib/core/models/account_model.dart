import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class AccountModel {
  final String id;
  final String name;

  AccountModel({
    required this.id,
    required this.name,
  });

  factory AccountModel.fromJson(Map<String, dynamic> json) {
    try {
      return AccountModel(
        id: json['name'] ?? '',
        name: json['account_name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse account model : $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse account model : $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AccountModel,
        actualType: json.runtimeType,
      );
    }
  }

  static List<AccountModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => AccountModel.fromJson(json)).toList();
  }

  @override
  String toString() {
    return 'AccountModel(id: $id, name: $name)';
  }
}
