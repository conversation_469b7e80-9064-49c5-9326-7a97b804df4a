import 'dart:convert';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';

class PaymentModel {
  final String paymentId;
  final String postingDate;
  final String type;
  final String customerId;
  final String customerName;
  final double amount;
  final String status;
  final String paymentMethod;

  PaymentModel({
    required this.paymentId,
    required this.postingDate,
    required this.type,
    required this.customerId,
    required this.customerName,
    required this.amount,
    required this.status,
    required this.paymentMethod,
  });

  String get formattedAmount {
    return amount.toMoneyString();
  }

  factory PaymentModel.fromJson(Map<String, dynamic> map) {
    try {
      return PaymentModel(
        paymentId: map['name'] ?? '',
        postingDate: map['posting_date'] ?? '',
        type: map['payment_type'] ?? '',
        customerId: map['party'] ?? '',
        customerName: map['party_name'] ?? '',
        amount: (map['paid_amount'] ?? 0.0).toDouble(),
        status: map['status'] ?? '',
        paymentMethod: map['mode_of_payment'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing payment model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing payment model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: PaymentModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<PaymentModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => PaymentModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'name': paymentId,
      'posting_date': postingDate,
      'payment_type': type,
      'party': customerId,
      'party_name': customerName,
      'paid_amount': amount,
      'status': status,
      'mode_of_payment': paymentMethod,
    };
  }

  String toJson() => json.encode(toMap());
}
