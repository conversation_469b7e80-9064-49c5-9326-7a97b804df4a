import 'package:objectbox/objectbox.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/date_format_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';

@Entity()
class AttendenceModel {
  int id = 0;
  final String attendanceId;
  final String employeeId;
  final String employeeName;

  @Property(type: PropertyType.date)
  @Index()
  DateTime? attendanceDate;

  final String status;
  final String shift;
  final String inTime;
  final String outTime;
  final double workingHours;

  AttendenceModel({
    required this.attendanceId,
    required this.employeeId,
    required this.employeeName,
    required this.attendanceDate,
    required this.status,
    required this.shift,
    required this.inTime,
    required this.outTime,
    required this.workingHours,
  });

  // handle dates
  static DateTime? _handleDates(String? date) {
    return date?.toDateTime();
  }

  // get formatted date
  String? get formattedDate {
    return attendanceDate?.toFormattedString(
      formatType: DateFormatType.dayNumberWithName,
    );
  }

  // Factory method to create an instance from JSON
  factory AttendenceModel.fromJson(Map<String, dynamic> json) {
    try {
      return AttendenceModel(
        attendanceId: json['name']?.toString() ?? '',
        employeeId: json['employee']?.toString() ?? '',
        employeeName: json['employee_name']?.toString() ?? '',
        status: json['status']?.toString() ?? '',
        attendanceDate: _handleDates(json['attendance_date'].toString()),
        shift: json['shift']?.toString() ?? '',
        inTime: json['in_time']?.toString() ?? '',
        outTime: json['out_time']?.toString() ?? '',
        workingHours:
            double.tryParse(json['working_hours']?.toString() ?? '0') ?? 0,
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing attendance model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing attendance model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AttendenceModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  // Method to convert the instance to JSON
  Map<String, dynamic> toJson() {
    return {
      'employee_name': employeeName,
      'attendance_date': attendanceDate,
      'shift': shift,
      'in_time': inTime,
      'out_time': outTime,
      'working_hours': workingHours,
      'status': status,
      'attendance_id': attendanceId,
      'formatted_date': formattedDate,
    };
  }

  // fromJson List
  static List<AttendenceModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => AttendenceModel.fromJson(json)).toList();
  }
}
