import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';

/*
g
 fields from api response

 {name: LA-00011, employee: HR-EMP-00421, employee_name: ab<PERSON><PERSON><PERSON> , leave_type: Casual Leave, from_date: 2025-03-13, to_date: 2025-03-13, reason: fdrbfbfhrhbbr, status: Rejected, creation: 2025-03-13 04:55:40.105354}


*/

class LeaveModel {
  final String id;
  final String employee;
  final String employeeName;
  final String leaveType;
  final String reason;
  final String status;
  final DateTime? creation;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int docStatus;

  // Constructor
  LeaveModel({
    required this.id,
    required this.employee,
    required this.employeeName,
    required this.leaveType,
    this.fromDate,
    this.toDate,
    required this.reason,
    required this.status,
    required this.creation,
    required this.docStatus,
  });

  // get formatted date
  String get formattedCreation => creation?.toFormattedString() ?? '';
  String get formattedFromDate => fromDate?.toFormattedString() ?? '';
  String get formattedToDate => toDate?.toFormattedString() ?? '';

  Color getLeaveColor({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    final currentStatus = status.toLowerCase();
    switch (currentStatus) {
      case 'pending':
        return appColors.subtextColor;
      case 'approved':
        return appColors.successColor;
      case 'rejected':
        return appColors.errorColor;
      default:
        return appColors.subtextColor;
    }
  }

  // Factory method to create LeaveModel object from JSON map
  factory LeaveModel.fromJson(Map<String, dynamic> json) {
    try {
      String? _startDate = json['from_date'];
      String? _endDate = json['to_date'] ?? '';
      String? _creation = json['creation'] ?? '';
      DateTime? fromDate = _startDate?.toDateTime();
      DateTime? toDate = _endDate?.toDateTime();
      DateTime? creation = _creation?.toDateTime();
      return LeaveModel(
        id: json['name'] ?? '',
        employee: json['employee'] ?? '',
        employeeName: json['employee_name'] ?? '',
        leaveType: json['leave_type'] ?? '',
        reason: json['reason'] ?? '',
        status: json['status'] ?? '',
        docStatus: json['docstatus'] ?? 0,
        creation: creation,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "Error parsing leave model from json: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "Error parsing leave model from json: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: LeaveModel,
        actualType: json.runtimeType,
      );
    }
  }

  // fromJsonList
  static List<LeaveModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => LeaveModel.fromJson(json)).toList();
  }
}
