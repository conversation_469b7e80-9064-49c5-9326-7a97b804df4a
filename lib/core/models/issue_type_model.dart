import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class IssueTypeModel {
  final String name;

  IssueTypeModel({
    required this.name,
  });

  factory IssueTypeModel.fromJson(Map<String, dynamic> map) {
    try {
      return IssueTypeModel(
        name: map['name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing issue type model: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "An error occurred while parsing issue type model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: IssueTypeModel,
        actualType: map.runtimeType,
      );
    }
  }

  static List<IssueTypeModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => IssueTypeModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
    };
  }
}
