import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class LeaveTypeModel {
  final String leaveType;
  final String name;

  // Constructor
  LeaveTypeModel({
    required this.leaveType,
    required this.name,
  });

  // Factory method to create LeaveTypeModel object from JSON map
  factory LeaveTypeModel.fromJson(Map<String, dynamic> json) {
    try {
      return LeaveTypeModel(
        leaveType: json['leave_type_name'] ?? '',
        name: json['name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "Error parsing leave type model from json: $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "Error parsing leave type model from json: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: LeaveTypeModel,
        actualType: json.runtimeType,
      );
    }
  }

  // fromJsonList
  static List<LeaveTypeModel> fromJsonList(List<dynamic> jsonList) {
    return jsonList.map((json) => LeaveTypeModel.fromJson(json)).toList();
  }

  // Method to convert LeaveTypeModel object to JSON
  Map<String, dynamic> toJson() {
    return {
      'leave_type_name': leaveType,
      'name': name,
    };
  }
}
