import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class ProjectModel {
  final String projectId;
  final String projectName;

  ProjectModel({
    required this.projectId,
    required this.projectName,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> map) {
    try {
      return ProjectModel(
        projectId: map['name'] ?? '',
        projectName: map['project_name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse ProjectModel from json: $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse ProjectModel from json: $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ProjectModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<ProjectModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => ProjectModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'projectId': projectId,
      'projectName': projectName,
    };
  }
}
