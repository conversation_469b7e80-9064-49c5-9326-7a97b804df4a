// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class AllUsersModel {
  final String prefredEmail;
  final String employeeName;
  final String userToken;
  final String? image;

  AllUsersModel({
    required this.prefredEmail,
    required this.employeeName,
    required this.userToken,
    required this.image,
  });

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'prefered_email': prefredEmail,
      'employee_name': employeeName,
      'user_token': userToken,
    };
  }

  factory AllUsersModel.fromJson(Map<String, dynamic> map) {
    try {
      return AllUsersModel(
        prefredEmail: map['prefered_email'] ?? map['email'] ?? '',
        employeeName: map['employee_name'] ?? '',
        userToken: map['user_token'] ?? '',
        image: map['user_image'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        'Failed to parse AllUsersModel from map: $e',
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: 'Failed to parse AllUsersModel from map: $e',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: AllUsersModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  String toJson() => json.encode(toMap());

  static List<AllUsersModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => AllUsersModel.fromJson(map)).toList();
  }
}
