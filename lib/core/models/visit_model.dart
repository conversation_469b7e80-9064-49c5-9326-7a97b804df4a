import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

class VisitModel {
  final String name;
  final String customerName;
  final String visitType;
  final String description;

  VisitModel({
    required this.name,
    required this.customerName,
    required this.visitType,
    required this.description,
  });

  factory VisitModel.fromJson(Map<String, dynamic> map) {
    try {
      return VisitModel(
        name: map['name'] ?? '',
        customerName: map['customer_name'] ?? '',
        visitType: map['visit_type'] ?? '',
        description: map['description'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "Error parsing visit model from map : $e",
        error: e,
        stackTrace: stackTrace,
      );
      throw ParsingFailure(
        message: "Error parsing visit model from map : $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: VisitModel,
        actualType: map.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  static List<VisitModel> fromJsonList(List<dynamic> list) {
    return list.map((visit) => VisitModel.fromJson(visit)).toList();
  }
}
