class EncounterModel {
  final String id;
  final String patient;
  final String patientName;
  final String practitioner;
  final DateTime encounterDate;
  final String encounterType;
  final String status;
  final List<String> symptoms;
  final String diagnosis;
  final List<PrescriptionItem> prescriptions;
  final List<LabTestOrder> labTests;
  final List<ProcedureOrder> procedures;

  EncounterModel({
    required this.id,
    required this.patient,
    required this.patientName,
    required this.practitioner,
    required this.encounterDate,
    required this.encounterType,
    required this.status,
    required this.symptoms,
    required this.diagnosis,
    required this.prescriptions,
    required this.labTests,
    required this.procedures,
  });

  factory EncounterModel.fromJson(Map<String, dynamic> json) {
    return EncounterModel(
      id: json['name'] ?? '',
      patient: json['patient'] ?? '',
      patientName: json['patient_name'] ?? '',
      practitioner: json['practitioner'] ?? '',
      encounterDate: DateTime.parse(json['encounter_date'] ?? DateTime.now().toIso8601String()),
      encounterType: json['encounter_type'] ?? '',
      status: json['status'] ?? '',
      symptoms: List<String>.from(json['symptoms'] ?? []),
      diagnosis: json['diagnosis'] ?? '',
      prescriptions: (json['prescriptions'] as List?)?.map((e) => PrescriptionItem.fromJson(e)).toList() ?? [],
      labTests: (json['lab_tests'] as List?)?.map((e) => LabTestOrder.fromJson(e)).toList() ?? [],
      procedures: (json['procedures'] as List?)?.map((e) => ProcedureOrder.fromJson(e)).toList() ?? [],
    );
  }
}

class PrescriptionItem {
  final String drugCode;
  final String drugName;
  final String dosage;
  final String frequency;
  final int duration;
  final String instructions;

  PrescriptionItem({
    required this.drugCode,
    required this.drugName,
    required this.dosage,
    required this.frequency,
    required this.duration,
    required this.instructions,
  });

  factory PrescriptionItem.fromJson(Map<String, dynamic> json) {
    return PrescriptionItem(
      drugCode: json['drug_code'] ?? '',
      drugName: json['drug_name'] ?? '',
      dosage: json['dosage'] ?? '',
      frequency: json['frequency'] ?? '',
      duration: json['duration'] ?? 0,
      instructions: json['instructions'] ?? '',
    );
  }
}

class LabTestOrder {
  final String testCode;
  final String testName;
  final String priority;
  final String instructions;

  LabTestOrder({
    required this.testCode,
    required this.testName,
    required this.priority,
    required this.instructions,
  });

  factory LabTestOrder.fromJson(Map<String, dynamic> json) {
    return LabTestOrder(
      testCode: json['test_code'] ?? '',
      testName: json['test_name'] ?? '',
      priority: json['priority'] ?? 'Normal',
      instructions: json['instructions'] ?? '',
    );
  }
}

class ProcedureOrder {
  final String procedureCode;
  final String procedureName;
  final String priority;
  final String instructions;

  ProcedureOrder({
    required this.procedureCode,
    required this.procedureName,
    required this.priority,
    required this.instructions,
  });

  factory ProcedureOrder.fromJson(Map<String, dynamic> json) {
    return ProcedureOrder(
      procedureCode: json['procedure_code'] ?? '',
      procedureName: json['procedure_name'] ?? '',
      priority: json['priority'] ?? 'Normal',
      instructions: json['instructions'] ?? '',
    );
  }
}