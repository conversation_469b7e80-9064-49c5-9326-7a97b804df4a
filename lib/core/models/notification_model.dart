import 'dart:convert';

class NotificationModel {

  String? title;
  String? notificationId;
  String? message;
  String? fcmToken;
  String? userId;
  String? status;
  String? createdAt;
  String? updatedAt;

  NotificationModel({
    this.title,
    this.message,
    this.notificationId,
    this.fcmToken,
    this.userId,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      notificationId: map['_id'],
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      fcmToken: map['fcmToken'] ?? '',
      userId: map['userId'] ?? '',
      status: map['status'] ?? '',
      createdAt: map['createdAt'] ?? '',
      updatedAt: map['updatedAt'] ?? '',
    );
  }

  static List<NotificationModel> fromMapList(List<dynamic> mapList) {
    return mapList.map((map) => NotificationModel.fromMap(map)).toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'message': message,
      'fcmToken': fcmToken,
      'userId': userId,
      'status': status,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  String toJson() => json.encode(toMap());

  factory NotificationModel.fromJson(String source) =>
      NotificationModel.fromMap(json.decode(source) as Map<String, dynamic>);
}
