import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/api_status.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';

/// Represents a generic API response model.
///
/// [T] is the type of the data contained in the response.
class ApiResponseModel<T> {
  /// The status of the API response.
  final ApiStatus status;

  /// The message returned by the API.
  final String apiMessage;

  /// The data returned by the API, if any.
  final T? data;

  ApiResponseModel({
    required this.status,
    required this.apiMessage,
    this.data,
  }) {
    // if (status == ApiStatus.success && data == null) {
    //   throw ValidationFailure(
    //     message: 'Successful response but data is null',
    //     fieldName: 'data',
    //   );
    // }
  }

  /// Parses an [ApiResponseModel] from a JSON map.
  ///
  /// [json] is the JSON map to parse.
  /// [fromJsonT] is an optional function to parse the data of type [T] from JSON.
  /// If not provided, the data is assumed to be of type [T] directly.
  factory ApiResponseModel.fromJson({
    required Map<String, dynamic> json,
    T Function(Object?)? fromJsonT,
  }) {
    try {
      // /// Check if the response contains a Frappe exception
      // if (json.containsKey('exc_type') ||
      //     json.containsKey('exception') ||
      //     json.containsKey('exc')) {
      //   throw FrappeFailure.fromFrappeException(json);
      // }

      // Extract the inner `message` (Handles both `message` and `msg`)
      final apiMessage =
          json['message']?['msg'] ?? json['message']?['message'] ?? '';

      // Extract the inner `data` (Handles both `Data` and `data`)
      final rawData = json['message']?['Data'] ?? json['message']?['data'];
      final status = _parseStatus(json['message']?['status'] ?? '');

      // For error responses, don't try to parse data - just return null
      if (status == ApiStatus.error) {
        return ApiResponseModel(
          status: status,
          apiMessage: apiMessage,
          data: null,
        );
      }

      return ApiResponseModel(
        status: status,
        apiMessage: apiMessage,
        data:
            rawData != null && fromJsonT != null ? fromJsonT(rawData) : rawData,
      );
    } on FrappeFailure catch (_) {
      rethrow;
    } catch (error, stackTrace) {
      AppLogger().error(
        'Failed to parse ApiResponseModel  and error is $error',
        error: error,
        stackTrace: stackTrace,
      );

      // Handle  parsing errors
      throw ParsingFailure(
        message: 'Failed to parse ApiResponseModel',
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: ApiResponseModel,
        actualType: json.runtimeType,
        stackTrace: stackTrace,
      );
    }
  }

  /// Parses the API status from a string.
  ///
  /// [status] is the string representation of the API status.
  ///
  /// Returns the corresponding [ApiStatus] enum value.
  ///
  /// Throws no exceptions. If the status is unknown, returns [ApiStatus.unknown].
  static ApiStatus _parseStatus(String status) {
    switch (status) {
      case 'success' || 'Success':
        return ApiStatus.success;
      case 'error' || 'Error':
        return ApiStatus.error;
      default:
        return ApiStatus.unknown;
    }
  }

  //
  T getNonNullableData() {
    if (data == null) {
      throw ValidationFailure(
        message: "Data cannot be empty",
        fieldName: "data",
      );
    }
    return data!;
  }

  bool isSuccess({int? statusCode}) {
    return status == ApiStatus.success ||
        (statusCode != null && statusCode >= 200 && statusCode < 400);
  }

  bool isClientError({int? statusCode}) {
    return status == ApiStatus.error ||
        (statusCode != null && statusCode >= 400 && statusCode < 500);
  }

  @override
  String toString() {
    return 'ApiResponseModel(status: $status, apiMessage: $apiMessage, data: $data)';
  }
}
