import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/parse_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';

class IssueModel {
  final String subject;
  final String raisedBy;
  final String priority;
  final String issueType;
  final String issueId;
  final String description;

  IssueModel({
    required this.subject,
    required this.raisedBy,
    required this.priority,
    required this.issueType,
    required this.description,
    required this.issueId,
  });

  String get formattedDescription {
    return description.parseHtmlString();
  }

  factory IssueModel.fromJson(Map<String, dynamic> map) {
    try {
      return IssueModel(
        subject: map['subject'] ?? '',
        raisedBy: map['raised_by'] ?? '',
        priority: map['priority'] ?? '',
        issueType: map['issue_type'] ?? '',
        description: map['description'] ?? '',
        issueId: map['name'] ?? '',
      );
    } catch (e, stackTrace) {
      AppLogger().error(
        "An error occurred while parsing issue model: $e",
        error: e,
        stackTrace: stackTrace,
      );

      throw ParsingFailure(
        message: "An error occurred while parsing issue model: $e",
        failureType: ParsingFailureType.jsonParsingError,
        expectedType: IssueModel,
        actualType: map.runtimeType,
      );
    }
  }

  static List<IssueModel> fromJsonList(List<dynamic> mapList) {
    return mapList.map((map) => IssueModel.fromJson(map)).toList();
  }

  Map<String, dynamic> toJson() {
    return {
      'subject': subject,
      'raisedBy': raisedBy,
      'priority': priority,
      'issueType': issueType,
      'description': description,
    };
  }
}
