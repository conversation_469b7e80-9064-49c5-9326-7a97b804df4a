import 'dart:convert';

import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';

class CommentModel {
  final String id;
  final String email;
  final String content;
  final String profileImage;
  final DateTime createdAt;

  CommentModel({
    required this.id,
    required this.email,
    required this.content,
    required this.createdAt,
    required this.profileImage,
  });

  factory CommentModel.fromJson(Map<String, dynamic> map) {
    return CommentModel(
      id: map['name'] ?? '',
      email: map['comment_email'] ?? '',
      content: map['content'] ?? '',
      profileImage: map['user_image'] ?? map['employee_image'] ?? '',
      createdAt:
          map['creation'] != null && map['creation'].toString().isNotEmpty
              ? DateTime.parse(map['creation'])
              : DateTime.now(),
    );
  }

  static List<CommentModel> fromJsonList(List<dynamic> commentsList) {
    return commentsList
        .map((comment) => CommentModel.fromJson(comment))
        .toList();
  }

  Map<String, dynamic> toMap() {
    return {
      'contentEmail': email,
      'content': content,
      'creation': createdAt.toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  // Get the time difference between the creation date and the current date
  // Helper function to format the time difference
  String get getTimeDifference {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()} week${(difference.inDays / 7).floor() == 1 ? '' : 's'} ago';
    } else {
      final months = (difference.inDays / 30).floor();
      return '${months} month${months == 1 ? '' : 's'} ago';
    }
  }

  // Get Formatted Comment
  String get formattedComment {
    return content.parseHtmlString();
  }
}
