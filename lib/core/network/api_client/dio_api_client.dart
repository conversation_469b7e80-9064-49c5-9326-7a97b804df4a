import 'package:dio/dio.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';

class DioApiClient {
  final Dio dio;

  DioApiClient({required this.dio});

  /// Unified method for all HTTP requests
  Future<Response> request({
    required HttpMethod method,
    required String endPointUrl,
    Map<String, dynamic>? headers,
    RequestData? data,
    Map<String, dynamic>? queryParameters,
    void Function(int, int)? onReceiveProgress,
    void Function(int, int)? onSendProgress,
    CancelToken? cancelToken,
    Duration? sendTimeout,
    Duration? receiveTimeout,
    bool Function(int?)? validateStatus,
    ResponseType? responseType,
  }) async {
    try {
      final options = Options(
        method: method.name,
        sendTimeout: sendTimeout,
        receiveTimeout: receiveTimeout,
        validateStatus: validateStatus,
        responseType: responseType,
        headers: {
          ...?headers,
          'Content-Type': data?.contentType,
        },
      );

      final response = await dio.request(
        endPointUrl,
        options: options,
        data: data?.toHttpPayload,
        queryParameters: queryParameters,
        onReceiveProgress: onReceiveProgress,
        onSendProgress: onSendProgress,
        cancelToken: cancelToken,
      );
      return response;
    } on DioException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }

  /// Download File
  Future<Response> downloadFile({
    required String urlPath,
    dynamic savePath,
    void Function(int, int)? onReceiveProgress,
    Map<String, dynamic>? queryParameters,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    String lengthHeader = Headers.contentLengthHeader,
    Object? data,
    Options? options,
  }) async {
    try {
      final response = await dio.download(
        urlPath,
        savePath,
        onReceiveProgress: onReceiveProgress,
        cancelToken: cancelToken,
        options: options,
        data: data,
        queryParameters: queryParameters,
        lengthHeader: lengthHeader,
        deleteOnError: deleteOnError,
      );
      return response;
    } on DioException {
      rethrow;
    } catch (e) {
      rethrow;
    }
  }
}
