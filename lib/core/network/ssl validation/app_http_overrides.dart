import 'dart:io';

class AppHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port) {
        // Disable SSL validation **only for your IP**
        if (host == "*************") {
          return true; // Bypass SSL
        }
        return false; // Validate SSL for all other domains
      };
  }
}
