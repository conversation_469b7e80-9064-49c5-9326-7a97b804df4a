import 'package:dio/dio.dart';
import 'package:dio_cache_interceptor/dio_cache_interceptor.dart';
import 'package:rasiin_tasks_app/app/app_config.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/network/interceptors/auth_interceptor.dart';
import 'package:rasiin_tasks_app/core/network/interceptors/logger_interceptor.dart';

Dio createDioClient() {
  final options = BaseOptions(
    baseUrl: ApiEndPoints.baseUrl,
    connectTimeout: const Duration(seconds: 30),
    receiveTimeout: const Duration(seconds: 30),
    sendTimeout: const Duration(seconds: 30),
    followRedirects: true,
    validateStatus: (status) => status != null && status <= 500,
    receiveDataWhenStatusError: true,
    headers: {
      'Accept-Encoding': 'br, gzip',
      'Authorization': AppConfig.getApiToken(),
    },
  );

  final dio = Dio(options);

  dio.interceptors.addAll([
    AuthInterceptor(),
    LoggerInterceptor(),
    DioCacheInterceptor(
      options: CacheOptions(
        store: MemCacheStore(),
        policy: CachePolicy.request,
        // hitCacheOnErrorExcept: [401, 403],
        hitCacheOnErrorCodes: [401, 403],
        maxStale: const Duration(days: 7),
        allowPostMethod: false,
      ),
    ),
  ]);

  return dio;
}
