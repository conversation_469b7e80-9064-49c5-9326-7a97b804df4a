import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/approval_decission.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/sales_invoice_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';

class SalesInvoiceRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  SalesInvoiceRepository({
    required this.dioApiClient,
    required this.httpError<PERSON>and<PERSON>,
  });

  FutureEitherFailOr<List<SalesInvoiceModel>> getSalesInvoices({
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) =>
          SalesInvoiceModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_sales_invoice,
        data: RequestData.json({
          "test_role": userRoles,
        }),
      ),
    );

    return response.fold(
      // Handle failure case
      (failure) {
        return left(failure);
      },
      // Handle success case
      (apiResponse) async {
        final salesInvoices = apiResponse.getNonNullableData();
        return right(salesInvoices);
      },
    );
  }

  FutureEitherFailOr<String> updateSalesInvoiceWorkState({
    required String salesInvoiceId,
    required ApprovalDecision approvalDecision,
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.update_sales_invoice_work_state,
        data: RequestData.json({
          "sales_invoice": salesInvoiceId,
          "action": approvalDecision.salesInvoiceApprovalDecision,
          "test_role": userRoles,
        }),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) async {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  FutureEitherFailOr<Uint8List> getSalesInvoicePdf({
    required String salesInvoiceId,
  }) async {
    final response = await httpErrorHandler.handleRequestBytes(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.pdfBaseUrl,
        queryParameters: {
          "name": salesInvoiceId,
          "format": "POS By Group",
          "doctype": "Sales Invoice",
          // "no_letterhead": "0",
          // "letterhead": "logo",
          // "settings": "{}",
          // "_lang": "en",
        },
        responseType: ResponseType.bytes,
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (bytes) {
        return right(bytes);
      },
    );
  }
}
