import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_type_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';

class IssueRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  IssueRepository({
    required this.dioApiClient,
    required this.httpErrorHand<PERSON>,
  });

  // create issue
  FutureEitherFailOr<String> createIssue({
    required String subject,
    required String raisedBy,
    required String priority,
    required String issueType,
    required String desc,
  }) async {
    final res = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_issue,
        data: RequestData.json(
          {
            "subject": subject,
            // "customer": customer,
            "raised_by": raisedBy,
            "prior": priority,
            "issue_type": issueType,
            "desc": desc,
          },
        ),
      ),
    );
    return res.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // assign issue
  FutureEitherFailOr<String> assignIssue({
    required String issueId,
    required String issueType,
    required List<String> usersEmail,
    required String description,
    required String assignedDate,
    required String assignedBy,
  }) async {
    final res = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.assign_issue,
        data: RequestData.json({
          "issue_id": issueId,
          "issue_type": issueType,
          "users": usersEmail,
          "desc": description,
          "assigned_date": assignedDate,
          "assigned_by": assignedBy
        }),
      ),
    );
    return res.fold(
      (failure) => left(failure),
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // change task status
  FutureEitherFailOr<String> changeIssueStatus({
    required String issueId,
    required String issueStatus,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.change_issue_status,
        data: RequestData.json({
          "assigned_issue_id": issueId,
          "assigned_issue_status": issueStatus,
        }),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // get all issues
  FutureEitherFailOr<List<IssueModel>> getAllIssues() async {
    // Fetch from server
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => IssueModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_issues,
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        // Fallback to cached data if fetching from server failed
        // final cachedIssues = await _getCachedIssues();
        // if (cachedIssues.isRight()) {
        // return cachedIssues;
        // }
        return left(failure); // Return failure if cache is also empty
      },
      (apiResponse) async {
        final issues = apiResponse.getNonNullableData();
        return right(issues);
      },
    );
  }

  // get all issue types
  FutureEitherFailOr<List<IssueTypeModel>> getAllIssueTypes() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => IssueTypeModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_issue_type,
      ),
    );

    return response.fold(
      (failure) async {
        //
        return left(failure);
      },
      (apiResponse) async {
        final List<IssueTypeModel> issuesTypes =
            apiResponse.getNonNullableData();
        return right(issuesTypes);
      },
    );
  }

  // get issue comments
  FutureEitherFailOr<List<CommentModel>> getIssueComments({
    required String issueId,
  }) async {
    //
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => CommentModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_issue_comments,
        data: RequestData.json({
          "issue_name": issueId,
        }),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        //
        final comments = apiResponse.getNonNullableData();
        return right(comments);
      },
    );
  }

  // add issue comment
  FutureEitherFailOr<String> addIssueComment({
    required String issueId,
    required String comment,
    required String userEmail,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.add_issue_comment,
        data: RequestData.json({
          "issue_name": issueId,
          "user_email": userEmail,
          "content": comment,
        }),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }
}
