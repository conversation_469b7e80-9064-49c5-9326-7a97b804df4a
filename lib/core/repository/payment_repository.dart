import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/payment_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';

class PaymentRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  PaymentRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  //---------------------------------------------- Fetching   -------------------------------------------------

  FutureEitherFailOr<List<PaymentModel>> getAllPayments({
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => PaymentModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_payments,
        data: RequestData.json({
          "test_role": userRoles,
        }),
      ),
    );

    return response.fold(
      (failure) async => left(failure),
      (apiResponse) async {
        final payments = apiResponse.getNonNullableData();
        return right(payments);
      },
    );
  }

  //-------------------------------------  Creation ---------------------------------

  FutureEitherFailOr<String> createPayment({
    required PaymentModel payment,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_payment,
        data: RequestData.json(
          payment.toMap(),
        ),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final String message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }
}
