import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/appointment_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';

typedef FutureEitherFailOr<T> = Future<Either<AppFailure, T>>;

class AppointmentRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  AppointmentRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  // Get doctor appointments
  FutureEitherFailOr<List<AppointmentModel>> getDoctorAppointments({
    required String doctorIdentifier,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => AppointmentModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_doctor_appointments,
        data: RequestData.json({
          'doctor_identifier': doctorIdentifier,
        }),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final List<AppointmentModel> appointments = apiResponse.getNonNullableData();
        return right(appointments);
      },
    );
  }
}