import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/account_model.dart';
import 'package:rasiin_tasks_app/core/models/cost_center_model.dart';
import 'package:rasiin_tasks_app/core/models/expense_model.dart';
import 'package:rasiin_tasks_app/core/models/department_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/params/create_expense_params.dart';

class ExpenseRepository {
  //
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  ExpenseRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  // get all expense types
  FutureEitherFailOr<List<DepartmentModel>> getAllDepartments() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => DepartmentModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_departments,
      ),
    );

    return response.fold(
      (failure) async {
        //
        return left(failure);
      },
      (apiResponse) async {
        //

        final expenseTypes = apiResponse.getNonNullableData();

        return right(expenseTypes);
      },
    );
  }

  // get all expense types
  FutureEitherFailOr<List<ExpenseModel>> getAllExpenses({
    required List<String> userRoles,
    required String userEmail,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => ExpenseModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_expenses,
        data: RequestData.json(
          {
            'test_role': userRoles,
            'employee': userEmail,
          },
        ),
      ),
    );

    return response.fold(
      (failure) async {
        //
        return left(failure);
      },
      (apiResponse) async {
        //

        final expenses = apiResponse.getNonNullableData();

        return right(expenses);
      },
    );
  }

  // get all cost centers
  FutureEitherFailOr<List<CostCenterModel>> getAllCostCenters() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => CostCenterModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_cost_centers,
      ),
    );

    return response.fold(
      (failure) async {
        //
        return left(failure);
      },
      (apiResponse) async {
        //

        final costCenters = apiResponse.getNonNullableData();

        return right(costCenters);
      },
    );
  }

  // get all accounts
  FutureEitherFailOr<List<AccountModel>> getAllAccounts() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => AccountModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_expense_accounts,
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final accounts = apiResponse.getNonNullableData();
        return right(accounts);
      },
    );
  }

  // create expense
  FutureEitherFailOr<String> createExpense({
    required List<CreateExpenseParams> createExpenseParams,
  }) async {
    final expenses = CreateExpenseParams.toJsonList(createExpenseParams);
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_expense,
        data: RequestData.json({
          'expenses': expenses,
        }),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  Future<List<CreateExpenseParams>> addExpense(
      List<CreateExpenseParams> currentExpenses,
      CreateExpenseParams newExpense) async {
    print('Expense to create unique id is : ${newExpense.uniqueId}');
    // Check if the expense already exists
    if (currentExpenses
        .any((expense) => expense.uniqueId == newExpense.uniqueId)) {
      print('Expense already exists: ${newExpense.uniqueId}');
      return List.from(currentExpenses); // Return a copy without changes.
    }

    // Add the new expense to a new list
    return List.from(currentExpenses)
      ..add(newExpense); // Return a new list with the new expense added.
  }

  Future<List<CreateExpenseParams>> updateExpense(
      List<CreateExpenseParams> currentExpenses,
      CreateExpenseParams updatedExpense) async {
    print('Expense to update unique id is : ${updatedExpense.uniqueId}');
    final index = currentExpenses
        .indexWhere((expense) => expense.uniqueId == updatedExpense.uniqueId);

    // If the expense is found, update it
    if (index != -1) {
      // Create a new list and update the specified index
      return List.from(currentExpenses)
        ..[index] =
            updatedExpense; // Return a new list with the updated expense.
    } else {
      print('Expense not found: ${updatedExpense.uniqueId}');
      return List.from(currentExpenses); // Return a copy without changes.
    }
  }

  Future<List<CreateExpenseParams>> deleteExpense(
      List<CreateExpenseParams> currentExpenses,
      CreateExpenseParams expenseToDelete) async {
    print('Expense to delete unique id is : ${expenseToDelete.uniqueId}');
    // Create a new list without the specified expense
    return List.from(currentExpenses)
      ..removeWhere(
        (expense) => expense.uniqueId == expenseToDelete.uniqueId,
      ); // Return a new list without the deleted expense.
  }

  // approve expense
  FutureEitherFailOr<String> approveExpense({
    required String expenseId,
    required List<String> userRoles,
  }) async {
    //
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.approve_expense,
        data: RequestData.json(
          {
            'expense_id': expenseId,
            'test_role': userRoles,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // modify expense

  /*

   fields
   expense_id=None, account=None, paid_from=None, cost_center=None, department_name=None, remark=None, test_role=None

  */
  FutureEitherFailOr<String> modifyExpense({
    required String expenseId,
    required String account,
    required String paidFrom,
    required List<String> userRoles,
    required String costCenter,
    required String remark,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndPoints.update_expense,
        data: RequestData.json({
          'expense_id': expenseId,
          'account': account,
          'paid_from': paidFrom,
          'test_role': userRoles,
          'cost_center': costCenter,
          'remark': remark,
        }),
      ),
    );

    return response.fold(
      (failure) {
        // Return the failure if the modification fails
        return left(failure);
      },
      (apiResponse) {
        // Get the response message if the modification is successful
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }
}
