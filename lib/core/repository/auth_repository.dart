import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/core/enums/cache_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/services/local_cache_services.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/database/database_manager.dart';

class AuthRepository {
  final LocalCacheServices localCacheServices;
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final DatabaseManager databaseManager;

  AuthRepository({
    required this.dioApiClient,
    required this.localCacheServices,
    required this.httpErrorHandler,
    required this.databaseManager,
  });

  Future<String> _fetchFcmToken() async {
    return await FirebaseMessaging.instance.getToken() ?? '';
  }

  Future<void> _storeUserEmailAndId({required UserModel user}) async {
    await Future.wait(
      [
        localCacheServices.storeEmployeeId(user.employeeId),
        localCacheServices.storeUserEmail(user.email),
      ],
    );
  }

  // login user
  FutureEitherFailOr<UserModel> login({
    required String username,
    required String password,
  }) async {
    final token = await _fetchFcmToken();
    debugPrint("fcm token is : $token");
    final res = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      // skipConnectionCheck: true,
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.login,
        data: RequestData.json(
          {
            "user_name": username,
            "password": password,
            "token": token,
          },
        ),
      ),
    );
    return res.fold(
      (failure) => left(failure),
      (apiResponse) async {
        //
        // final user = success['message']['data'] as Map<String, dynamic>;
        // final userModel = UserModel.fromMap(user);
        final userModel = apiResponse.getNonNullableData();

        await _storeUserEmailAndId(user: userModel);
        return right(userModel);
      },
    );
  }

  FutureEitherFailOr<bool> logoutUser() async {
    try {
      final storedUser = await localCacheServices.flutterSecureStorageServices
          .readData('email');
      debugPrint("fetched user email in logout is  : $storedUser");
      await _clearUserData();
      return right(true);
    } catch (error, stackTrace) {
      AppLogger().error(
        "An error occurred during logout: ${error.toString()}",
        error: error,
        stackTrace: stackTrace,
      );
      return left(CacheFailure(
        message: 'An error occurred during logout: ${error.toString()}',
        failureType: CacheFailureType.unknown,
        stackTrace: stackTrace,
      ));
    }
  }

  Future<void> _clearUserData() async {
    await Future.wait([
      localCacheServices.deleteAllData(),
      databaseManager.clearDatabase(),
    ]);
  }

  // check user
  Future<bool> checkUser() async {
    try {
      // final user = await flutterSecureStorageServices.readData('email');
      final userEmail = await localCacheServices.getUserEmail();
      if (userEmail != null && userEmail.isNotEmpty) {
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  }
}
