import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';
import 'package:rasiin_tasks_app/core/models/project_model.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/services/flutter_secure_storage_services.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';

class TaskRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  TaskRepository({
    required this.dioApiClient,
    required this.httpErrorHand<PERSON>,
  });

  // ---------------------------- Task Methods ----------------------------

  // Get all tasks
  FutureEitherFailOr<List<TaskModel>> getAllTasks({
    required List<String> userRoles,
  }) async {
    // Fetch from server
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => TaskModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_tasks,
        data: RequestData.json(
          {
            "test_role": userRoles,
          },
        ),
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final tasks = apiResponse.getNonNullableData();
        return right(tasks);
      },
    );
  }

  // Create task
  FutureEitherFailOr<String> createTask({
    required TaskModel task,
    required List<String> userRoles,
  }) async {
    final tasks = task.toMap();
    final data = {
      ...tasks,
      'test_role': userRoles,
    };
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.createTask,
        data: RequestData.json(
          data,
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // Change task status
  FutureEitherFailOr<String> changeTaskStatus({
    required String taskId,
    required String taskStatus,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.change_task_status,
        data: RequestData.json(
          {
            "assigned_task_id": taskId,
            "assigned_task_status": taskStatus,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // ---------------------------- Project Methods ----------------------------

  // Get all projects
  FutureEitherFailOr<List<ProjectModel>> getAllProjects() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => ProjectModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.getProjects,
      ),
    );

    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final projects = apiResponse.getNonNullableData();
        return right(projects);
      },
    );
  }

  // ---------------------------- Assigned Task Methods ----------------------------

  // Assign task
  FutureEitherFailOr<String> assignTask({
    required String taskId,
    required List<String> usersEmail,
    required String description,
    required String assignedDate,
    required String assignedBy,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.assign_task,
        data: RequestData.json(
          {
            "task_id": taskId,
            "users": usersEmail,
            "desc": description,
            "assigned_date": assignedDate,
            "assigned_by": assignedBy,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // Get all assigned tasks
  FutureEitherFailOr<List<AssignedTasksModel>> getAllAssignedTasks({
    required String currentUserEmail,
  }) async {
    final email = await FlutterSecureStorageServices().readData("email");

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          AssignedTasksModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_assigned_tasks,
        data: RequestData.json(
          {
            "email": email ?? currentUserEmail,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) async {
        final assignedTasks = apiResponse.getNonNullableData();
        return right(assignedTasks);
      },
    );
  }

  // ---------------------------- Comment Methods ----------------------------

  // Get all comments by reference type
  FutureEitherFailOr<List<CommentModel>> getAllCommentsByReferenceType({
    required String referenceType,
    required String taskId,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => CommentModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_tasks_comment_taks,
        data: RequestData.json(
          {
            "reference_type": referenceType,
            "task_id": taskId,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final comments = apiResponse.getNonNullableData();
        return right(comments);
      },
    );
  }

  // Send comment in all tasks
  FutureEitherFailOr<String> sendCommentInAllTasks({
    required String taskId,
    required String userEmail,
    required String comment,
    required String taskType,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.send_comment_in_alltaks,
        data: RequestData.json(
          {
            "user_email": userEmail,
            "reference_name": taskId,
            "content": comment,
            "reference_type": taskType,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // // ---------------------------- Filtering Methods ----------------------------

  // // Filter all tasks
  // Future<List<TaskModel>> filterTasks({
  //   required FilterField field,
  //   required FilterOperator operator,
  //   required String value,
  // }) async {
  //   final List<TaskModel> filteredTasks =
  //       await taskObjectBoxManager.filterTasks(
  //     field: field,
  //     operator: operator,
  //     value: value,
  //   );
  //   return filteredTasks;
  // }

  // // Filter all assigned tasks
  // Future<List<AssignedTasksModel>> filterAssignedTasks({
  //   required FilterField field,
  //   required FilterOperator operator,
  //   required String value,
  // }) async {
  //   final List<AssignedTasksModel> filteredTasks =
  //       await taskObjectBoxManager.filterAssignedTasks(
  //     field: field,
  //     operator: operator,
  //     value: value,
  //   );
  //   return filteredTasks;
  // }

  // // ---------------------------- Sorting Methods ----------------------------

  // // Sort all tasks
  // Future<List<TaskModel>> sortTasks({
  //   required FilterField field,
  //   required bool isAscending,
  // }) async {
  //   final List<TaskModel> sortedTasks = await taskObjectBoxManager.sortTasks(
  //     field: field,
  //     ascending: isAscending,
  //   );
  //   return sortedTasks;
  // }

  // // Sort all assigned tasks
  // Future<List<AssignedTasksModel>> sortAssignedTasks({
  //   required FilterField field,
  //   required bool isAscending,
  // }) async {
  //   final List<AssignedTasksModel> sortedTasks =
  //       await taskObjectBoxManager.sortAssignedTasks(
  //     field: field,
  //     ascending: isAscending,
  //   );
  //   return sortedTasks;
  // }
}
