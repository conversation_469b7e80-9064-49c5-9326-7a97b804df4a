import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/sales_order_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';

class SalesOrderRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  SalesOrderRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  FutureEitherFailOr<List<SalesOrderModel>> getAllSalesOrders({
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => SalesOrderModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_sales_orders,
        data: RequestData.json(
          {
            'test_role': userRoles,
          },
        ),
      ),
    );

    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final salesOrders = apiResponse.getNonNullableData();
        return right(salesOrders);
      },
    );
  }
}
