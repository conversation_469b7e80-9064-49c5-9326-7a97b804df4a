import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/notification_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';

class NotificationRepository {
  final HttpErrorHandler httpErrorHandler;
  final DioApiClient dioApiClient;

  NotificationRepository({
    required this.httpErrorHandler,
    required this.dioApiClient,
  });
  // static final _baseUrl =
  //     "https://hodan-hospital-backend.vercel.app/api/v1/auth/";
  // //! dio setup
  // final Dio _dio = Dio(
  //   BaseOptions(
  //     baseUrl: _baseUrl,
  //     connectTimeout: const Duration(seconds: 30),
  //     contentType: "application/json",
  //     maxRedirects: 5,
  //     sendTimeout: const Duration(seconds: 30),
  //     receiveTimeout: const Duration(seconds: 30),
  //     validateStatus: (status) {
  //       return status != null && (status >= 200 && status <= 500);
  //     },
  //   ),
  // );

  //! send notification
  FutureEitherFailOr<String> sendNotification({
    required List<String> title,
    required String message,
    required List<String> fcmTokens,
    required String userId,
  }) async {
    final sendNotificationUrl =
        "https://hodan-hospital-backend.vercel.app/api/v1/auth/send-notification";

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: sendNotificationUrl,
        data: RequestData.json(
          {
            'userId': userId,
            'fcmTokens': fcmTokens,
            'titles': title,
            'message': message,
          },
        ),
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final apiMessage = apiResponse.apiMessage;
        return right(apiMessage);
      },
    );
  }

  //! get all notification
  FutureEitherFailOr<List<NotificationModel>> getAllNotifications() async {
    final getNotificationsUrl =
        "https://hodan-hospital-backend.vercel.app/api/v1/auth/get-notifications";

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => NotificationModel.fromMapList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: getNotificationsUrl,
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final notifications = apiResponse.getNonNullableData();
        return right(notifications);
      },
    );
  }

  // delete single notification
  FutureEitherFailOr<String> deleteSingleNotification({
    required String id,
  }) async {
    final deleteNotificationUrl =
        "https://hodan-hospital-backend.vercel.app/api/v1/auth/delete-notification/$id";

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.delete,
        endPointUrl: deleteNotificationUrl,
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final apiMessage = apiResponse.apiMessage;
        return right(apiMessage);
      },
    );
  }

  // delete single notification
  // EitherFailOrSuccess<String> deleteSingleNotification({
  //   required String id,
  // }) async {
  //   // final endPoint = "delete-notification/$id";
  //   final deleteNotificationUrl =
  //       "https://hodan-hospital-backend.vercel.app/api/v1/auth/delete-notification/$id";

  //   try {
  //     final response = await dioApiClient.request(
  //       method: HttpMethod.delete,
  //       endPointUrl: deleteNotificationUrl,
  //     );
  //     final responseBody = response.data as Map<String, dynamic>;
  //     final apiMessage = responseBody['message'];
  //     if (response.statusCode == 200) {
  //       debugPrint("api message is $apiMessage");
  //       return right(apiMessage);
  //     } else {
  //       return left(ApiFailureParams(
  //         errorMessage:
  //             "Failed to delete notification ${response.statusCode} and message is :$apiMessage",
  //         stackTrace: StackTrace.current,
  //       ));
  //     }
  //   } catch (error, stackTrace) {
  //     AppLogger().error(
  //       "Failed to delete single notification : ${error.toString()}",
  //       error: error,
  //       stackTrace: stackTrace,
  //     );
  //     return left(ApiFailureParams(
  //       errorMessage: "Failed to delete single notification $error",
  //       stackTrace: stackTrace,
  //     ));
  //   }
  // }
}
