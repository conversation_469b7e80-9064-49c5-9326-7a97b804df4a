import 'package:fpdart/fpdart.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/database/user_database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/services/local_cache_services.dart';
import 'package:rasiin_tasks_app/core/services/multipart_services.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/data_repository_helper.dart';

import '../models/dashboard_model.dart';

class UsersRepository {
  final DioApiClient dioApiClient;
  final MultiPartServices multiPartServices;
  final LocalCacheServices localCacheServices;
  final HttpErrorHandler httpErrorHandler;
  final DataRepositoryHelper dataRepositoryHelper;
  final UserDatabaseManager userDatabaseManager;

  UsersRepository({
    required this.dioApiClient,
    required this.multiPartServices,
    required this.userDatabaseManager,
    required this.localCacheServices,
    required this.httpErrorHandler,
    required this.dataRepositoryHelper,
  });

  Future<String> _getUserIdentifier() async {
    return (await localCacheServices.getUserEmail()) ??
        (await localCacheServices.getEmployeeId()) ??
        '';
  }

  // get user data
  FutureEitherFailOr<UserModel> getAuthUser({
    required bool forceFetch,
  }) async {
    final storedUser = await _getUserIdentifier();

    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => UserModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_auth_user,
        data: RequestData.json(
          {
            "user_email": storedUser,
            "force_fetch": forceFetch,
            // "user_email": "Administrator",
          },
        ),
      ),
    );
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        //
        final userModel = apiResponse.getNonNullableData();

        return right(userModel);
      },
    );
  }

  // get all users
  FutureEitherFailOr<List<AllUsersModel>> getAllUsers() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) => AllUsersModel.fromJsonList(json as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_users,
      ),
    );

    return response.fold(
      (failure) async {
        //

        return left(failure);
      },
      (apiResponse) async {
        //
        final users = apiResponse.getNonNullableData();

        return right(users);
      },
    );
  }

  // forgot password
  FutureEitherFailOr<String> changePassword({
    required String newPassword,
    required String email,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.change_password,
        data: RequestData.json(
          {
            "user__email": email,
            "user_password": newPassword,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // change profile image
  FutureEitherFailOr<String> changeProfileImage({
    required String employeeId,
    required XFile profileImage,
  }) async {
    //
    final formData = await multiPartServices.buildChangeProfileImageFormData(
      employeeId: employeeId,
      image: profileImage,
    );
    //

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.change_profile_image,
        data: RequestData.formData(
          formData,
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        //
        final message =
            apiResponse.data is String ? apiResponse.data as String : '';
        return right(message);
      },
    );
  }

  // get profile image
  FutureEitherFailOr<String> getProfileImage({
    required String employeeId,
  }) async {
    final id = await localCacheServices.getEmployeeId();
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_employee_profile,
        data: RequestData.json(
          {
            'employee_id': id ?? employeeId,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        final imageUrl =
            apiResponse.data is String ? apiResponse.data as String : '';
        return right(imageUrl);
      },
    );
  }

  // get user dashboard
  FutureEitherFailOr<DashboardModel> getDashboard() async {
    final identifier = await _getUserIdentifier();
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (json) =>
          DashboardModel.fromJson(json as Map<String, dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_user_dashboard,
        data: RequestData.json(
          {
            'user_identifier': identifier,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        //
        return left(failure);
      },
      (apiResponse) {
        final dashboard = apiResponse.getNonNullableData();
        return right(dashboard);
      },
    );
  }
}
