import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/visit_model.dart';
import 'package:rasiin_tasks_app/core/models/visit_type_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';

class VisitRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  VisitRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  //-------------------------------------  Creation ---------------------------------

  // create visit
  FutureEitherFailOr<String> createVisit({
    required String visitType,
    required String description,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_visit,
        data: RequestData.json(
          {
            'visit_type': visitType,
            'description': description,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  //---------------------------------------------- Fetching   -------------------------------------------------

  FutureEitherFailOr<List<VisitModel>> getAllVisits({
    required String employee,
    required List<String> userRoles,
  }) async {
    // Fetch from server
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => VisitModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_visits,
        data: RequestData.json(
          {
            'employee': employee,
            'test_role': userRoles,
          },
        ),
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final visits = apiResponse.getNonNullableData();
        return right(visits);
      },
    );
  }

  FutureEitherFailOr<List<VisitTypeModel>> getAllVisitTypes() async {
    // Fetch from server
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => VisitTypeModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_visit_types,
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final visitTypes = apiResponse.getNonNullableData();
        return right(visitTypes);
      },
    );
  }
}
