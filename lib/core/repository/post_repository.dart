import 'package:fpdart/fpdart.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/database/post_database_manager.dart';
import 'package:rasiin_tasks_app/core/enums/cache_failure_type.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/services/file_picker_services.dart';
import 'package:rasiin_tasks_app/core/services/multipart_services.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/data_repository_helper.dart';

class PostRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final FilePickerServices filePickerServices;
  final DataRepositoryHelper dataRepositoryHelper;
  final PostDatabaseManager postDatabaseManager;

  PostRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.filePickerServices,
    required this.dataRepositoryHelper,
    required this.postDatabaseManager,
  });

  FutureEitherFailOr<List<PostModel>> getAllPosts({
    required bool forceRefresh,
    required String userEmail,
    required int limit,
    required int offset,
  }) async {
    return dataRepositoryHelper.fetchListWithCacheFallback(
      forceRefresh: forceRefresh,
      remoteFetch: () => httpErrorHandler.handleRequest(
        fromJsonT: (json) => PostModel.fromJsonList(json as List<dynamic>),
        requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndPoints.get_all_posts,
          queryParameters: {
            'user': userEmail,
            'limit': limit,
            'offset': offset,
          },
        ),
      ),
      localFetch: () => postDatabaseManager.getPosts(),
      saveToCache: (posts) => postDatabaseManager.savePosts(posts: posts),
    );
  }

  // toggle_like_post
  FutureEitherFailOr<String> toggleLikePost({
    required String postId,
    required String userEmail,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndPoints.toggle_like_post,
        data: RequestData.json({
          "post": postId,
          "user": userEmail,
        }),
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // vote_on_poll
  FutureEitherFailOr<String> voteOnPoll({
    required String postId,
    required String optionId,
    required String userEmail,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.put,
        endPointUrl: ApiEndPoints.vote_on_poll,
        data: RequestData.json({
          "post": postId,
          "user": userEmail,
          "option": optionId,
        }),
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  // pick video
  FutureEitherFailOr<XFile> pickVideo({
    Duration? maxDuration,
  }) async {
    final video = await filePickerServices.pickVideo(maxDuration: maxDuration);
    if (video != null) {
      return right(video);
    }

    return left(
      CacheFailure(
        message: 'Error picking video',
        failureType: CacheFailureType.unknown,
      ),
    );
  }

  // pick multiple images
  FutureEitherFailOr<List<XFile>> pickMultipleImages() async {
    final images = await filePickerServices.pickMultipleImages();
    if (images != null) {
      return right(images);
    }

    return left(
      CacheFailure(
        message: 'Error picking multiple images',
        failureType: CacheFailureType.unknown,
      ),
    );
  }

  /// create post
  FutureEitherFailOr<String> createPost({
    required String userEmail,
    required String content,
    required XFile? pickedVideo,
    required List<XFile> pickedImages,
  }) async {
    final formData = await MultiPartServices().buildPostWithVideoFormData(
      userEmail: userEmail,
      content: content,
      video: pickedVideo,
      images: pickedImages,
    );

    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_post,
        data: RequestData.formData(formData),
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  /// create poll post
  FutureEitherFailOr<String> createPollPost({
    required String userEmail,
    required String question,
    required String option1,
    required String option2,
    String? option3,
    String? option4,
    int? durationMins,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_poll,
        data: RequestData.json({
          'user': userEmail,
          'question': question,
          'option_1': option1,
          'option_2': option2,
          if (option3 != null) 'option_3': option3,
          if (option4 != null) 'option_4': option4,
          'duration_mins': durationMins,
        }),
      ),
    );
    return response.fold(
      (failure) => left(failure),
      (apiResponse) {
        final message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }
}
