import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/leave_model.dart';
import 'package:rasiin_tasks_app/core/models/leave_type_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';

class LeaveRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;

  LeaveRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
  });

  //-------------------------------------  Creation ---------------------------------

  // Create leave
  FutureEitherFailOr<String> createLeave({
    required String employeeId,
    required String leaveType,
    required String fromDate,
    required String toDate,
    required String reason,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.create_leave,
        data: RequestData.json({
          "emp_id": employeeId,
          "emp_leave_type": leaveType,
          "from_date": fromDate,
          "to_date": toDate,
          "reason": reason,
        }),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final String message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }

  //---------------------------------------------- Fetching   -------------------------------------------------

  FutureEitherFailOr<List<LeaveModel>> getAllLeaves({
    required String employee,
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => LeaveModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_all_leaves,
        data: RequestData.json(
          {
            "employee": employee,
            "test_role": userRoles,
          },
        ),
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final List<LeaveModel> leaves = apiResponse.getNonNullableData();
        return right(leaves);
      },
    );
  }

  FutureEitherFailOr<List<LeaveTypeModel>> getAllLeaveTypes() async {
    final response = await httpErrorHandler.handleRequest(
      fromJsonT: (data) => LeaveTypeModel.fromJsonList(data as List<dynamic>),
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.get_leave_type,
      ),
    );

    // Handle response
    return response.fold(
      (failure) async {
        return left(failure);
      },
      (apiResponse) async {
        final List<LeaveTypeModel> leaveTypes =
            apiResponse.getNonNullableData();

        return right(leaveTypes);
      },
    );
  }

  //---------------------------------------------- Approval -------------------------------------------------

  FutureEitherFailOr<String> approveLeave({
    required String leaveId,
    required List<String> userRoles,
  }) async {
    final response = await httpErrorHandler.handleRequest(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.post,
        endPointUrl: ApiEndPoints.approve_leave,
        data: RequestData.json(
          {
            'leave_id': leaveId,
            'test_role': userRoles,
          },
        ),
      ),
    );

    return response.fold(
      (failure) {
        return left(failure);
      },
      (apiResponse) {
        final String message = apiResponse.apiMessage;
        return right(message);
      },
    );
  }
}
