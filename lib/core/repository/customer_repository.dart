// import 'package:fpdart/fpdart.dart';
// import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
// import 'package:rasiin_tasks_app/core/models/customer_model.dart';
// import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
// import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
// import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
// import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';

// class CustomerRepository {
//   final DioApiClient dioApiClient;
//   final HttpErrorHandler httpErrorHandler;

//   CustomerRepository({
//     required this.dioApiClient,
//     required this.httpErrorHandler,
//   });

//   FutureEitherFailOr<List<CustomerModel>> getAllCustomers() async {
//     final response = await httpErrorHandler.handleRequest(
//       requestFunction: () => dioApiClient.request(
//         method: HttpMethod.get,
//         endPointUrl: ApiEndPoints.get_customers,
//       ),
//     );

//     return response.fold(
//       (failure) async {
//         return left(failure);
//       },
//       (apiResponse) async {
//         final customers = apiResponse.getNonNullableData();
//         return right(customers);
//       },
//     );
//   }
// }
