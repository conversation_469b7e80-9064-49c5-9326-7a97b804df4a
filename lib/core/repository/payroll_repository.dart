import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/database/payroll_database_manager.dart';
import 'package:rasiin_tasks_app/core/enums/cache_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/payroll_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/services/file_picker_services.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/data_repository_helper.dart';

class PayrollRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final FilePickerServices filePickerServices;
  final PayrollDatabaseManager payrollDatabaseManager;
  final DataRepositoryHelper dataRepositoryHelper;

  PayrollRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.filePickerServices,
    required this.payrollDatabaseManager,
    required this.dataRepositoryHelper,
  });

  FutureEitherFailOr<List<PayrollModel>> getAllPayrolls({
    required String employee,
    required List<String> userRoles,
    required bool forceFetch,
  }) async {
    // final employeeId = await localCacheServices.getEmployeeId();

    final response =
        await dataRepositoryHelper.fetchListWithCacheFallback<PayrollModel>(
      forceRefresh: forceFetch,
      autoRefresh: false,
      localFetch: () {
        return payrollDatabaseManager.getPayrolls();
      },
      saveToCache: (payrolls) async {
        await payrollDatabaseManager.savePayrolls(payrolls: payrolls);
      },
      remoteFetch: () {
        return httpErrorHandler.handleRequest(
          fromJsonT: (json) {
            print('🔍 Raw JSON received: $json');
            // Handle error responses where json might not be a list
            if (json is List<dynamic>) {
              return PayrollModel.fromJsonList(json);
            } else {
              // For error responses, return empty list
              print('⚠️ Payroll API returned non-list data: $json');
              return <PayrollModel>[];
            }
          },
          requestFunction: () => dioApiClient.request(
            method: HttpMethod.get,
            endPointUrl: ApiEndPoints.get_employee_salary_slip,
            data: RequestData.json(
              {
                'test_role': userRoles,
                'employee_id': employee,
                // 'employee_id': 'HR-EMP-00383',
              },
            ),
          ),
        );
      },
    );

    return response;
  }

  FutureEitherFailOr<List<PayrollModel>> filterPayroll({
    required int year,
    required Month month,
  }) async {
    final monthInt = month.index + 1;
    print("🔍 PayrollRepository: Filtering - year: $year, month: $monthInt (${month.name})");

    final result = await payrollDatabaseManager.filterPayrollsByMonthAndYear(
      year: year,
      month: monthInt,
    );

    return result.fold(
      (failure) {
        print("❌ PayrollRepository: Filter failed - ${failure.message}");
        return left(failure);
      },
      (success) {
        print("✅ PayrollRepository: Filter success - ${success.length} payrolls found");
        return right(success);
      },
    );
  }

  FutureEitherFailOr<Uint8List> getPayrollPdf({
    required String payrollId,
  }) async {
    final response = await httpErrorHandler.handleRequestBytes(
      requestFunction: () => dioApiClient.request(
        method: HttpMethod.get,
        endPointUrl: ApiEndPoints.pdfBaseUrl,
        queryParameters: {
          "name": payrollId,
          "doctype": "Salary Slip",
          "format": "Standard",
          // "no_letterhead": "0",
          // "letterhead": "logo",
          // "settings": "{}",
          // "_lang": "en",
        },
        responseType: ResponseType.bytes,
      ),
    );
    return response.fold(
      (failure) {
        return left(failure);
      },
      (bytes) {
        return right(bytes);
      },
    );
  }

  FutureEitherFailOr<String> downloadPayrollPdf({
    required Uint8List bytes,
    required String employeeId,
  }) async {
    try {
      // Step 1: Request storage permission
      // final status = await filePickerServices.checkStoragePermission();
      // if (!status) {
      //   return left(CacheFailure(
      //     message: 'Storage permission denied',
      //     failureType: CacheFailureType.permissionDenied,
      //   ));
      // }

      // Step 2: Let the user choose the save location
      final String? selectedDirectory =
          await filePickerServices.getDirectoryPath();
      if (selectedDirectory == null) {
        return left(CacheFailure(
          message: 'No directory selected',
          failureType: CacheFailureType.noDirectorySelected,
        ));
      }

      // Step 3: Create the file path
      final timeStamp = DateTime.now().millisecondsSinceEpoch.toString();
      final safeEmployeeId = employeeId.replaceAll(RegExp(r'[^\w\-]'), '_');
      final fileName = 'salary_slip_${safeEmployeeId}_$timeStamp.pdf';
      final filePath = '$selectedDirectory/$fileName';

      // Step 4: Write the bytes to the file
      // await file.writeAsBytes(bytes);
      await filePickerServices.saveFileToDirectory(
        directoryPath: selectedDirectory,
        fileName: fileName,
        bytes: bytes,
      );

      return right(filePath);
    } catch (e) {
      return left(CacheFailure(
        message: 'Failed to download PDF: $e',
        failureType: CacheFailureType.unknown,
      ));
    }
  }
}
