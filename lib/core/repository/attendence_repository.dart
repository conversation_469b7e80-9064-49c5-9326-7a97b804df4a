import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/database/attendence_database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/models/attendence_model.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/request_data.dart';
import 'package:rasiin_tasks_app/core/constants/api_end_points.dart';
import 'package:rasiin_tasks_app/core/enums/http_method_enum.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/data_repository_helper.dart';

class AttendanceRepository {
  final DioApiClient dioApiClient;
  final HttpErrorHandler httpErrorHandler;
  final AttendenceDatabaseManager attendenceDatabaseManager;
  final DataRepositoryHelper dataRepositoryHelper;

  AttendanceRepository({
    required this.dioApiClient,
    required this.httpErrorHandler,
    required this.attendenceDatabaseManager,
    required this.dataRepositoryHelper,
  });

  // FutureEitherFailOr<List<AttendenceModel>> getAllAttendances({
  //   required String employeeId,
  //   required List<String> userRoles,
  //   bool forceRefresh = false,
  // }) async {
  //   // Step 1: Check if forceRefresh is true
  //   if (!forceRefresh) {
  //     // Step 2: Fetch from local cache
  //     final localResult = await attendenceDatabaseManager.getAttendences();
  //     if (localResult.isRight()) {
  //       return localResult;
  //     }
  //   }

  //   // Step 2: Fetch from server
  //   final remoteResponse = await httpErrorHandler.handleRequest(
  //     fromJsonT: (json) => AttendenceModel.fromJsonList(json as List<dynamic>),
  //     requestFunction: () => dioApiClient.request(
  //       method: HttpMethod.get,
  //       endPointUrl: ApiEndPoints.get_employee_attendance,
  //       data: RequestData.json(
  //         {
  //           'employee': employeeId,
  //           'test_role': userRoles,
  //         },
  //       ),
  //     ),
  //   );

  //   return remoteResponse.fold(
  //     // Handle failure case
  //     (failure) {
  //       return left(failure);
  //     },
  //     // Handle success case
  //     (apiResponse) async {
  //       final attendances = apiResponse.getNonNullableData();
  //       if (attendances.isNotEmpty) {
  //         await attendenceDatabaseManager.saveAttendences(
  //           attendences: attendances,
  //         );
  //       }
  //       return right(attendances);
  //     },
  //   );
  // }

  FutureEitherFailOr<List<AttendenceModel>> getAllAttendances({
    required String employeeId,
    required List<String> userRoles,
    required bool forceRefresh,
  }) async {
    return dataRepositoryHelper.fetchListWithCacheFallback(
      localFetch: () => attendenceDatabaseManager.getAttendences(),
      remoteFetch: () => httpErrorHandler.handleRequest(
        fromJsonT: (json) =>
            AttendenceModel.fromJsonList(json as List<dynamic>),
        requestFunction: () => dioApiClient.request(
          method: HttpMethod.get,
          endPointUrl: ApiEndPoints.get_employee_attendance,
          data: RequestData.json({
            'employee': employeeId,
            'test_role': userRoles,
          }),
        ),
      ),
      saveToCache: (attendances) => attendenceDatabaseManager.saveAttendences(
        attendences: attendances,
      ),
      forceRefresh: forceRefresh,
    );
  }

  FutureEitherFailOr<List<AttendenceModel>> filterAttendances({
    required int year,
    required Month month,
    List<String>? employeeIds,
  }) async {
    final result =
        await attendenceDatabaseManager.filterAttendencesByMonthAndYear(
      year: year,
      month: month.monthNumber,
      employeeIds: employeeIds,
    );

    return result.fold(
      (failure) => left(failure),
      (success) => right(success),
    );
  }
}
