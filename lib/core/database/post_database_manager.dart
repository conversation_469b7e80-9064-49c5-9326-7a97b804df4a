import 'package:rasiin_tasks_app/core/database/database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/database_error_handler.dart';
import 'package:rasiin_tasks_app/core/enums/database_operation_type.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';

class PostDatabaseManager {
  final DatabaseManager databaseManager;
  final DatabaseErrorHandler databaseErrorHandler;

  PostDatabaseManager({
    required this.databaseManager,
    required this.databaseErrorHandler,
  });

  // store posts to database
  FutureEitherFailOr<List<int>> savePosts(
      {required List<PostModel> posts}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        await _deletePosts();
        final postBox =
            await databaseErrorHandler.databaseManager.getBox<PostModel>();

        // Insert or Update posts
        return postBox.putManyAsync(posts);
      },
    );
  }

  // get posts from database
  FutureEitherFailOr<List<PostModel>> getPosts() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        final postBox =
            await databaseErrorHandler.databaseManager.getBox<PostModel>();
        final posts = await postBox.getAllAsync();
        return posts;
      },
    );
  }

  // delete posts from database
  FutureEitherFailOr<int> _deletePosts() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        final postBox =
            await databaseErrorHandler.databaseManager.getBox<PostModel>();
        return postBox.removeAll();
      },
    );
  }
}
