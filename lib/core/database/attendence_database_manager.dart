import 'package:rasiin_tasks_app/core/database/database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/database_error_handler.dart';
import 'package:rasiin_tasks_app/core/enums/database_operation_type.dart';
import 'package:rasiin_tasks_app/core/models/attendence_model.dart';
import 'package:rasiin_tasks_app/objectbox.g.dart';

class AttendenceDatabaseManager {
  final DatabaseManager databaseManager;
  final DatabaseErrorHandler databaseErrorHandler;

  AttendenceDatabaseManager({
    required this.databaseManager,
    required this.databaseErrorHandler,
  });

  // store payrolls to database
  FutureEitherFailOr<List<int>> saveAttendences(
      {required List<AttendenceModel> attendences}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        await _deleteAttendence();
        final attendenceBox = await databaseErrorHandler.databaseManager
            .getBox<AttendenceModel>();

        // Insert or Update appointment
        return attendenceBox.putManyAsync(attendences);
      },
    );
  }

  // get payrolls from database
  FutureEitherFailOr<List<AttendenceModel>> getAttendences() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        final attendenceBox = await databaseErrorHandler.databaseManager
            .getBox<AttendenceModel>();
        final attendences = await attendenceBox.getAllAsync();
        return attendences;
      },
    );
  }

  // filter by month and year
  // FutureEitherFailOr<List<AttendenceModel>> filterAttendencesByMonthAndYear({
  //   required int month,
  //   required int year,
  // }) async {
  //   final payrollBox =
  //       await databaseErrorHandler.databaseManager.getBox<AttendenceModel>();

  //   final startDate = DateTime(year, month, 1);
  //   final endDate = DateTime(year, month + 1, 1).subtract(Duration(seconds: 1));

  //   print("Filtering attendances for: Year = $year, Month = $month");
  //   print("Start Date: $startDate");
  //   print("End Date: $endDate");

  //   return await databaseErrorHandler.handleDatabaseOperationWithQuery<
  //       List<AttendenceModel>, AttendenceModel>(
  //     query: payrollBox.query(
  //       AttendenceModel_.attendanceDate.between(
  //           startDate.millisecondsSinceEpoch, endDate.millisecondsSinceEpoch),
  //     ),
  //     operationType: DatabaseOperationType.query,
  //     queryOperation: (queryBuilder) async {
  //       final query = queryBuilder.build();
  //       final result = query.find();
  //       print("Result: ${result.length}");
  //       query.close();
  //       return result;
  //     },
  //   );
  // }

  FutureEitherFailOr<List<AttendenceModel>> filterAttendencesByMonthAndYear({
    required int month,
    required int year,
    List<String>? employeeIds,
  }) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        final attendenceBox = await databaseErrorHandler.databaseManager
            .getBox<AttendenceModel>();

        final startDate = DateTime(year, month, 1);
        final endDate =
            DateTime(year, month + 1, 1).subtract(Duration(milliseconds: 1));

        var condition = AttendenceModel_.attendanceDate.between(
          startDate.millisecondsSinceEpoch,
          endDate.millisecondsSinceEpoch,
        );

        // Add employeeId filter only if the list is not null and not empty
        if (employeeIds != null && employeeIds.isNotEmpty) {
          condition = condition.and(
            AttendenceModel_.employeeName.oneOf(employeeIds),
          );
        }

        final query = attendenceBox.query(condition).build();
        final result = query.find();
        query.close();
        return result;
      },
    );
  }

  // delete payrolls from database
  FutureEitherFailOr<int> _deleteAttendence() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        final attendenceBox = await databaseErrorHandler.databaseManager
            .getBox<AttendenceModel>();
        return attendenceBox.removeAll();
      },
    );
  }
}
