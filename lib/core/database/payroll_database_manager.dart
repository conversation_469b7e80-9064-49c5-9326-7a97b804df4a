import 'package:rasiin_tasks_app/core/database/database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/database_error_handler.dart';
import 'package:rasiin_tasks_app/core/enums/database_operation_type.dart';
import 'package:rasiin_tasks_app/core/models/payroll_model.dart';
import 'package:rasiin_tasks_app/objectbox.g.dart';

class PayrollDatabaseManager {
  final DatabaseManager databaseManager;
  final DatabaseErrorHandler databaseErrorHandler;

  PayrollDatabaseManager({
    required this.databaseManager,
    required this.databaseErrorHandler,
  });

  // store payrolls to database
  FutureEitherFailOr<List<int>> savePayrolls(
      {required List<PayrollModel> payrolls}) async {
    print("💾 PayrollDatabaseManager: Starting savePayrolls with ${payrolls.length} payrolls");
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        print("💾 PayrollDatabaseManager: Deleting existing payrolls...");
        await _deletePayroll();
        final payrollBox =
            await databaseErrorHandler.databaseManager.getBox<PayrollModel>();

        print("💾 PayrollDatabaseManager: Saving ${payrolls.length} payrolls to database");

        // Debug: Print first few payroll dates
        if (payrolls.isNotEmpty) {
          print("💾 PayrollDatabaseManager: Sample payroll dates:");
          for (int i = 0; i < (payrolls.length > 3 ? 3 : payrolls.length); i++) {
            print("   - ${payrolls[i].payrollId}: ${payrolls[i].postingDate}");
          }
        }

        final result = payrollBox.putMany(payrolls);
        print("💾 PayrollDatabaseManager: Successfully saved ${result.length} payrolls");
        return result;
      },
    );
  }

  // get payrolls from database
  FutureEitherFailOr<List<PayrollModel>> getPayrolls() async {
    print("📖 PayrollDatabaseManager: Starting getPayrolls");
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        final payrollBox =
            await databaseErrorHandler.databaseManager.getBox<PayrollModel>();

        final count = payrollBox.count();
        print("📖 PayrollDatabaseManager: Getting payrolls from database : $count");

        final allPayrolls = payrollBox.getAll();
        print("📖 PayrollDatabaseManager: Retrieved ${allPayrolls.length} payrolls");

        // Debug: Print first few payroll dates
        if (allPayrolls.isNotEmpty) {
          print("📖 PayrollDatabaseManager: Sample retrieved payroll dates:");
          for (int i = 0; i < (allPayrolls.length > 3 ? 3 : allPayrolls.length); i++) {
            print("   - ${allPayrolls[i].payrollId}: ${allPayrolls[i].postingDate}");
          }
        }

        return allPayrolls;
      },
    );
  }

  // filter by month and year
  FutureEitherFailOr<List<PayrollModel>> filterPayrollsByMonthAndYear({
    required int month,
    required int year,
  }) async {
    print("🔍 PayrollDatabaseManager: Starting filterPayrollsByMonthAndYear");
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        print("🔍 PayrollDatabaseManager: filtering payroll in month : $month , year : $year");
        final payrollBox =
            await databaseErrorHandler.databaseManager.getBox<PayrollModel>();

        final totalCount = payrollBox.count();
        print("🔍 PayrollDatabaseManager: Total payrolls in database: $totalCount");

        final startDate = DateTime(year, month, 1);
        final endDate =
            DateTime(year, month + 1, 1).subtract(Duration(milliseconds: 1));

        print("🔍 PayrollDatabaseManager: Date range - Start: $startDate, End: $endDate");
        print("🔍 PayrollDatabaseManager: Timestamp range - Start: ${startDate.millisecondsSinceEpoch}, End: ${endDate.millisecondsSinceEpoch}");

        // Debug: Check all payroll dates before filtering
        if (totalCount > 0) {
          final allPayrolls = payrollBox.getAll();
          print("🔍 PayrollDatabaseManager: All payroll dates in database:");
          for (var payroll in allPayrolls.take(5)) {
            print("   - ${payroll.payrollId}: ${payroll.postingDate} (${payroll.postingDate.millisecondsSinceEpoch})");
          }
        }

        final query = payrollBox
            .query(
              PayrollModel_.postingDate.between(
                  startDate.millisecondsSinceEpoch,
                  endDate.millisecondsSinceEpoch),
            )
            .build();
        final result = query.find();
        print("🔍 PayrollDatabaseManager: filtered payroll result length : ${result.length}");

        if (result.isNotEmpty) {
          print("🔍 PayrollDatabaseManager: Filtered results:");
          for (var payroll in result.take(3)) {
            print("   - ${payroll.payrollId}: ${payroll.postingDate}");
          }
        }

        query.close();
        return result;
      },
    );
  }

  // delete payrolls from database
  FutureEitherFailOr<int> _deletePayroll() async {
    print("🗑️ PayrollDatabaseManager: Starting _deletePayroll");
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        final payrollBox =
            await databaseErrorHandler.databaseManager.getBox<PayrollModel>();
        final countBeforeDelete = payrollBox.count();
        print("🗑️ PayrollDatabaseManager: Clearing payrolls : $countBeforeDelete");
        final deletedCount = payrollBox.removeAll();
        print("🗑️ PayrollDatabaseManager: Deleted $deletedCount payrolls");
        return deletedCount;
      },
    );
  }
}
