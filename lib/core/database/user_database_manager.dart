import 'package:rasiin_tasks_app/core/database/database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/errors/database_error_handler.dart';
import 'package:rasiin_tasks_app/core/enums/database_operation_type.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/objectbox.g.dart';

class UserDatabaseManager {
  final DatabaseManager databaseManager;
  final DatabaseErrorHandler databaseErrorHandler;

  UserDatabaseManager({
    required this.databaseManager,
    required this.databaseErrorHandler,
  });

  // store user to database
  FutureEitherFailOr<int> saveUser({required UserModel user}) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.write,
      operationFunction: () async {
        await _deleteUser();
        final userBox =
            await databaseErrorHandler.databaseManager.getBox<UserModel>();

        // Insert or Update appointment
        return userBox.put(user);
      },
    );
  }

  // get users from database
  FutureEitherFailOr<UserModel> getUser({
    required String employeeId,
  }) async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.read,
      operationFunction: () async {
        final userBox =
            await databaseErrorHandler.databaseManager.getBox<UserModel>();
        final user = userBox
            .query(UserModel_.employeeId.equals(employeeId))
            .build()
            .find()
            .first;
        // return user;
        return user;
      },
    );
  }

  // delete users from database
  FutureEitherFailOr<int> _deleteUser() async {
    return await databaseErrorHandler.handleDatabaseOperation(
      operationType: DatabaseOperationType.delete,
      operationFunction: () async {
        final userBox =
            await databaseErrorHandler.databaseManager.getBox<UserModel>();
        // print("Clearing users : ${userBox.count()}");
        return userBox.removeAll();
      },
    );
  }
}
