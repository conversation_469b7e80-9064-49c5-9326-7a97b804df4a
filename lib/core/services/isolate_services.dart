import 'dart:async';
import 'dart:isolate';

/// A class to handle background tasks using isolates.
class IsolateServices {
  /// Runs the provided function in a background isolate and returns the result via a [Future].
  Future<T> executeInBackground<T>(
    Future<T> Function(SendPort sendPort) taskFunction,
  ) async {
    final receivePort = ReceivePort();

    // Spawn the isolate and send the receive port to it.
    final isolate = await Isolate.spawn(
      _isolateEntryFunction,
      _IsolateTaskData(taskFunction, receivePort.sendPort),
    );

    // Wait for the result from the isolate
    final result = await receivePort.first;

    // Clean up the isolate after the task is complete.
    isolate.kill(priority: Isolate.immediate);

    if (result is T) {
      return result;
    } else {
      throw Exception('Failed to fetch data from background task');
    }
  }

  /// The entry function that runs inside the isolate.
  static Future<void> _isolateEntryFunction(_IsolateTaskData data) async {
    final sendPort = data.sendPort;
    try {
      final result = await data.taskFunction(sendPort);
      sendPort.send(result); // Send the result back to the main isolate
    } catch (e) {
      sendPort.send('Error: $e'); // Send error back if something fails
    }
  }
}

/// A class to encapsulate the task to be run in the isolate.
class _IsolateTaskData {
  final Future Function(SendPort sendPort) taskFunction;
  final SendPort sendPort;

  _IsolateTaskData(this.taskFunction, this.sendPort);
}
