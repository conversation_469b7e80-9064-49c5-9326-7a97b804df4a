import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/bottomsheets/custom_bottom_sheet.dart';
import 'package:rasiin_tasks_app/features/common/widgets/bottomsheets/filter_bottom_sheet.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class BottomSheetServices {
  static Future<void> showBottomSheet(
    BuildContext context, {
    required String headerTitleText,
    TextStyle? headerTitleTextStyle,
    required Widget formFields,
    Color? backgroundColor,
    EdgeInsetsGeometry padding = const EdgeInsets.all(16.0),
    double borderRadius = 20.0,
    bool isDismissible = true,
    bool enableDrag = true,
    double minHeight = 0.1,
    double maxHeight = 0.9,
  }) async {
    final bool isIOS = Theme.of(context).platform == TargetPlatform.iOS;

    final modalSheet =
        isIOS ? showCupertinoModalBottomSheet : showMaterialModalBottomSheet;

    final double effectiveMinHeight = (minHeight < 0.1)
        ? 0.1
        : (minHeight > 0.9)
            ? 0.9
            : minHeight;

    final double effectiveMaxHeight = (maxHeight < 0.1)
        ? 0.1
        : (maxHeight > 0.9)
            ? 0.9
            : maxHeight;

    // Ensure that minHeight is not greater than maxHeight
    final double finalMinHeight = effectiveMinHeight > effectiveMaxHeight
        ? effectiveMaxHeight
        : effectiveMinHeight;

    final double finalMaxHeight = effectiveMaxHeight < effectiveMinHeight
        ? effectiveMinHeight
        : effectiveMaxHeight;

    modalSheet(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      builder: (context) => ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * finalMaxHeight,
          minHeight: MediaQuery.of(context).size.height * finalMinHeight,
        ),
        child: _BottomSheetContent(
          headerTitleText: headerTitleText,
          headerTitleTextStyle: headerTitleTextStyle,
          formFields: formFields,
          padding: padding,
          borderRadius: borderRadius,
          backgroundColor: backgroundColor,
        ),
      ),
    );
  }

  // selectable bottom sheet
  static Future<void> showSelectableBottomSheet<T>(
    BuildContext context, {
    required SelectionType selectionType, // Enum for single or multi-select
    required List<T> options, // The list of options to select from
    required Function(List<T>)
        onSelectionChanged, // Callback when selection changes
    required List<T> selectedValues, // Preselected values
    required String Function(T)? displayTitle,
    required String Function(T)? displaySubTitle,
    required String? Function(T)? displayImage,
    required String bottomSheetTitle,
    bool isDismissible = true,
    bool enableDrag = true,
    double minHeight = 0.1,
    double maxHeight = 0.9,
    required bool showClearButton,
  }) async {
    //
    final bool isIOS = Theme.of(context).platform == TargetPlatform.iOS;

    final modalSheet =
        isIOS ? showCupertinoModalBottomSheet : showMaterialModalBottomSheet;

    final double effectiveMinHeight = (minHeight < 0.1)
        ? 0.1
        : (minHeight > 0.9)
            ? 0.9
            : minHeight;

    final double effectiveMaxHeight = (maxHeight < 0.1)
        ? 0.1
        : (maxHeight > 0.9)
            ? 0.9
            : maxHeight;

    // Ensure that minHeight is not greater than maxHeight
    final double finalMinHeight = effectiveMinHeight > effectiveMaxHeight
        ? effectiveMaxHeight
        : effectiveMinHeight;

    final double finalMaxHeight = effectiveMaxHeight < effectiveMinHeight
        ? effectiveMinHeight
        : effectiveMaxHeight;

    //
    modalSheet(
      context: context,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      builder: (context) => ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * finalMaxHeight,
          minHeight: MediaQuery.of(context).size.height * finalMinHeight,
        ),
        child: CustomBottomSheet<T>(
          options: options,
          selectedValues: selectedValues,
          selectionType: selectionType,
          displayTitle: displayTitle,
          bottomSheetTitle: bottomSheetTitle,
          displayImage: displayImage,
          onSelectionChanged: onSelectionChanged,
          displaySubTitle: displaySubTitle,
          showClearButton: showClearButton,
        ),
      ),
    );
  }

  // filter bottom sheet
  static Future<void> showFilterBottomSheet(
    BuildContext context, {
    required String headerTitleText,
    Widget? customContent,
    EdgeInsetsGeometry? padding,
    required Function(int year, Month month, List<AllUsersModel> selectedUsers)
        onApply,
    int? initialYear,
    Month? initialMonth,
    List<AllUsersModel>? selectedUsers,
  }) async {
    //
    final bool isIOS = Theme.of(context).platform == TargetPlatform.iOS;

    final modalSheet =
        isIOS ? showCupertinoModalBottomSheet : showMaterialModalBottomSheet;

    modalSheet(
      context: context,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: context.appColors.surfaceColor,
      builder: (context) => FilterBottomSheet(
        title: headerTitleText,
        customContent: customContent,
        padding: padding,
        onApply: onApply,
        initialYear: initialYear,
        initialMonth: initialMonth,
        selectedUsers: selectedUsers,
      ),
    );
  }
}

class _BottomSheetContent extends StatelessWidget {
  final String headerTitleText;
  final TextStyle? headerTitleTextStyle;
  final Widget formFields;
  final EdgeInsetsGeometry padding;
  final double borderRadius;
  final Color? backgroundColor;

  const _BottomSheetContent({
    required this.headerTitleText,
    this.headerTitleTextStyle,
    required this.formFields,
    required this.padding,
    required this.borderRadius,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final bgColor = appColors.surfaceColor;

    return Material(
      color: Colors.transparent,
      child: Container(
        padding: padding,
        decoration: BoxDecoration(
          color: backgroundColor ?? bgColor,
          borderRadius:
              BorderRadius.vertical(top: Radius.circular(borderRadius.r)),
        ),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Drag indicator
              Center(
                child: CustomContainer(
                  width: 60,
                  height: 6,
                  margin: EdgeInsets.only(top: 8.h, bottom: 16.h),
                  color: appColors.dividerColor,
                ),
              ),

              // Header
              if (headerTitleText.isNotEmpty)
                Text(
                  headerTitleText,
                  style: headerTitleTextStyle ?? textTheme.titleSmall,
                  textAlign: TextAlign.center,
                ),

              SizedBox(height: 16.h),

              // Form fields
              formFields,

              SizedBox(height: 150.h),
            ],
          ),
        ),
      ),
    );
  }
}
