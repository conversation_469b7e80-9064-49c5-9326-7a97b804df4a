import 'package:flutter_secure_storage/flutter_secure_storage.dart';

class FlutterSecureStorageServices {
  static const _storage = FlutterSecureStorage();

  // store data
  Future<void> storeData(String key, String value) async {
    await _storage.write(key: key, value: value);
  }

  // read data
  Future<String?> readData(String key) async {
    return await _storage.read(key: key);
  }

  // remove data
  Future<void> removeData(String key) async {
    await _storage.delete(key: key);
  }

  // delete all data
  Future<void> deleteAllData() async {
    await _storage.deleteAll();
  }
}
