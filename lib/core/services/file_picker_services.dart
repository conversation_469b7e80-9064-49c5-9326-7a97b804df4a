import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';

class FilePickerServices {
  // get directory path
  Future<String?> getDirectoryPath() async {
    try {
      final result = await FilePicker.platform.getDirectoryPath();
      return result;
    } catch (e, s) {
      AppLogger().error(
        'Error getting directory path: $e',
        stackTrace: s,
      );
      return null;
    }
  }

  // save file to directory
  Future<String?> saveFileToDirectory({
    required String directoryPath,
    required String fileName,
    required Uint8List bytes,
  }) async {
    try {
      final result = await FilePicker.platform.saveFile(
        fileName: fileName,
        bytes: bytes,
      );
      return result;
    } catch (e, s) {
      AppLogger().error(
        'Error saving file to directory: $e',
        stackTrace: s,
      );
      return null;
    }
  }

  // save file to temporary directory
  Future<String?> saveFileToTemporaryDirectory({
    required String fileName,
    required Uint8List bytes,
  }) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final filePath = '${tempDir.path}/$fileName';
      await File(filePath).writeAsBytes(bytes);
      return filePath;
    } catch (e, s) {
      AppLogger().error(
        'Error saving file to temporary directory: $e',
        stackTrace: s,
      );
      return null;
    }
  }

  // pick multiple images
  Future<List<XFile>?> pickMultipleImages() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: true,
        // compressionQuality: 50,
      );
      if (result == null) {
        return null;
      }

      final files = result.files.map((file) => XFile(file.path!)).toList();
      return files;
    } catch (e, s) {
      AppLogger().error(
        'Error picking multiple images: $e',
        stackTrace: s,
      );
      return null;
    }
  }

  // pick video using image picker
  Future<XFile?> pickVideo({
    Duration? maxDuration,
  }) async {
    try {
      final imagePicker = ImagePicker();
      final result = await imagePicker.pickVideo(
        source: ImageSource.gallery,
        maxDuration: maxDuration,
      );
      if (result == null) {
        return null;
      }

      return result;
    } catch (e, s) {
      AppLogger().error(
        'Error picking video: $e',
        stackTrace: s,
      );
      return null;
    }
  }

  // pick multiple videos
  Future<List<XFile>?> pickMultipleVideos() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: true,
      );
      if (result == null) {
        return null;
      }

      final files = result.files.map((file) => XFile(file.path!)).toList();
      return files;
    } catch (e, s) {
      AppLogger().error(
        'Error picking multiple videos: $e',
        stackTrace: s,
      );
      return null;
    }
  }
}
