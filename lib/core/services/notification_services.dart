// import 'package:firebase_core/firebase_core.dart';
// import 'package:firebase_messaging/firebase_messaging.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:rasiin_tasks_app/app/app.dart';
// import 'package:rasiin_tasks_app/features/main/screens/main_screen.dart';
// import 'package:app_settings/app_settings.dart';

// class NotificationService {
//   final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//       FlutterLocalNotificationsPlugin();

//   Future<void> initialize() async {
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

//     // Request notification permission from the user
//     await _requestNotificationPermission();

//     const AndroidInitializationSettings initializationSettingsAndroid =
//         AndroidInitializationSettings('@mipmap/ic_launcher');

//     final InitializationSettings initializationSettings =
//         InitializationSettings(
//       android: initializationSettingsAndroid,
//     );

//     await flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: _onDidReceiveNotificationResponse,
//     );

//     FirebaseMessaging.onMessage.listen((RemoteMessage message) {
//       _handleMessage(message);
//     });

//     FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
//       _handleMessageOnTap(message);
//     });
//   }

//   Future<void> _handleMessage(RemoteMessage message) async {
//     if (message.notification != null) {
//       String? taskId = message.data[
//           'taskId']; // Assuming the taskId is passed in the notification data
//       await _showNotification(
//         message.notification?.title,
//         message.notification?.body,
//         taskId, // Pass taskId as payload
//       );
//     }
//   }

//   Future<void> _showNotification(
//       String? title, String? body, String? payload) async {
//     const AndroidNotificationDetails androidPlatformChannelSpecifics =
//         AndroidNotificationDetails(
//       'your_channel_id',
//       'your_channel_name',
//       channelDescription: 'your_channel_description',
//       importance: Importance.max,
//       priority: Priority.high,
//       showWhen: false,
//     );

//     const NotificationDetails platformChannelSpecifics =
//         NotificationDetails(android: androidPlatformChannelSpecifics);

//     await flutterLocalNotificationsPlugin.show(
//       0,
//       title,
//       body,
//       platformChannelSpecifics,
//       payload:
//           payload, // Pass the payload which can be the taskId or screen to navigate
//     );
//   }

//   // Handle navigation when the notification is tapped
//   Future<void> _onDidReceiveNotificationResponse(
//       NotificationResponse notificationResponse) async {
//     String? payload = notificationResponse.payload;
//     if (payload != null) {
//       navigatorKey.currentState?.pushAndRemoveUntil(
//         MaterialPageRoute(
//           builder: (_) =>
//               MainScreen(initialTabIndex: 1), // Navigate to Tasks Tab
//         ),
//         (Route<dynamic> route) => false, // Remove all previous routes
//       );
//     }
//   }

//   // Handle message when the app is opened from the notification tap
//   Future<void> _handleMessageOnTap(RemoteMessage message) async {
//     String? taskId = message.data['taskId']; // Assuming the taskId is passed
//     if (taskId != null) {
//       navigatorKey.currentState?.pushAndRemoveUntil(
//         MaterialPageRoute(
//           builder: (_) =>
//               MainScreen(initialTabIndex: 1), // Navigate to Tasks Tab
//         ),
//         (Route<dynamic> route) => false, // Remove all previous routes
//       );
//     }
//   }

//   Future<void> _requestNotificationPermission() async {
//     FirebaseMessaging messaging = FirebaseMessaging.instance;

//     NotificationSettings settings = await messaging.requestPermission(
//       alert: true,
//       badge: true,
//       sound: true,
//     );

//     if (settings.authorizationStatus == AuthorizationStatus.authorized) {
//       print('User granted permission');
//     } else if (settings.authorizationStatus ==
//         AuthorizationStatus.provisional) {
//       print('User granted provisional permission');
//     } else {
//       print('User declined or has not accepted permission');
//       // Show a dialog explaining why the app needs notifications.
//       WidgetsBinding.instance.addPostFrameCallback((_) {
//         _showPermissionExplanationDialog();
//       });
//     }
//   }

//   // Show a dialog explaining why the app needs notifications.
//   void _showPermissionExplanationDialog() {
//     if (navigatorKey.currentContext == null) return;

//     showDialog(
//       context: navigatorKey.currentContext!,
//       builder: (context) {
//         return AlertDialog(
//           title: const Text('Notification Permission Required'),
//           content: const Text(
//               'To stay up to date with the latest updates and taks assigned to you and notifications, please enable notifications in the app settings.'),
//           actions: [
//             TextButton(
//               onPressed: () {
//                 Navigator.pop(context);
//               },
//               child: const Text('Close'),
//             ),
//             TextButton(
//               onPressed: () {
//                 // Open app settings so the user can enable notifications.
//                 openAppSettings();
//               },
//               child: const Text('Open Settings'),
//             ),
//           ],
//         );
//       },
//     );
//   }

//   // Open app settings for enabling notifications
//   void openAppSettings() {
//     AppSettings.openAppSettings();
//   }
// }

// // Background handler function
// Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
//   await Firebase.initializeApp();
//   print('Handling a background message: ${message.messageId}');
// }

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:app_settings/app_settings.dart';

import 'package:rasiin_tasks_app/app/app.dart';
import 'package:rasiin_tasks_app/features/main/screens/main_screen.dart';

class NotificationService {
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  Future<void> initialize() async {
    try {
      // Background message handler must be set before any use of FirebaseMessaging
      FirebaseMessaging.onBackgroundMessage(
          _firebaseMessagingBackgroundHandler);

      // Request permission for Firebase Messaging
      await _requestFirebasePermissions();

      // Request iOS permissions for local notifications (for versions < iOS 10)
      await _requestLocalNotificationPermissions();

      // Initialization settings for Android and iOS
      const androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      final iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
        defaultPresentAlert: true,
        defaultPresentSound: true,
        defaultPresentBadge: true,
        defaultPresentBanner: true,
        defaultPresentList: true,
      );

      final settings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // Initialize the local notifications plugin
      await _localNotifications.initialize(
        settings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

      // Handle notification taps (terminated or background)
      FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageTap);

      debugPrint("🔔 NotificationService initialized.");
    } catch (e, st) {
      debugPrint("❌ NotificationService initialization failed: $e");
      debugPrint("$st");
      rethrow;
    }
  }

  // Handle Firebase messages when app is in foreground
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;
    if (notification != null) {
      await _showLocalNotification(
        notification.title,
        notification.body,
        data['taskId'],
      );
    }
  }

  // Show local notification
  Future<void> _showLocalNotification(
      String? title, String? body, String? payload) async {
    const androidDetails = AndroidNotificationDetails(
      'tasks_channel_id',
      'Tasks Notifications',
      channelDescription: 'Notification channel for task updates',
      importance: Importance.max,
      priority: Priority.high,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _localNotifications.show(
      0,
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // When user taps on a local notification
  Future<void> _onNotificationTapped(NotificationResponse response) async {
    final payload = response.payload;
    if (payload != null) {
      _navigateToTask(payload);
    }
  }

  // Handle notification tap from Firebase (background or terminated state)
  Future<void> _handleMessageTap(RemoteMessage message) async {
    final taskId = message.data['taskId'];
    if (taskId != null) {
      _navigateToTask(taskId);
    }
  }

  // Navigate to specific task screen or tab
  void _navigateToTask(String taskId) {
    navigatorKey.currentState?.pushAndRemoveUntil(
      MaterialPageRoute(
        builder: (_) => MainScreen(initialTabIndex: 1), // Navigate to Tasks tab
      ),
      (route) => false,
    );
  }

  // Request Firebase messaging permissions
  Future<void> _requestFirebasePermissions() async {
    final settings = await FirebaseMessaging.instance.requestPermission(
      alert: true,
      badge: true,
      sound: true,
    );

    if (settings.authorizationStatus == AuthorizationStatus.denied) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showPermissionExplanationDialog();
      });
    }
  }

  // Request iOS permissions for local notifications
  Future<void> _requestLocalNotificationPermissions() async {
    final iosPlugin = _localNotifications.resolvePlatformSpecificImplementation<
        IOSFlutterLocalNotificationsPlugin>();

    await iosPlugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  // Explain why notifications are needed
  void _showPermissionExplanationDialog() {
    if (navigatorKey.currentContext == null) return;

    showDialog(
      context: navigatorKey.currentContext!,
      builder: (context) {
        return AlertDialog(
          title: const Text('Notification Permission Required'),
          content: const Text(
            'To receive important updates and task assignments, please enable notifications in settings.',
          ),
          actions: [
            TextButton(
              onPressed: Navigator.of(context).pop,
              child: const Text('Dismiss'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                AppSettings.openAppSettings();
              },
              child: const Text('Open Settings'),
            ),
          ],
        );
      },
    );
  }
}

// Top-level function required for background messaging
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  debugPrint('📩 Background message received: ${message.messageId}');
}
