import 'package:dio/dio.dart';
import 'package:image_picker/image_picker.dart';

class MultiPartServices {
  // build category  formData
  Future<FormData> buildChangeProfileImageFormData({
    required String employeeId,
    required XFile image,
  }) async {
    return FormData.fromMap({
      'employee_id': employeeId,
      'profile_image':
          await MultipartFile.fromFile(image.path, filename: image.name),
    });
  }

  // build post form data
  Future<FormData> buildPostFormData({
    required String title,
    required String content,
    required XFile? image,
  }) async {
    return FormData.fromMap({
      'title': title,
      'content': content,
      'image': image != null
          ? await MultipartFile.fromFile(image.path, filename: image.name)
          : null,
    });
  }

  // build post form data
  Future<FormData> buildPostWithVideoFormData({
    required String userEmail,
    required String content,
    required XFile? video,
    required List<XFile> images,
  }) async {
    final videoFile = video != null
        ? await MultipartFile.fromFile(video.path, filename: video.name)
        : null;
    final imageFiles = images.isNotEmpty
        ? await Future.wait(
            images.map((image) =>
                MultipartFile.fromFile(image.path, filename: image.name)),
          )
        : null;
    return FormData.fromMap({
      'user': userEmail,
      'media': imageFiles != null && imageFiles.isNotEmpty
          ? imageFiles
          : (video != null ? [videoFile] : null),
      'content': content,
    });
  }
}
