import 'package:rasiin_tasks_app/core/services/flutter_secure_storage_services.dart';
import 'package:rasiin_tasks_app/core/constants/cache_key_constants.dart';

class LocalCacheServices {
  final FlutterSecureStorageServices flutterSecureStorageServices;

  LocalCacheServices({required this.flutterSecureStorageServices});

  // Get user email
  Future<String?> getUserEmail() async {
    return await flutterSecureStorageServices
        .readData(CacheKeyConstants.USER_EMAIL);
  }

  // Get employee id
  Future<String?> getEmployeeId() async {
    return await flutterSecureStorageServices
        .readData(CacheKeyConstants.EMPLOYEE_ID);
  }

  // Store user email
  Future<void> storeUserEmail(String? email) async {
    if (email == null || email.isEmpty) return;
    await flutterSecureStorageServices.storeData(
        CacheKeyConstants.USER_EMAIL, email);
  }

  // Store employee id
  Future<void> storeEmployeeId(String? employeeId) async {
    if (employeeId == null || employeeId.isEmpty) return;
    await flutterSecureStorageServices.storeData(
        CacheKeyConstants.EMPLOYEE_ID, employeeId);
  }

  // Delete user email and employee id
  Future<void> deleteUserData() async {
    await Future.wait(
      [
        flutterSecureStorageServices.removeData(CacheKeyConstants.USER_EMAIL),
        flutterSecureStorageServices.removeData(CacheKeyConstants.EMPLOYEE_ID),
      ],
    );
  }

  // Delete all data
  Future<void> deleteAllData() async {
    await flutterSecureStorageServices.deleteAllData();
  }
}
