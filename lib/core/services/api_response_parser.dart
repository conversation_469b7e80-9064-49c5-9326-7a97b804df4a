import 'package:rasiin_tasks_app/core/enums/api_response_key_enum.dart';

// ApiResponseParser class using the enum
class ApiResponseParser {
  // Generic method to extract data from a given response, handling various formats
  static List<T>? extractData<T>(
    Map<String, dynamic> response, {
    required List<T> Function(List<dynamic>) fromJsonList,
    ApiResponseKey key = ApiResponseKey.message,
  }) {
    print("Key is ${key.key}");
    dynamic data = response[
        ApiResponseKey.message.key]; // Use enum to get the 'message' key

    // Case 1: If 'message' contains an object with a 'data' field
    if (data is Map<String, dynamic> && data.containsKey(key.key)) {
      final dynamic keyData = data[key.key];
      if (keyData is List && keyData.isNotEmpty) {
        return fromJsonList(keyData);
      }
    }

    // Case 2: If 'message' is directly a List (no 'data' field)
    if (data is List && data.isNotEmpty) {
      // Check if the first element is a list (multiple nested lists)
      if (data[0] is List) {
        return fromJsonList(
            data[0]); // Extract and pass the first list to the parser
      } else {
        return fromJsonList(data); // Otherwise, parse as a flat list
      }
    }

    // Case 3: If 'message' is an array with data and status (i.e., response format contains a list of lists)
    if (data is List && data.length > 1 && data[0] is List) {
      final dynamic firstElement = data[0];
      if (firstElement is List && firstElement.isNotEmpty) {
        return fromJsonList(firstElement);
      }
    }

    // Return null if no matching format is found
    return null;
  }

  // Generic method to extract a single object from a given response, handling various formats
  static T? extractSingle<T>(
    Map<String, dynamic> response, {
    ApiResponseKey key = ApiResponseKey.message,
    required T Function(Map<String, dynamic>) fromJson,
  }) {
    print("Key is ${key.key}");
    dynamic data = response[
        ApiResponseKey.message.key]; // Use enum to get the 'message' key

    // Case 1: If 'message' contains a list and we need to extract the first element
    if (data is List && data.isNotEmpty) {
      final firstItem = data[0]; // Get the first element
      if (firstItem is Map<String, dynamic>) {
        return fromJson(firstItem); // Parse the first object in the list
      }
    }

    // Case 1: If 'message' contains an object with a 'data' field
    if (data is Map<String, dynamic> && data.containsKey(key.key)) {
      final dynamic keyData = data[key.key];
      if (keyData is Map<String, dynamic>) {
        return fromJson(keyData); // Parse the single object
      }
    }

    // Case 2: If 'message' is directly an object (no 'data' field)
    if (data is Map<String, dynamic>) {
      return fromJson(data); // Parse the single object
    }

    // Return null if no matching format is found
    return null;
  }

//   static List<T>? extractData<T>(
//   Map<String, dynamic> response,
//   List<String> keyPath, // Accept a list of keys to dynamically search for data
//   List<T> Function(List<dynamic>) fromJsonList,
// ) {
//   print("Searching with keys: $keyPath");

//   dynamic data = response;

//   // Traverse through the keyPath dynamically
//   for (var key in keyPath) {
//     data = data[key];
//     if (data == null) {
//       return null;
//     }
//   }

//   if (data is List && data.isNotEmpty) {
//     return fromJsonList(data);
//   }

//   return null;
// }
}

  // // Specific parser for Payroll data (custom for Payroll data structure)
  // static List<PayrollModel>? extractPayrollData(Map<String, dynamic> response) {
  //   print("extracting payroll data");
  //   return extractData<PayrollModel>(
  //       response, ApiResponseKey.data, PayrollModel.fromJsonList);
  // }

