import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService {
  final Connectivity _connectivity = Connectivity();

  /// Checks if the device is connected to a network and has internet access
  Future<bool> isConnected() async {
    // First, check if the device is connected to any network
    final connectivityResult = await _connectivity.checkConnectivity();
    // ignore: unrelated_type_equality_checks
    if (connectivityResult == ConnectivityResult.none) {
      // No network connection at all
      return false;
    }

    // Check for actual internet access by pinging a reliable host
    return await _hasInternetConnection();
  }

  /// Stream to listen for network changes (can be used for real-time updates)
  Stream<bool> get connectivityStream =>
      _connectivity.onConnectivityChanged.asyncMap(
        (connectivityResult) async {
          // Check actual internet access when network changes
          return await _hasInternetConnection();
        },
      );

  /// Method to check actual internet access by pinging a reliable server
  Future<bool> _hasInternetConnection() async {
    try {
      // Ping a reliable public server (Google DNS in this case)
      final result = await InternetAddress.lookup('google.com');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      }
    } catch (_) {
      return false; // No internet access
    }
    return false;
  }
}
