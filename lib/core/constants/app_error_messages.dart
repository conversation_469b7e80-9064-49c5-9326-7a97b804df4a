class AppErrorMessages {
  // HTTP Error Messages
  static const String httpBadRequest =
      'The request was invalid. Please check your input and try again.';
  static const String httpUnauthorized =
      'You are not authorized to access this resource. Please log in.';
  static const String httpForbidden =
      'You are not authorized to access this resource.';
  static const String httpNoDataFound = 'No data available.';
  static const String httpInvalidServerResponse =
      'Invalid server response. Please try again.';
  static const String httpTimeout =
      'The request timed out. Please check your internet connection and try again.';
  static const String httpNetworkError =
      'Please check your internet connection and try again.';
  static const String httpCancelled = 'The request was cancelled.';
  static String httpClientError(String? apiMessage) =>
      apiMessage ?? 'An error occurred on the client side. Please try again.';
  static const String httpServerError =
      'A server error occurred. Please try again later.';
  static const String httpUnknownError = 'Something went wrong. Please try again.';
  static const String httpUnExpectedError = 'An unexpected error occurred.';
  static const String httpExpectationFailed =
      'The expectation failed. Please try again.';
  static const String httpValidation =
      'The request was invalid. Please check your input and try again.';

  // Database Error Messages
  static const String dbConnectionError = 'Database Connection Error: ';
  static const String dbReadError = 'Database Read Error: ';
  static const String dbWriteError = 'Database Write Error: ';
  static const String dbQueryError = 'Database Query Error: ';
  static const String dbNoDataFound = 'No Data Found: ';
  static const String dbTransactionError = 'Database Transaction Error: ';
  static const String dbUnknownError = 'Unknown Database Error: ';
  static const String dbUnExpectedError =
      'An unexpected database error occurred: ';

  // Cache Error Messages
  static const String cacheNotFound = 'Cache Not Found: ';
  static const String cacheWriteError = 'Cache Write Error: ';
  static const String cacheReadError = 'Cache Read Error: ';
  static const String cacheDeleteError = 'Cache Delete Error: ';
  static const String cacheUnknownError = 'Unknown Cache Error: ';
  static const String cacheUnExpectedError = 'Unexpected Cache Error: ';

  // Validation Error Messages
  static String validationError(String message, String fieldName) =>
      'Validation Error: $message. Field: $fieldName';

  // Parsing Error Messages
  static String jsonParsingError(String message, dynamic expectedType) =>
      'JSON Parsing Error: $message. Expected type: $expectedType';
  static String xmlParsingError(String message, dynamic expectedType) =>
      'XML Parsing Error: $message. Expected type: $expectedType';
  static String typeConversionError(String message, dynamic expectedType) =>
      'Type Conversion Error: $message. Expected type: $expectedType';
  static String unknownParsingError(String message, dynamic expectedType) =>
      'Unknown Parsing Error: $message. Expected type: $expectedType';
  static String unexpectedParsingError(String message, dynamic expectedType) =>
      'Unexpected Parsing Error: $message. Expected type: $expectedType';

  // Unexpected Error Messages
  static String unexpectedError(String message) =>
      'An unexpected error occurred: $message';

  // Frappe Error Messages
  static String frappeValidationError(
          {String? serverMessage, required String message}) =>
      'Frappe ValidationError: ${serverMessage ?? message}. Please review your input and try again.';

  static String frappeMissingArgument(
          {String? serverMessage, required String message}) =>
      'Missing Argument Error: ${serverMessage ?? message}';
  static String frappeModuleNotFound(
          {String? serverMessage, required String message}) =>
      'Module Not Found: ${serverMessage ?? message}';
  static String frappePermissionError(
          {String? serverMessage, required String message}) =>
      'Permission Error: ${serverMessage ?? message}';
  static String frappeTypeError(
          {String? serverMessage, required String message}) =>
      'Type Error: ${serverMessage ?? message}';
  static String frappeUnknownError(
          {String? serverMessage, required String message}) =>
      'Unknown Error: ${serverMessage ?? message}';
  static String frappeUnexpectedError(
          {String? serverMessage, required String message}) =>
      'Unexpected Error: ${serverMessage ?? message}';

  // **🔥 Payment Error Messages**
  static const String paymentInsufficientBalance =
      'Your account balance is insufficient.';
  static const String paymentCustomerRejected =
      'Payment was rejected by the customer.';
  static const String paymentInvalidPinCode =
      'The provided PIN code is invalid.';
  static const String paymentInvalidMethod = 'Invalid payment method provided.';
  static const String paymentTransactionError =
      'There was an error processing your transaction.';
  static const String paymentCancelled = 'The payment was cancelled.';
  static const String paymentTimeout = 'The payment request timed out.';
  static const String paymentNetworkError =
      'Network error. Please check your internet connection.';
  static String paymentUnknownError(String message) =>
      'An unknown payment error occurred: $message.';
  static String paymentUnexpectedError(String message) =>
      'Unexpected payment error: $message.';
}
