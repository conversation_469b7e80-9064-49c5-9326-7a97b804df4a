class ErrorMessageConstants {
  // Authentication & Authorization Errors
  static const String unauthorized =
      "You are not authorized. Please log in again.";
  static const String tokenNotFound = "Token not found. Please log in again.";
  static const String forbidden =
      "You do not have permission to access this resource.";

  // Client Errors (4xx)
  static const String invalidRequestError =
      "Invalid request. Please check your data.";
  static const String dataNotFoundError = "The requested data was not found.";

  // Server Errors (5xx)
  static const String serverError =
      "An error occurred on the server. Please try again later.";
  static const String badGateway =
      "Bad gateway. The server is down or being updated.";
  static const String serviceUnavailable =
      "Service is currently unavailable. Please try again later.";

  // Network-related Errors
  static const String noInternetConnection =
      "No internet connection. Please check your network.";
  static const String timeoutError =
      "Request timed out. Please check your network connection.";
  static const String connectionLost = "Connection was lost. Please try again.";

  // Operation Errors
  static const String operationCancelled =
      "The operation was cancelled. No changes were made.";

  // empty data errors
  static const String emptyDataError = "No Data available.";

  // Generic Errors
  static const String unexpectedError =
      "An unexpected error occurred. Please try again.";
  static const String unknownError =
      "An unknown error occurred. Please try again.";

  // Invalid Response Errors
  static const String invalidServerResponse =
      "Unexpected response from the server.";
  static const String clientError =
      "A client-side error occurred. Please try again.";
}
