import 'package:rasiin_tasks_app/app/app_config.dart';

class ApiEndPoints {
  /// Base URL
  static String pdfBaseUrl = AppConfig.getPdfBaseUrl();
  static String baseUrl = AppConfig.getBaseUrl();

  //! attendance and salary slip
  static const String get_employee_attendance =
      "attendance.get_employee_attendance";
  static const String get_employee_salary_slip = "salary_slip.get_salary_slip";

  //! user
  static const String login = 'user.login_user';
  static const String get_auth_user = 'user.get_user_by_email';
  static const String get_all_users = 'user.get_all_employee';
  static const String change_password = 'user.change_password';
  static const String change_profile_image = 'user.upload_profile_image';
  static const String get_employee_profile = 'user.get_employee_profile';
  static const String get_user_dashboard = 'user.get_dashboard';
  //! customers
  // static const String get_customers = 'issue.get_customers';
  //! project
  static const String getProjects = 'create_task.get_project';
  //! tasks
  static const String createTask = 'create_task.create_task';
  static const String get_all_tasks = 'create_task.get_all_tasks';
  static const String get_assigning_task_project_id =
      'create_task.get_assigning_project_task_id';
  static const String get_assigned_tasks = 'create_task.get_assigned_tasks';
  // comments
  static const String send_comment_in_assigned_taks =
      'create_task.task_comments';
  static const String send_comment_in_alltaks = 'create_task.task_comments';
  static const String get_tasks_comment_taks = 'create_task.get_task_comments';

  static const String assign_task = "create_task.assign_task";
  static const String change_task_status = "create_task.assigned_task_status";
  //! issue
  static const String get_all_issues = "issue.get_issues";
  static const String get_assigned_issue = "issue.get_assigned_issue";
  static const String create_issue = "issue.create_issue";
  static const String assign_issue = "issue.assign_issue";
  static const String get_issue_type = "issue.get_issue_type";
  static const String change_issue_status =
      "issue.update_assigned_issue_status";
  static const String get_issue_comments = "issue.get_issue_comments";
  static const String add_issue_comment = "issue.add_issue_comment";
  //! visit
  static const String get_all_visits = "visit.get_all_visit";
  static const String get_all_visit_types = "visit.get_visit_type";
  static const String create_visit = "visit.create_visit";

  //! cos center
  // static const String get_cos_centers = "expense.get_all_cos_center";

  //! expense
  static const String create_expense = "expense.create_expense";
  static const String get_all_expenses = "expense.get_all_expenses";
  static const String get_all_departments = "expense.get_all_department_names";
  static const String approve_expense = "expense.submit_expense";
  static const String get_all_expense_accounts = "expense.get_all_accounts";
  static const String update_expense = "expense.update_expense";
  static const String get_all_cost_centers = "expense.get_all_cost_center";

  //! leave
  static const String get_all_leaves = "leave.get_all_leave_assignment";
  static const String get_leave_type = "leave.get_leave_type";
  static const String create_leave = "leave.create_leave";
  static const String approve_leave = "leave.approve_leave";

  //! sales invoice
  static const String get_sales_invoice = "sales_invoice.get_sales_invoice";
  static const String update_sales_invoice_work_state =
      "sales_invoice.update_sales_invoice_work_state";

  //! payment
  static const String get_all_payments = "sales_order.get_payment_entries";
  static const String create_payment = "sales_order.create_payment_entry";

  //! order
  static const String get_all_sales_orders = "sales_order.get_sales_orders";

  //! posts
  static const String get_all_posts = "post.get_posts_with_media";
  static const String create_post = "post.create_post_with_media";
  static const String get_post_comments = "post.get_comments_for_post";
  static const String comment_on_post = "post.comment_on_post";
  static const String toggle_like_post = "post.toggle_like_post";
  static const String create_poll = "post.create_poll_post";
  static const String vote_on_poll = "post.vote_on_poll";

  static const String get_doctor_appointments =
      "appointment.get_doctor_appointments";
}
