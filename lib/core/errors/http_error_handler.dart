import 'dart:async';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:fpdart/fpdart.dart';
import 'package:rasiin_tasks_app/core/constants/app_error_messages.dart';
import 'package:rasiin_tasks_app/core/enums/http_failure_type.dart';
import 'package:rasiin_tasks_app/core/errors/app_failure.dart';
import 'package:rasiin_tasks_app/core/models/api_response_model.dart';
import 'package:rasiin_tasks_app/core/network/connection_checker.dart';

class HttpErrorHandler {
  final ConnectionChecker connectionChecker;

  HttpErrorHandler({required this.connectionChecker});

  /// Handles Dio exceptions and maps them to `AppFailure`.
  AppFailure _handleDioError(DioException e) {
    HttpFailureType failureType;
    String errorMessage;

    // Extract API Message safely
    String? apiMessage;
    if (e.response?.data is Map<String, dynamic>) {
      final responseData = e.response!.data as Map<String, dynamic>;
      if (responseData.containsKey("message")) {
        final messageData = responseData["message"];
        if (messageData is String) {
          apiMessage = messageData;
        } else if (messageData is Map<String, dynamic> &&
            (messageData.containsKey("msg") ||
                messageData.containsKey("message"))) {
          apiMessage = messageData["msg"] ?? messageData["message"];
        }
      }
    }

    // Map DioException to `HttpFailureType`
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        failureType = HttpFailureType.timeout;
        errorMessage = AppErrorMessages.httpTimeout;
        break;
      case DioExceptionType.connectionError:
        failureType = HttpFailureType.network;
        errorMessage = AppErrorMessages.httpNetworkError;
        break;
      case DioExceptionType.badResponse:
        final bool isClientError = e.response?.statusCode != null &&
            e.response!.statusCode! >= 400 &&
            e.response!.statusCode! < 500;
        if (isClientError) {
          return _handleClientError(
            apiResponse: null,
            statusCode: e.response?.statusCode ?? 0,
            response: e.response?.data ?? {},
          );
        }

        failureType = isClientError
            ? HttpFailureType.clientError
            : HttpFailureType.serverError;
        errorMessage = AppErrorMessages.httpClientError(apiMessage);
        break;
      case DioExceptionType.cancel:
        failureType = HttpFailureType.cancelled;
        errorMessage = AppErrorMessages.httpCancelled;
        break;
      case DioExceptionType.unknown:
        if (e.error is SocketException || e.error is HttpException) {
          failureType = HttpFailureType.network;
          errorMessage = AppErrorMessages.httpNetworkError;
        } else {
          failureType = HttpFailureType.unknown;
          errorMessage = AppErrorMessages.httpUnknownError;
        }
        break;
      default:
        failureType = HttpFailureType.unknown;
        errorMessage = AppErrorMessages.httpUnknownError;
        break;
    }

    return HttpFailure(
      message: errorMessage,
      failureType: failureType,
      stackTrace: e.stackTrace,
      apiMessage: apiMessage,
      statusCode: e.response?.statusCode,
    );
  }

  /// Handles client-side errors (e.g., 4xx status codes) and returns an `AppFailure`.
  AppFailure _handleClientError<T>({
    required ApiResponseModel<T>? apiResponse,
    required int statusCode,
    required Map<String, dynamic> response,
  }) {
    String errorMessage = apiResponse?.apiMessage ?? '';
    HttpFailureType failureType;

    switch (statusCode) {
      case 400:
        failureType = HttpFailureType.badRequest;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 401:
        failureType = HttpFailureType.unauthorized;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 403:
        failureType = HttpFailureType.forbidden;
        // errorMessage = AppErrorMessages.httpForbidden;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
      case 404:
        failureType = HttpFailureType.noDataFound;
        // errorMessage = AppErrorMessages.httpNoDataFound;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;

      case 417:
        // failureType = HttpFailureType.expectationFailed;
        // errorMessage = AppErrorMessages.httpExpectationFailed;
        final error = FrappeFailure.fromFrappeException(response);
        return error;
      default:
        failureType = HttpFailureType.clientError;
        errorMessage = apiResponse?.apiMessage ?? '';
        break;
    }

    return HttpFailure(
      message: AppErrorMessages.httpClientError(errorMessage),
      apiMessage: errorMessage,
      failureType: failureType,
      statusCode: statusCode,
    );
  }

  /// Main method to handle network requests and errors.
  FutureEitherFailOr<ApiResponseModel<T>> handleRequest<T>({
    required Future<Response?> Function() requestFunction,
    T Function(Object?)? fromJsonT,
    bool skipConnectionCheck = false,
  }) async {
    try {
      // ✅ Only check the connection if `skipConnectionCheck` is false
      if (!skipConnectionCheck) {
        final isConnected = await connectionChecker.isConnected;
        if (!isConnected) {
          return left(
            HttpFailure(
              message: AppErrorMessages.httpNetworkError,
              apiMessage: AppErrorMessages.httpNetworkError,
              failureType: HttpFailureType.network,
            ),
          );
        }
      }

      // ✅ Make the network request
      final response = await requestFunction();

      // Validate the response format
      if (response == null || response.data is! Map<String, dynamic>) {
        return left(
          HttpFailure(
            message: AppErrorMessages.httpInvalidServerResponse,
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
          ),
        );
      }

      final responseBody = response.data as Map<String, dynamic>;

      // Parse the API response
      final apiResponse = ApiResponseModel<T>.fromJson(
        json: responseBody,
        fromJsonT: fromJsonT,
      );

      // Handle success
      if (apiResponse.isSuccess(statusCode: response.statusCode)) {
        return right(apiResponse);
      }

      // Handle client-side errors (4xx)
      if (apiResponse.isClientError(statusCode: response.statusCode)) {
        return left(
          _handleClientError(
            apiResponse: apiResponse,
            statusCode: response.statusCode!,
            response: responseBody,
          ),
        );
      }

      /// Handle server-side errors (5xx)
      /// Check if the response contains a Frappe exception
      if (responseBody.containsKey('exc_type') ||
          responseBody.containsKey('exception') ||
          responseBody.containsKey('exc')) {
        try {
          throw FrappeFailure.fromFrappeException(responseBody);
        } on FrappeFailure catch (error) {
          return left(
            FrappeFailure(
              message: error.message,
              failureType: error.failureType,
              stackTrace: error.stackTrace,
              serverMessage: error.serverMessage,
              serverStackTrace: error.serverStackTrace,
            ),
          );
        }
      }

      /// Handle other server-side errors
      return left(
        HttpFailure(
          message: "Server error occurred",
          failureType: HttpFailureType.serverError,
          statusCode: response.statusCode,
        ),
      );
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: "Unexpected error occurred",
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }

  FutureEitherFailOr<Uint8List> handleRequestBytes({
    required Future<Response?> Function() requestFunction,
  }) async {
    try {
      final response = await requestFunction();
      if (response == null || response.data is! List<int>) {
        return left(
          HttpFailure(
            message: AppErrorMessages.httpInvalidServerResponse,
            failureType: HttpFailureType.serverError,
            statusCode: response?.statusCode,
            apiMessage: "Invalid server response",
          ),
        );
      }
      final Uint8List bytes = Uint8List.fromList(response.data);
      return right(bytes);
    } on DioException catch (error) {
      return left(_handleDioError(error));
    } catch (error, stackTrace) {
      return left(
        HttpFailure(
          message: "Unexpected error occurred",
          failureType: HttpFailureType.unknown,
          stackTrace: stackTrace,
        ),
      );
    }
  }
}
