import 'package:html/parser.dart' as html_parser;

extension AppStringExtensions on String {
  String parseHtmlString() {
    final document = html_parser.parse(this);
    final parsedString =
        html_parser.parse(document.body?.text ?? "").documentElement?.text ??
            "";
    return parsedString.sanitizeUnicode();
  }

  /// Sanitizes string to remove invalid UTF-16 characters that cause rendering issues
  String sanitizeUnicode() {
    try {
      // Remove null characters and other problematic control characters
      String cleaned =
          replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

      // Remove invalid Unicode surrogate pairs
      cleaned = cleaned.replaceAll(RegExp(r'[\uD800-\uDFFF]'), '');

      // Remove any remaining invalid characters
      cleaned = cleaned.replaceAll(
          RegExp(r'[^\u0009\u000A\u000D\u0020-\uD7FF\uE000-\uFFFD]'), '');

      // Trim whitespace and normalize spaces
      cleaned = cleaned.trim().replaceAll(RegExp(r'\s+'), ' ');

      return cleaned;
    } catch (e) {
      print('⚠️ Unicode sanitization error: $e');
      // Fallback: return a safe version with only basic ASCII characters
      return replaceAll(RegExp(r'[^\x20-\x7E]'), '').trim();
    }
  }

  /// Safe text getter that ensures the string is valid for UI rendering
  String get safeText => sanitizeUnicode();
}
