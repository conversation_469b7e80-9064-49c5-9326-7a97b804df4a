import 'package:intl/intl.dart';

extension MoneyFormatting on num {
  // Method to format a number as money
  String toMoneyString({
    String locale = 'en_US',
    String symbol = '\$', // USD, EUR, INR,
    int decimalPlaces = 2,
    String customPattern = '¤#,##0.00',
    bool showSign = false,
    bool showSymbol = true,
    bool isExpense = false,
    bool trimZeros = false, // NEW FEATURE
  }) {
    // Use the symbol only if showSymbol is true
    final String effectiveSymbol = showSymbol ? symbol : '';

    // Create the number format with custom pattern and options
    final format = NumberFormat.currency(
      locale: locale,
      symbol: effectiveSymbol,
      decimalDigits: decimalPlaces,
      customPattern: customPattern,
    );

    // Format the absolute value (so we control sign ourselves)
    String formattedValue = format.format(this.abs());

    // Trim .00 if needed and no fractional part
    if (trimZeros && formattedValue.contains('.')) {
      formattedValue = formattedValue.replaceAll(RegExp(r'\.00\b'), '');
    }

    // Conditionally add the "+" or "-" sign
    if (showSign) {
      formattedValue = isExpense ? '-$formattedValue' : '+$formattedValue';
    }

    return formattedValue;
  }
}
