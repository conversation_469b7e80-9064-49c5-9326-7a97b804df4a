import 'package:intl/intl.dart';
import 'package:rasiin_tasks_app/core/enums/date_format_type_enum.dart';

extension DateFormatting on DateTime {
  // Method to format DateTime based on DateFormatType
  String toFormattedString(
      {DateFormatType formatType = DateFormatType.yearMonthDay}) {
    String formattedDate;

    // Select the date format based on the enum value
    switch (formatType) {
      case DateFormatType.yearMonthDay:
        formattedDate = DateFormat("yyyy-MM-dd").format(this); // 2020-10-23
        break;
      case DateFormatType.dayMonthYear:
        formattedDate = DateFormat("d-M-yyyy").format(this); // 10-3-2023
        break;
      case DateFormatType.dayTextMonthYear:
        formattedDate = DateFormat("d-MMM-yyyy").format(this); // 23-Jan-2022
        break;
      case DateFormatType.dayShortMonthYear:
        formattedDate = DateFormat("dd-MMM-yyyy").format(this); // 23-Jan-2022
        break;
      case DateFormatType.fullTextDate:
        formattedDate = DateFormat("EEEE, d MMMM yyyy")
            .format(this); // Monday, 1 January 2024
        break;
      case DateFormatType.shortTextDate:
        formattedDate =
            DateFormat("d MMMM yyyy").format(this); // 1 January 2024
        break;
      case DateFormatType.shortDate: // New format: 15 Nov
        formattedDate = DateFormat("d MMM").format(this); // 15 Nov
        break;
      case DateFormatType.dayNameOnly: // New format: Friday
        formattedDate = DateFormat("EEEE").format(this); // Friday
        break;
      case DateFormatType.dayNumberWithName: // 07 Wednesday
        formattedDate = DateFormat("dd EEEE").format(this); // 07 Wednesday
        break;
    }

    return formattedDate;
  }

  String toTimeString() {
    return DateFormat("HH:mm").format(this);
  }

  String get monthName {
    switch (month) {
      case 1:
        return 'January';
      case 2:
        return 'February';
      case 3:
        return 'March';
      case 4:
        return 'April';
      case 5:
        return 'May';
      case 6:
        return 'June';
      case 7:
        return 'July';
      case 8:
        return 'August';
      case 9:
        return 'September';
      case 10:
        return 'October';
      case 11:
        return 'November';
      case 12:
        return 'December';
      default:
        return '';
    }
  }
}

extension TimeAgoExtension on DateTime {
  /// Returns a human-readable time difference string (e.g., "2 minutes ago")
  /// [capitalizeFirstLetter] - Whether to capitalize the first letter (default: true)
  /// [showAgo] - Whether to show "ago" at the end (default: true)
  String timeAgo({bool capitalizeFirstLetter = true, bool showAgo = true}) {
    final now = DateTime.now();
    final difference = now.difference(this);

    if (difference.isNegative) {
      return _formatTimeAgoString('soon', capitalizeFirstLetter,
          showAgo: false);
    }

    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return _formatTimeAgoString(
        '$years year${years > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return _formatTimeAgoString(
        '$months month${months > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    } else if (difference.inDays > 0) {
      return _formatTimeAgoString(
        '${difference.inDays} day${difference.inDays > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    } else if (difference.inHours > 0) {
      return _formatTimeAgoString(
        '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    } else if (difference.inMinutes > 0) {
      return _formatTimeAgoString(
        '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    } else {
      return _formatTimeAgoString(
        '${difference.inSeconds} second${difference.inSeconds > 1 ? 's' : ''}',
        capitalizeFirstLetter,
        showAgo: showAgo,
      );
    }
  }

  /// Returns a human-readable time remaining string (e.g., "2 minutes left")
  /// This is useful for countdowns like poll expiry
  String timeLeft({bool capitalizeFirstLetter = true}) {
    final now = DateTime.now();
    final difference = this.difference(now);

    if (difference.isNegative) {
      return 'Ended';
    }

    if (difference.inDays > 0) {
      return _formatTimeLeftString(
        '${difference.inDays} day${difference.inDays > 1 ? 's' : ''}',
        capitalizeFirstLetter,
      );
    } else if (difference.inHours > 0) {
      return _formatTimeLeftString(
        '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}',
        capitalizeFirstLetter,
      );
    } else if (difference.inMinutes > 0) {
      return _formatTimeLeftString(
        '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}',
        capitalizeFirstLetter,
      );
    } else {
      return _formatTimeLeftString(
        '${difference.inSeconds} second${difference.inSeconds > 1 ? 's' : ''}',
        capitalizeFirstLetter,
      );
    }
  }

  String _formatTimeAgoString(
    String value,
    bool capitalizeFirstLetter, {
    bool showAgo = true,
  }) {
    var result = showAgo ? '$value ago' : value;
    if (capitalizeFirstLetter && result.isNotEmpty) {
      result = result[0].toUpperCase() + result.substring(1);
    }
    return result;
  }

  String _formatTimeLeftString(String value, bool capitalizeFirstLetter) {
    var result = '$value left';
    if (capitalizeFirstLetter && result.isNotEmpty) {
      result = result[0].toUpperCase() + result.substring(1);
    }
    return result;
  }
}

// to convert string to dateTime
extension StringToDateTime on String {
  DateTime? toDateTime() {
    return DateTime.tryParse(this);
  }
}
