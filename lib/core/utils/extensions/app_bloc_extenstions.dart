import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/attendence%20bloc/attendence_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/authentication%20bloc/authentication_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/bloc/payment_bloc/payment_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/payroll%20bloc/payroll_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/sales%20invoice/sales_invoice_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/visit%20bloc/visit_bloc.dart';

import '../../bloc/appointment_bloc/appointment_bloc.dart';
import '../../bloc/expense bloc/expense_bloc.dart';
import '../../bloc/issue bloc/issue_bloc.dart';
import '../../bloc/leave bloc/leave_bloc.dart';
import '../../bloc/notification bloc/notification_bloc.dart';
import '../../bloc/sales order bloc/sales_order_bloc.dart';
import '../../bloc/users bloc/users_bloc.dart';

extension AppBlocExtenstions on BuildContext {
  AuthenticationBloc get authenticationBloc =>
      BlocProvider.of<AuthenticationBloc>(this);

  UsersBloc get usersBloc {
    return BlocProvider.of<UsersBloc>(this);
  }

  TasksBloc get tasksBloc {
    return BlocProvider.of<TasksBloc>(this);
  }

  // CustomersBloc get customersBloc {
  //   return BlocProvider.of<CustomersBloc>(this);
  // }

  IssueBloc get issuesBloc {
    return BlocProvider.of<IssueBloc>(this);
  }

  NotificationBloc get notificationBloc {
    return BlocProvider.of<NotificationBloc>(this);
  }

  VisitBloc get visitBloc {
    return BlocProvider.of<VisitBloc>(this);
  }

  ExpenseBloc get expenseBloc {
    return BlocProvider.of<ExpenseBloc>(this);
  }

  LeaveBloc get leaveBloc {
    return BlocProvider.of<LeaveBloc>(this);
  }

  PayrollBloc get payrollBloc {
    return BlocProvider.of<PayrollBloc>(this);
  }

  AttendanceBloc get attendenceBloc {
    return BlocProvider.of<AttendanceBloc>(this);
  }

  DialogCubit get dialogCubit {
    return BlocProvider.of<DialogCubit>(this);
  }

  PaymentBloc get paymentBloc {
    return BlocProvider.of<PaymentBloc>(this);
  }

  SalesInvoiceBloc get salesInvoiceBloc {
    return BlocProvider.of<SalesInvoiceBloc>(this);
  }

  SalesOrderBloc get salesOrderBloc {
    return BlocProvider.of<SalesOrderBloc>(this);
  }

  PostBloc get postBloc {
    return BlocProvider.of<PostBloc>(this);
  }

  AppointmentBloc get appointmentBloc {
    return BlocProvider.of<AppointmentBloc>(this);
  }
}
