import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/dialogs/custom_dialog.dart';

void listenDialogCubit(BuildContext context, DialogState state) {
  if (state is DialogOpened) {
    final dialogWidget = PopScope(
      canPop: state.barrierDismissible,
      child: CustomDialog(
        dialogType: state.dialogType,
        title: state.title,
        message: state.message,
        cancelBtnText: state.cancelButtonText ?? 'Cancel',
        confirmBtnText: state.confirmButtonText ?? 'OK',
        cancelButtonwidth: state.cancelButtonWidth,
        confirmButtonwidth: state.confirmButtonWidth,
        cancelButtonHeight: state.cancelButtonHeight ?? 30,
        confirmButtonHeight: state.confirmButtonHeight ?? 30,
        onConfirm: () {
          context.dialogCubit.closeDialog();
          state.onConfirm?.call();
        },
        onCancel: () {
          context.dialogCubit.closeDialog();
          state.onCancel?.call();
        },
      ),
    );

    if (Theme.of(context).platform == TargetPlatform.iOS) {
      showCupertinoDialog(
        context: context,
        barrierDismissible: state.barrierDismissible,
        builder: (_) => dialogWidget,
      );
    } else {
      showDialog(
        context: context,
        barrierDismissible: state.barrierDismissible,
        builder: (_) => dialogWidget,
      );
    }

    // Add auto-close functionality
    // if (state.enableAutoClose) {
    //   Future.delayed(state.autoCloseDuration ?? Duration(seconds: 5), () {
    //     if (Navigator.of(context, rootNavigator: true).canPop()) {
    //       Navigator.of(context, rootNavigator: true).pop();
    //     }
    //   });
    // }
  } else if (state is DialogClosed) {
    if (Navigator.of(context, rootNavigator: true).canPop()) {
      Navigator.of(context, rootNavigator: true).pop();
    }
  }
}
