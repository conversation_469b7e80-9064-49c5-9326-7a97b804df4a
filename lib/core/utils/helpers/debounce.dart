import 'package:flutter_bloc/flutter_bloc.dart' show EventTransformer;
import 'package:stream_transform/stream_transform.dart';

class DebounceHelper {
  /// A utility function that creates a debounce transformer for event streams
  /// [duration] The duration to wait before processing subsequent events
  static EventTransformer<T> blocDebouncer<T>({
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return (events, mapper) => events.debounce(duration).switchMap(mapper);
  }


}
