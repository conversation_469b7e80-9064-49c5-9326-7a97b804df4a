import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/models/attendence_model.dart';

class AttendanceSummary {
  final int totalDays;
  final int presentDays;
  final int absentDays;
  final int onLeaveDays;
  final int halfDayDays;
  final int workFromHomeDays;

  AttendanceSummary({
    required this.totalDays,
    required this.presentDays,
    required this.absentDays,
    required this.onLeaveDays,
    required this.halfDayDays,
    required this.workFromHomeDays,
  });

  factory AttendanceSummary.fromAttendances(
    List<AttendenceModel> attendances, {
    required int year,
    required int month,
  }) {
    final totalDaysInMonth = DateUtils.getDaysInMonth(year, month);

    int _countByStatus(String status) => attendances
        .where((att) => att.status.toLowerCase() == status.toLowerCase())
        .length;

    return AttendanceSummary(
      totalDays: totalDaysInMonth,
      presentDays: _countByStatus('Present'),
      absentDays: _countByStatus('Absent'),
      onLeaveDays: _countByStatus('On Leave'),
      halfDayDays: _countByStatus('Half day'),
      workFromHomeDays: _countByStatus('Work from home'),
    );
  }

  int get totalActualDays {
    final total =
        presentDays + absentDays + onLeaveDays + halfDayDays + workFromHomeDays;
    return total;
  }

  int get totalUnrecordedDays {
    return totalDays - totalActualDays;
  }

  double get presentPercentage => totalDays > 0 ? presentDays / totalDays : 0;
  double get absentPercentage => totalDays > 0 ? absentDays / totalDays : 0;
  double get onLeavePercentage => totalDays > 0 ? onLeaveDays / totalDays : 0;
  double get halfDayPercentage => totalDays > 0 ? halfDayDays / totalDays : 0;
  double get workFromHomePercentage =>
      totalDays > 0 ? workFromHomeDays / totalDays : 0;
  double get unrecordedPercentage =>
      totalDays > 0 ? totalUnrecordedDays / totalDays : 0;
}
