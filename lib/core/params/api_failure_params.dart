enum ApiErrorType {
  noInternet,
  timeout,
  authentication,
  cancelled,
  serverError,
  clientError,
  emptyData,
  unknown,
}

class ApiFailureParams {
  final String errorMessage;
  final String? apiErrorMessage;
  final int? statusCode;
  final StackTrace? stackTrace;
  final ApiErrorType errorType;

  ApiFailureParams({
    required this.errorMessage,
    this.apiErrorMessage,
    this.stackTrace,
    this.statusCode,
    this.errorType = ApiErrorType.unknown, // Default to unknown error type
  });
}
