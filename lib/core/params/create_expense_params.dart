import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';

class CreateExpenseParams {
  final String uniqueId;
  final String remark;
  final String departmentName;
  final String userEmail;
  final DateTime? date;
  final double amount;

  const CreateExpenseParams({
    required this.uniqueId,
    required this.remark,
    required this.departmentName,
    required this.userEmail,
    required this.date,
    required this.amount,
  });

  Map<String, dynamic> toMap({
    required List<CreateExpenseParams> expenses,
  }) {
    return <String, dynamic>{
      'expenses': CreateExpenseParams.toJsonList(expenses),
    };
  }

  // Convert the Expense object to a map (which can be converted to JSON)
  Map<String, dynamic> toJson() {
    return {
      'department_name': departmentName,
      'remark': remark,
      'amount': amount,
      'date': date?.toFormattedString(),
      'user_name': userEmail,
    };
  }

  static List<Map<String, dynamic>> toJsonList(List<CreateExpenseParams> data) {
    return data.map((expense) => expense.toJson()).toList();
  }

  static String generateUniqueId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
