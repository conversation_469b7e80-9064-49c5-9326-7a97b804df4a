import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:rasiin_tasks_app/core/bloc/attendence%20bloc/attendence_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/authentication%20bloc/authentication_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/bloc/expense%20bloc/expense_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/leave%20bloc/leave_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/payment_bloc/payment_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/payroll%20bloc/payroll_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/visit%20bloc/visit_bloc.dart';
import 'package:rasiin_tasks_app/core/database/attendence_database_manager.dart';
import 'package:rasiin_tasks_app/core/database/database_manager.dart';
import 'package:rasiin_tasks_app/core/database/payroll_database_manager.dart';
import 'package:rasiin_tasks_app/core/database/post_database_manager.dart';
import 'package:rasiin_tasks_app/core/database/user_database_manager.dart';
import 'package:rasiin_tasks_app/core/errors/database_error_handler.dart';
import 'package:rasiin_tasks_app/core/errors/http_error_handler.dart';
import 'package:rasiin_tasks_app/core/network/api_client/dio_api_client.dart';
import 'package:rasiin_tasks_app/core/network/connection_checker.dart';
import 'package:rasiin_tasks_app/core/network/dio_client_factory.dart';
import 'package:rasiin_tasks_app/core/repository/attendence_repository.dart';
import 'package:rasiin_tasks_app/core/repository/auth_repository.dart';
import 'package:rasiin_tasks_app/core/repository/expense_repository.dart';
import 'package:rasiin_tasks_app/core/repository/issue_repository.dart';
import 'package:rasiin_tasks_app/core/repository/leave_repository.dart';
import 'package:rasiin_tasks_app/core/repository/notification_repository.dart';
import 'package:rasiin_tasks_app/core/repository/payment_repository.dart';
import 'package:rasiin_tasks_app/core/repository/payroll_repository.dart';
import 'package:rasiin_tasks_app/core/repository/post_repository.dart';
import 'package:rasiin_tasks_app/core/repository/sales_invoice_repository.dart';
import 'package:rasiin_tasks_app/core/repository/sales_order_repository.dart';
import 'package:rasiin_tasks_app/core/repository/task_repository.dart';
import 'package:rasiin_tasks_app/core/repository/users_repository.dart';
import 'package:rasiin_tasks_app/core/repository/visit_repository.dart';
import 'package:rasiin_tasks_app/core/services/connectivity_services.dart';
import 'package:rasiin_tasks_app/core/services/file_picker_services.dart';
import 'package:rasiin_tasks_app/core/services/flutter_secure_storage_services.dart';
import 'package:rasiin_tasks_app/core/services/isolate_services.dart';
import 'package:rasiin_tasks_app/core/services/local_cache_services.dart';
import 'package:rasiin_tasks_app/core/services/multipart_services.dart';
import 'package:rasiin_tasks_app/core/services/notification_services.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/data_repository_helper.dart';

import '../core/bloc/appointment_bloc/appointment_bloc.dart';
import '../core/bloc/notification bloc/notification_bloc.dart';
import '../core/bloc/sales invoice/sales_invoice_bloc.dart';
import '../core/bloc/sales order bloc/sales_order_bloc.dart';
import '../core/repository/appointment_repository.dart';

GetIt getIt = GetIt.instance;

Future<void> registerDependencies() async {
  print('🛠️ Starting registerDependencies...');
  await registerDatabase();
  print('📦 Database registered.');

  registerServices();
  print('🔧 Services registered.');

  registerRepositories();
  print('📁 Repositories registered.');

  registerBloc();
  print('⚙️ BLoC registered.');
}

//! database
Future<void> registerDatabase() async {
  final databaseManager = await DatabaseManager();
  await databaseManager.init();
  getIt.registerLazySingleton<DatabaseManager>(() => databaseManager);

  getIt.registerLazySingleton<DatabaseErrorHandler>(
    () => DatabaseErrorHandler(databaseManager: getIt<DatabaseManager>()),
  );

  getIt.registerLazySingleton<PayrollDatabaseManager>(
    () => PayrollDatabaseManager(
      databaseManager: getIt<DatabaseManager>(),
      databaseErrorHandler: getIt<DatabaseErrorHandler>(),
    ),
  );

  getIt.registerLazySingleton<AttendenceDatabaseManager>(
    () => AttendenceDatabaseManager(
      databaseManager: getIt<DatabaseManager>(),
      databaseErrorHandler: getIt<DatabaseErrorHandler>(),
    ),
  );

  getIt.registerLazySingleton<PostDatabaseManager>(
    () => PostDatabaseManager(
      databaseManager: getIt<DatabaseManager>(),
      databaseErrorHandler: getIt<DatabaseErrorHandler>(),
    ),
  );
  getIt.registerLazySingleton<UserDatabaseManager>(
    () => UserDatabaseManager(
      databaseManager: getIt<DatabaseManager>(),
      databaseErrorHandler: getIt<DatabaseErrorHandler>(),
    ),
  );
}

//! services
void registerServices() {
  if (!getIt.isRegistered<DataRepositoryHelper>()) {
    getIt.registerLazySingleton<DataRepositoryHelper>(
      () => DataRepositoryHelper(),
    );
  }

  if (!getIt.isRegistered<FlutterSecureStorageServices>()) {
    getIt.registerLazySingleton<FlutterSecureStorageServices>(
      () => FlutterSecureStorageServices(),
    );
  }

  if (!getIt.isRegistered<ConnectionChecker>()) {
    getIt.registerLazySingleton<ConnectionChecker>(
      () => ConnectionCheckerImpl(),
    );
  }

  if (!getIt.isRegistered<IsolateServices>()) {
    getIt.registerLazySingleton<IsolateServices>(
      () => IsolateServices(),
    );
  }

  if (!getIt.isRegistered<LocalCacheServices>()) {
    getIt.registerLazySingleton<LocalCacheServices>(
      () => LocalCacheServices(
        flutterSecureStorageServices: getIt<FlutterSecureStorageServices>(),
      ),
    );
  }

  // Other services
  if (!getIt.isRegistered<ConnectivityService>()) {
    getIt.registerLazySingleton<ConnectivityService>(
      () => ConnectivityService(),
    );
  }

  if (!getIt.isRegistered<HttpErrorHandler>()) {
    getIt.registerLazySingleton<HttpErrorHandler>(
      () => HttpErrorHandler(
        connectionChecker: getIt<ConnectionChecker>(),
      ),
    );
  }

  if (!getIt.isRegistered<MultiPartServices>()) {
    getIt.registerLazySingleton<MultiPartServices>(
      () => MultiPartServices(),
    );
  }

  if (!getIt.isRegistered<NotificationService>()) {
    getIt.registerLazySingleton<NotificationService>(
      () => NotificationService(),
    );
  }

  if (!getIt.isRegistered<Dio>()) {
    getIt.registerLazySingleton<Dio>(() => createDioClient());
  }

  if (!getIt.isRegistered<DioApiClient>()) {
    getIt.registerLazySingleton<DioApiClient>(
      () => DioApiClient(dio: getIt<Dio>()),
    );
  }

  if (!getIt.isRegistered<FilePickerServices>()) {
    getIt.registerLazySingleton<FilePickerServices>(
      () => FilePickerServices(),
    );
  }
}

//! repository
void registerRepositories() {
  if (!getIt.isRegistered<AuthRepository>()) {
    getIt.registerLazySingleton<AuthRepository>(
      () => AuthRepository(
        dioApiClient: getIt<DioApiClient>(),
        localCacheServices: getIt<LocalCacheServices>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
        databaseManager: getIt<DatabaseManager>(),
      ),
    );
  }

  if (!getIt.isRegistered<UsersRepository>()) {
    getIt.registerLazySingleton<UsersRepository>(
      () => UsersRepository(
        dioApiClient: getIt<DioApiClient>(),
        multiPartServices: getIt<MultiPartServices>(),
        localCacheServices: getIt<LocalCacheServices>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dataRepositoryHelper: getIt<DataRepositoryHelper>(),
        userDatabaseManager: getIt<UserDatabaseManager>(),
      ),
    );
  }

  if (!getIt.isRegistered<TaskRepository>()) {
    getIt.registerLazySingleton<TaskRepository>(
      () => TaskRepository(
        dioApiClient: getIt<DioApiClient>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
      ),
    );
  }

  // if (!getIt.isRegistered<CustomerRepository>()) {
  //   getIt.registerLazySingleton<CustomerRepository>(
  //     () => CustomerRepository(
  //       dioApiClient: getIt<DioApiClient>(),
  //       httpErrorHandler: getIt<HttpErrorHandler>(),
  //     ),
  //   );
  // }
  if (!getIt.isRegistered<VisitRepository>()) {
    getIt.registerLazySingleton<VisitRepository>(
      () => VisitRepository(
        dioApiClient: getIt<DioApiClient>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
      ),
    );
  }

  if (!getIt.isRegistered<IssueRepository>()) {
    getIt.registerLazySingleton<IssueRepository>(
      () => IssueRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }

  if (!getIt.isRegistered<ExpenseRepository>()) {
    getIt.registerLazySingleton<ExpenseRepository>(
      () => ExpenseRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }

  if (!getIt.isRegistered<LeaveRepository>()) {
    getIt.registerLazySingleton<LeaveRepository>(
      () => LeaveRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }

  if (!getIt.isRegistered<NotificationRepository>()) {
    getIt.registerLazySingleton<NotificationRepository>(
      () => NotificationRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }

  if (!getIt.isRegistered<PayrollRepository>()) {
    getIt.registerLazySingleton<PayrollRepository>(
      () => PayrollRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
        filePickerServices: getIt<FilePickerServices>(),
        payrollDatabaseManager: getIt<PayrollDatabaseManager>(),
        dataRepositoryHelper: getIt<DataRepositoryHelper>(),
      ),
    );
  }

  if (!getIt.isRegistered<AttendanceRepository>()) {
    getIt.registerLazySingleton<AttendanceRepository>(
      () => AttendanceRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
        attendenceDatabaseManager: getIt<AttendenceDatabaseManager>(),
        dataRepositoryHelper: getIt<DataRepositoryHelper>(),
      ),
    );
  }

  if (!getIt.isRegistered<PaymentRepository>()) {
    getIt.registerLazySingleton<PaymentRepository>(
      () => PaymentRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }

  if (!getIt.isRegistered<SalesInvoiceRepository>()) {
    getIt.registerLazySingleton<SalesInvoiceRepository>(
      () => SalesInvoiceRepository(
        dioApiClient: getIt<DioApiClient>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
      ),
    );
  }

  if (!getIt.isRegistered<SalesOrderRepository>()) {
    getIt.registerLazySingleton<SalesOrderRepository>(
      () => SalesOrderRepository(
        dioApiClient: getIt<DioApiClient>(),
        httpErrorHandler: getIt<HttpErrorHandler>(),
      ),
    );
  }

  if (!getIt.isRegistered<PostRepository>()) {
    getIt.registerLazySingleton<PostRepository>(
      () => PostRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
        filePickerServices: getIt<FilePickerServices>(),
        dataRepositoryHelper: getIt<DataRepositoryHelper>(),
        postDatabaseManager: getIt<PostDatabaseManager>(),
      ),
    );
  }

  if (!getIt.isRegistered<AppointmentRepository>()) {
    getIt.registerLazySingleton<AppointmentRepository>(
      () => AppointmentRepository(
        httpErrorHandler: getIt<HttpErrorHandler>(),
        dioApiClient: getIt<DioApiClient>(),
      ),
    );
  }
}

//! bloc
void registerBloc() {
  if (!getIt.isRegistered<TasksBloc>()) {
    getIt.registerFactory<TasksBloc>(
      () => TasksBloc(
        taskRepository: getIt<TaskRepository>(),
        notificationRepository: getIt<NotificationRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<AuthenticationBloc>()) {
    getIt.registerFactory<AuthenticationBloc>(
      () => AuthenticationBloc(
        authRepository: getIt<AuthRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<UsersBloc>()) {
    getIt.registerFactory<UsersBloc>(
      () => UsersBloc(
        usersRepository: getIt<UsersRepository>(),
        flutterSecureStorageServices: getIt<FlutterSecureStorageServices>(),
      ),
    );
  }

  // if (!getIt.isRegistered<CustomersBloc>()) {
  //   getIt.registerFactory<CustomersBloc>(
  //     () => CustomersBloc(
  //       customerRepository: getIt<CustomerRepository>(),
  //     ),
  //   );
  // }

  if (!getIt.isRegistered<IssueBloc>()) {
    getIt.registerFactory<IssueBloc>(
      () => IssueBloc(
        issueRepository: getIt<IssueRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<NotificationBloc>()) {
    getIt.registerFactory<NotificationBloc>(
      () => NotificationBloc(
        notificationRepository: getIt<NotificationRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<VisitBloc>()) {
    getIt.registerFactory<VisitBloc>(
      () => VisitBloc(
        visitRepository: getIt<VisitRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<ExpenseBloc>()) {
    getIt.registerFactory<ExpenseBloc>(
      () => ExpenseBloc(
        expenseRepository: getIt<ExpenseRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<LeaveBloc>()) {
    getIt.registerFactory<LeaveBloc>(
      () => LeaveBloc(
        leaveRepository: getIt<LeaveRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<AttendanceBloc>()) {
    getIt.registerFactory<AttendanceBloc>(
      () => AttendanceBloc(
        attendanceRepository: getIt<AttendanceRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<PayrollBloc>()) {
    getIt.registerFactory<PayrollBloc>(
      () => PayrollBloc(
        payrollRepository: getIt<PayrollRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<DialogCubit>()) {
    getIt.registerFactory<DialogCubit>(
      () => DialogCubit(),
    );
  }

  if (!getIt.isRegistered<PaymentBloc>()) {
    getIt.registerFactory<PaymentBloc>(
      () => PaymentBloc(
        paymentRepository: getIt<PaymentRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<SalesInvoiceBloc>()) {
    getIt.registerFactory<SalesInvoiceBloc>(
      () => SalesInvoiceBloc(
        salesInvoiceRepository: getIt<SalesInvoiceRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<SalesOrderBloc>()) {
    getIt.registerFactory<SalesOrderBloc>(
      () => SalesOrderBloc(
        salesOrderRepository: getIt<SalesOrderRepository>(),
      ),
    );
  }

  if (!getIt.isRegistered<PostBloc>()) {
    getIt.registerFactory<PostBloc>(
      () => PostBloc(
        postRepository: getIt<PostRepository>(),
      ),
    );
  }


  if (!getIt.isRegistered<AppointmentBloc>()) {
    getIt.registerFactory<AppointmentBloc>(
      () => AppointmentBloc(
        appointmentRepository: getIt<AppointmentRepository>(),
      ),
    );
  }
}
