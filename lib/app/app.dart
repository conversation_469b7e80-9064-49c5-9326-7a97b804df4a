import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/app_dependencies.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/app/app_routes.dart';
import 'package:rasiin_tasks_app/app/theme/app_theme.dart';
import 'package:rasiin_tasks_app/core/bloc/attendence%20bloc/attendence_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/bloc/expense%20bloc/expense_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/leave%20bloc/leave_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/notification%20bloc/notification_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/payment_bloc/payment_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/payroll%20bloc/payroll_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/visit%20bloc/visit_bloc.dart';
import 'package:rasiin_tasks_app/features/common/screens/login_screen.dart';
import 'package:rasiin_tasks_app/features/main/screens/main_screen.dart';
import '../core/bloc/appointment_bloc/appointment_bloc.dart';
import '../core/bloc/authentication%20bloc/authentication_bloc.dart';
import '../core/bloc/sales invoice/sales_invoice_bloc.dart';
import '../core/bloc/sales order bloc/sales_order_bloc.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          lazy: false,
          create: (context) => getIt<AuthenticationBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<UsersBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<TasksBloc>(),
        ),
        // BlocProvider(
        //   create: (context) => getIt<CustomersBloc>(),
        // ),
        BlocProvider(
          create: (context) => getIt<IssueBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<NotificationBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<VisitBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<ExpenseBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<LeaveBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<PayrollBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<AttendanceBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<DialogCubit>(),
        ),
        BlocProvider(
          create: (context) => getIt<PaymentBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<SalesInvoiceBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<SalesOrderBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<PostBloc>(),
        ),
        BlocProvider(
          create: (context) => getIt<AppointmentBloc>(),
        ),
      ],
      child: ScreenUtilInit(
        designSize: const Size(360, 690),
        minTextAdapt: true,
        splitScreenMode: true,
        builder: (context, child) {
          return BlocBuilder<UsersBloc, UsersState>(
            builder: (context, state) {
              return MaterialApp(
                navigatorKey: navigatorKey,
                title: 'Rasiin Tasks App',
                debugShowCheckedModeBanner: false,
                onGenerateRoute: AppRoutes.onGenerateRoute,
                darkTheme: AppTheme.darkMode,
                theme: AppTheme.lightMode,
                themeMode: context.watch<UsersBloc>().currentThemeMode,
                home: const AuthScreen(),
              );
            },
          );
        },
      ),
    );
  }
}

class AuthScreen extends StatefulWidget {
  const AuthScreen({super.key});

  @override
  State<AuthScreen> createState() => _AuthScreenState();
}

class _AuthScreenState extends State<AuthScreen> {
  bool isAuthenticated = false;

  @override
  void initState() {
    super.initState();
    _checkAuthentication();
  }

  Future<void> _checkAuthentication() async {
    try {
      // Trigger the authentication check
      context.read<AuthenticationBloc>().add(AuthenticationCheckEvent());

      // Wait for the authentication state to change
      await context.read<AuthenticationBloc>().stream.firstWhere((state) {
        return state is AuthenticationStateAuthenticated ||
            state is AuthenticationStateInitial;
      });

      // Remove the splash screen after authentication state is resolved
      FlutterNativeSplash.remove();

      // Navigate based on authentication status
      final authState = context.read<AuthenticationBloc>().state;
      if (authState is AuthenticationStateAuthenticated) {
        setState(() {
          isAuthenticated = true;
        });
      } else {
        setState(() {
          isAuthenticated = false;
        });
      }
    } catch (error, stackTrace) {
      AppLogger().error(
        "Error during authentication process in AuthScreen",
        error: error,
        stackTrace: stackTrace,
      );

      setState(() {
        isAuthenticated = false;
      });

      FlutterNativeSplash.remove();
    }
  }

  @override
  Widget build(BuildContext context) {
    return isAuthenticated ? const MainScreen() : const LoginScreen();
  }
}
