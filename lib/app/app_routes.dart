import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/app.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/navigation_animations_helper.dart';
import 'package:rasiin_tasks_app/core/params/comment_screen_argument_params.dart';
import 'package:rasiin_tasks_app/core/params/full_screen_image_arguments.dart';
import 'package:rasiin_tasks_app/features/display%20task/screen/assign_tasks_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/apply_expense_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/apply_leave_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/approval_request_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/assign_issue_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/attendence_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_issue_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_notification_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_order_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_payment_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_visit_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/expense_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/issue_detail_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/issue_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/leave_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/notification_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/order_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/payment_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/payroll_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/visit_screen.dart';
import 'package:rasiin_tasks_app/features/main/screens/main_screen.dart';
import 'package:rasiin_tasks_app/features/posts/screen/comments_screen.dart';
import 'package:rasiin_tasks_app/features/posts/screen/create_poll_screen.dart';
import 'package:rasiin_tasks_app/features/posts/screen/full_post_image_screen.dart';
import 'package:rasiin_tasks_app/features/profile/screen/change_password_screen.dart';

import '../core/models/appointment_model.dart';
import '../features/appointments/screen/appointments_screen.dart';
import '../features/common/screens/login_screen.dart';
import '../features/common/screens/register_screen.dart';
import '../features/add task/screen/create_task_screen.dart';
import '../features/display task/screen/change_task_status_screen.dart';
import '../features/display task/screen/display_task_screen.dart';
import '../features/encounters/screen/open_encounter_screen.dart';
import '../features/home/<USER>/home_screen.dart';
import '../features/posts/screen/create_post_screen.dart';
import '../features/profile/screen/profile_screen.dart';

class AppRoutes {
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case ScreenConstants.main:
        return slideTransition(page: const MainScreen());
      case ScreenConstants.auth:
        return slideTransition(page: const AuthScreen());
      case ScreenConstants.home:
        return slideTransition(page: const HomeScreen());
      case ScreenConstants.profile:
        return slideTransition(page: const ProfileScreen());
      case ScreenConstants.login:
        return slideTransition(page: const LoginScreen());
      case ScreenConstants.register:
        return slideTransition(page: const RegisterScreen());
      case ScreenConstants.createTask:
        return slideTransition(page: const CreateTaskScreen());
      case ScreenConstants.displayTask:
        final bool? isLeadingNeeded = settings.arguments as bool?;
        return slideTransition(
          page: DisplayTaskScreen(
            isFloatingNeeded: isLeadingNeeded ?? false,
          ),
        );
      case ScreenConstants.assignTask:
        final TaskModel? task = settings.arguments as TaskModel?;
        return slideTransition(
          page: AssignTasksScreen(
            task: task,
          ),
        );
      case ScreenConstants.createPost:
        return slideTransition(page: const CreatePostScreen());
      case ScreenConstants.createPoll:
        return slideTransition(page: const CreatePollScreen());
      case ScreenConstants.postFullImage:
        final FullScreenImageArguments args =
            settings.arguments as FullScreenImageArguments;
        return slideTransition(
          page: FullScreenImage(
            fullScreenImageArguments: args,
          ),
        );
      case ScreenConstants.comments:
        final CommentScreenArgumentParams? commentScreenArgumentParams =
            settings.arguments as CommentScreenArgumentParams?;
        return slideTransition(
          page: CommentsScreen(
            commentScreenArgumentParams: commentScreenArgumentParams,
          ),
        );
      case ScreenConstants.approval:
        return slideTransition(page: const ApprovalRequestScreen());
      case ScreenConstants.notifcation:
        return slideTransition(page: const NotificationScreen());
      case ScreenConstants.payroll:
        return slideTransition(page: const PayrollScreen());
      case ScreenConstants.createNotification:
        return slideTransition(page: const CreateNotificationScreen());
      case ScreenConstants.issue:
        return slideTransition(page: const IssueScreen());
      case ScreenConstants.issueDetails:
        final IssueModel? issueModel = settings.arguments as IssueModel?;
        return slideTransition(
          page: IssueDetailScreen(
            issue: issueModel,
          ),
        );
      case ScreenConstants.createIssue:
        return slideTransition(page: const CreateIssueScreen());
      case ScreenConstants.changePassword:
        return slideTransition(page: const ChangePasswordScreen());
      case ScreenConstants.changeTaskStatus:
        final AssignedTasksModel assignedTask =
            settings.arguments as AssignedTasksModel;
        return slideTransition(
          page: ChangeTaskStatusScreen(
            assignedModel: assignedTask,
          ),
        );
      case ScreenConstants.assignIssue:
        final IssueModel? issueModel = settings.arguments as IssueModel?;
        return slideTransition(
          page: AssignIssueScreen(
            issue: issueModel,
          ),
        );

      case ScreenConstants.attendence:
        return slideTransition(page: const AttendenceScreen());
      case ScreenConstants.visit:
        return slideTransition(page: VisitScreen());
      case ScreenConstants.createVisit:
        return slideTransition(page: const CreateVisitScreen());
      case ScreenConstants.payment:
        return slideTransition(page: const PaymentScreen());
      case ScreenConstants.createPayment:
        return slideTransition(page: const CreatePaymentScreen());
      case ScreenConstants.expense:
        return slideTransition(page: const ExpenseScreen());
      case ScreenConstants.createExpense:
        return slideTransition(page: ApplyExpenseScreen());
      case ScreenConstants.order:
        return slideTransition(page: const OrderScreen());
      case ScreenConstants.createOrder:
        return slideTransition(page: CreateOrderScreen());
      case ScreenConstants.leave:
        return slideTransition(page: const LeaveScreen());
      case ScreenConstants.applyLeave:
        return slideTransition(page: ApplyLeaveScreen());

      case ScreenConstants.appointments:
        return slideTransition(
          page: const AppointmentsScreen(),
        );

      case ScreenConstants.openEncounter:
        final AppointmentModel? appointment =
            settings.arguments as AppointmentModel?;
        return slideTransition(
          page: OpenEncounterScreen(
            appointment: appointment,
          ),
        );
      default:
        return slideTransition(page: const LoginScreen());
    }
  }
}
