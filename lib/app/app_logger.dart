import 'dart:async';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

class AppLogger {
  static final AppLogger _singleton = AppLogger._internal();

  factory AppLogger() => _singleton;

  static final isProduction = const bool.fromEnvironment('dart.vm.product');

  Logger? _logger;

  AppLogger._internal();

  Future<void> setupLogging() async {
    final level = isProduction ? Level.error : Level.all;
    _logger = Logger(
      printer: CustomerPrinter('AppLogger'),
      level: level,
    );

    //  if (!isProduction) {
    //   _logger?.addOutput(ConsoleOutput());
    // }
    // else{
    //   _logger?.addOutput(FileOutput('app_logs.txt'));
    // }

    _logger?.i('Logging setup complete...');
  }

  // Updated log methods with null checks
  void info(String message) => _logger?.i(message);
  void warning(String message) => _logger?.w(message);
  void error(String message, {Object? error, StackTrace? stackTrace}) {
    if (_logger != null) {
      _logger!.e(message, error: error, stackTrace: stackTrace);
    } else {
      debugPrint('ERROR (Logger not initialized): $message');
      if (error != null) debugPrint('Error: $error');
      if (stackTrace != null) debugPrint('Stack: $stackTrace');
    }
  }
}

class CustomerPrinter extends LogPrinter {
  final String className;

  CustomerPrinter(this.className);

  // Define custom emojis for each log level
  static final Map<Level, String> levelEmojis = {
    Level.trace: '🔍', // Magnifying glass
    Level.debug: '🐛', // Bug
    Level.info: '💡', // Light bulb
    Level.warning: '⚠️', // Warning sign
    Level.error: '❌', // Cross mark
    Level.fatal: '💀', // Skull
    Level.off: '🔇', // Muted speaker
  };

  @override
  List<String> log(LogEvent event) {
    final prettyPrinter = PrettyPrinter(
      colors: true,
      dateTimeFormat: DateTimeFormat.dateAndTime,
      printEmojis: true,
    );

    final color =
        prettyPrinter.levelColors?[event.level] ?? const AnsiColor.none();
    final emoji =
        prettyPrinter.levelEmojis?[event.level] ?? levelEmojis[event.level]!;
    final buffer = StringBuffer('$emoji $className: ${event.message}');

    // Append error if exists
    if (event.error != null) {
      buffer.writeln('\n🔴 Error: ${event.error}');
    }

    // Append stack trace if exists
    if (event.stackTrace != null) {
      buffer.writeln('\n📍 Stack Trace:\n${event.stackTrace}');
    }

    return [color(buffer.toString())];
  }
}

// // Example of a custom output (e.g., file logging)
// class FileOutput extends LogOutput {
//   final String filePath;

//   FileOutput(this.filePath);

//   @override
//   void output(OutputEvent event) {
//     // Write logs to a file (example implementation)
//     // You can use the `dart:io` library to write to a file.
//     // For simplicity, this is just a placeholder.
//     event.lines.forEach((line) {
//       print('Writing to file ($filePath): $line');
//     });
//   }
// }

// // Example of a console output
// class ConsoleOutput extends LogOutput {
//   @override
//   void output(OutputEvent event) {
//     // Print logs to the console
//     event.lines.forEach(print);
//   }
// }
