// import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';

class AppConfig {
  /// Determine the appropriate env file based on the build mode
  static String _filename({bool isTestMode = false}) {
    // if (isTestMode) return '.env.test';
    // return kReleaseMode ? '.env.production' : '.env.development';
    return '.env.production';
  }

  /// Load environment variables from the appropriate .env file
  static Future<void> loadEnv({bool isTestMode = false}) async {
    try {
      final fileName = _filename(isTestMode: isTestMode);
      await dotenv.load(fileName: fileName);
      if (!dotenv.isInitialized) {
        AppLogger().error('Environment variables failed to initialize.');
      }
      AppLogger().info('Environment variables loaded successfully. $fileName');
    } catch (error, stackTrace) {
      AppLogger().error(
        'Failed to load environment variables',
        error: error,
        stackTrace: stackTrace,
      );
    }
  }

  /// Generic method to fetch environment variables with error handling
  static String _getEnvVariable(String key,
      {String defaultValue = '', bool isCritical = false}) {
    try {
      final value = dotenv.env[key];
      if (value != null && value.trim().isNotEmpty) {
        return value;
      }
      AppLogger().error('Environment variable $key is missing.');
      return defaultValue;
    } catch (error, stackTrace) {
      AppLogger().error(
        'Error fetching environment variable $key',
        error: error,
        stackTrace: stackTrace,
      );
      // if (isCritical) {
      //   throw error;
      // }
      return defaultValue;
    }
  }

  /// Get the base URL from env variables
  static String getBaseUrl() {
    return _getEnvVariable('BASE_URL', isCritical: true); // Critical
  }

  /// Get the API Token from env variables
  static String getApiToken() {
    return _getEnvVariable('API_TOKEN', isCritical: true); // Critical
  }

  /// Get the PDF Base URL from env variables
  static String getPdfBaseUrl() {
    return _getEnvVariable('PDF_BASE_URL', isCritical: true); // Critical
  }

  /// Return a header with the API key and secret
  static Map<String, String> getHeader() {
    final apiToken = getApiToken();
    AppLogger().info(apiToken + '##################################');
    return {'Authorization': apiToken};
  }

  /// Return a header with the API key and secret
  // static Map<String, String> getHodanHeader() {
  //   return {'Authorization': 'token e1cfd767093e7f4:c29a8e217f9ebff'};
  // }
}
