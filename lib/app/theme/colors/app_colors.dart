// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/enums/border_state_enum.dart';
import 'package:rasiin_tasks_app/core/enums/expense_status.dart';
import 'package:rasiin_tasks_app/core/enums/task_priority_enums.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';

// class AppColors {
//   // Primary Colors (Light & Dark Theme)
//   static const Color primaryColorLight =
//       Color(0xFF4544AE); // Light theme primary
//   static const Color primaryColorDark = Color(0xFF1C1A4E); // Dark theme primary

//   // Background Colors (Light & Dark Theme)
//   static const Color backgroundColorLight = Color(0xFFF5F5F5);
//   static const Color backgroundColorDark = Color(0xFF121212);

//   // card colors (for containers,cards)
//   static const Color cardColorLight = Color(0xFFFFFFFF);
//   static const Color cardColorDark = Color(0xFF2C2F38);

//   // Surface Colors (for bottom navigation bar,bottomsheet, etc.)
//   static const Color surfaceColorLight = Color(0xFFEBEBEB);
//   static const Color surfaceColorDark = Color(0xFF2C2C2C);

//   // Border Colors (Light & Dark Theme)
//   static const Color borderInactiveLight =
//       Color(0xFFBEBEBE); // Light inactive border
//   static const Color borderInactiveDark =
//       Color(0xFF444444); // Dark inactive border
//   static const Color borderActiveLight =
//       Color(0xFF6200EE); // Light active border (focus color)
//   static const Color borderActiveDark =
//       Color(0xFFBB86FC); // Dark active border (focus color)

//   // Shadow Colors (Light & Dark Theme)
//   static const Color shadowColorLight = Color(0x1F000000); // Light theme shadow
//   static const Color shadowColorDark =
//       Color(0x40000000); // Dark theme shadow (subtle)

//   // Elevation Colors (Light & Dark Theme)
//   static const Color elevationColorLight = Color(0xFFF0F0F0); // Light elevation
//   static const Color elevationColorDark = Color(0xFF2B2B2B); // Dark elevation

//   // Shimmer Effect Colors (Light & Dark Theme)
//   static const Color shimmerBaseColorLight =
//       Color(0xFFE0E0E0); // Light shimmer base
//   static const Color shimmerBaseColorDark =
//       Color(0xFF3A3A3A); // Dark shimmer base
//   static const Color shimmerHighlightColorLight =
//       Color(0xFFF5F5F5); // Light shimmer highlight
//   static const Color shimmerHighlightColorDark =
//       Color(0xFF606060); // Dark shimmer highlight

//   // Button Colors (Light & Dark Theme)
//   static const Color buttonColorLight =
//       Color(0xFF6200EE); // Light button background
//   static const Color buttonColorDark =
//       Color(0xFF3700B3); // Dark button background
//   static const Color buttonTextColorLight =
//       Color(0xFFFFFFFF); // Light button text
//   static const Color buttonTextColorDark =
//       Color(0xFFEEEEEE); // Dark button text

//   // Link Colors (Light & Dark Theme)
//   static const Color linkColorLight = Color(0xFF00B2E7); // Light link text
//   static const Color linkColorDark = Color(0xFF00B2E7); // Dark link text

//   // Warning
//   static const Color warningColorLight = Color(0xFF00A6B3);
//   static const Color warningColorDark = Color(0xFF006F74);

// // Info
//   static const Color infoColorLight = Color(0xFFFFE25D);
//   static const Color infoColorDark = Color(0xFFF5C23A);

// // Loading
//   static const Color loadingColorLight = Color(0xFF4E8BFF);
//   static const Color loadingColorDark = Color(0xFF0077B5);

// // Success
//   static const Color successColorLight = Color(0xFF52D17F);
//   static const Color successColorDark = Color(0xFF087A3C);

// // Error
//   static const Color errorColorLight = Color(0xFFFF4D6E);
//   static const Color errorColorDark = Color(0xFF9C0027);

// // Confirmation
//   static const Color confirmationColorLight = Color(0xFF55D9D0);
//   static const Color confirmationColorDark = Color(0xFF1E8B8A);

//   // Inactive/Disabled Colors (Light & Dark Theme)
//   static const Color inactiveColorLight = Color(0xFFBDBDBD); // Light inactive
//   static const Color inactiveColorDark = Color(0xFF757575); // Dark inactive

//   // Icon Colors (Light & Dark Theme)
//   static const Color iconColorLight = Color(0xFF757575); // Light theme icons
//   static const Color iconColorDark = Color(0xFFB0BEC5); // Dark theme icons

//   // Text Colors (Light & Dark Theme)
//   static const Color textColorLight = Color(0xFF1A1A1A);
//   static const Color textColorDark = Color(0xFFECECEC);
//   static const Color subtextColorLight = Color(0xFF5C5C5C);
//   static const Color subtextColorDark = Color(0xFFB0B3BA);

//   // Divider Colors (Light & Dark Theme)
//   static const Color dividerColorLight = Color(0xFFE0E0E0); // Light divider
//   static const Color dividerColorDark = Color(0xFF303030); // Dark divider

//   // Transparent Colors (useful for overlays, modals, etc.)
//   static const Color transparent = Color(0x00000000); // Transparent color

//   // ** Methods to return color based on status/priority/expense **

//   static Color getStatusColor(String? status) {
//     switch (status) {
//       case 'Working':
//         return AppColors
//             .primaryColorLight; // Use primary color for working status
//       case 'Open':
//         return AppColors.errorColorLight; // Use error color for open status
//       case 'Pending Review':
//         return AppColors
//             .warningColorLight; // Use warning color for pending review
//       case 'Overdue':
//         return AppColors.errorColorDark; // Dark error for overdue
//       case 'Template':
//         return AppColors.inactiveColorLight; // Grey for templates
//       case 'Completed':
//         return AppColors.successColorLight; // Green for completed tasks
//       case 'Closed':
//         return AppColors.successColorDark; // Dark green for closed tasks
//       case 'Cancelled':
//         return AppColors.inactiveColorDark; // Grey for cancelled tasks
//       default:
//         return AppColors.primaryColorDark; // Default to dark primary
//     }
//   }

//   static Color getPriorityColor(String? priority) {
//     switch (priority) {
//       case 'Low':
//         return AppColors.successColorLight; // Green for low priority
//       case 'Medium':
//         return AppColors.warningColorLight; // Yellow for medium priority
//       case 'High':
//         return AppColors.errorColorLight; // Red for high priority
//       case 'Urgent':
//         return AppColors.primaryColorDark; // Dark primary for urgent
//       default:
//         return AppColors.subtextColorLight; // Default grey for unknown priority
//     }
//   }

//   static Color getExpenseColor(String? expenseStatus) {
//     switch (expenseStatus?.toLowerCase()) {
//       case 'approved':
//         return AppColors.successColorLight; // Green for approved
//       case 'cancelled':
//         return AppColors.errorColorLight; // Red for cancelled
//       case 'draft':
//         return AppColors.inactiveColorLight; // Grey for draft
//       default:
//         return AppColors.primaryColorLight; // Default to primary color
//     }
//   }
// }

class CustomColors extends ThemeExtension<CustomColors> {
  final Color primaryColor;
  final Color backgroundColor;
  final Color cardColor;
  final Color surfaceColor;
  final Color borderInactiveColor;
  final Color borderActiveColor;
  final Color shadowColor;
  final Color elevationColor;
  final Color shimmerBaseColor;
  final Color shimmerHighlightColor;
  final Color buttonColor;
  final Color buttonTextColor;
  final Color linkColor;
  final Color warningColor;
  final Color infoColor;
  final Color loadingColor;
  final Color successColor;
  final Color errorColor;
  final Color confirmationColor;
  final Color inactiveColor;
  final Color iconColor;
  final Color textColor;
  final Color subtextColor;
  final Color dividerColor;
  final Color whiteColor;
  final Color blackColor;
  final Color transparent;

  const CustomColors({
    required this.primaryColor,
    required this.backgroundColor,
    required this.cardColor,
    required this.surfaceColor,
    required this.borderInactiveColor,
    required this.borderActiveColor,
    required this.shadowColor,
    required this.elevationColor,
    required this.shimmerBaseColor,
    required this.shimmerHighlightColor,
    required this.buttonColor,
    required this.buttonTextColor,
    required this.linkColor,
    required this.warningColor,
    required this.infoColor,
    required this.loadingColor,
    required this.successColor,
    required this.errorColor,
    required this.confirmationColor,
    required this.inactiveColor,
    required this.iconColor,
    required this.textColor,
    required this.subtextColor,
    required this.dividerColor,
    required this.transparent,
    required this.whiteColor,
    required this.blackColor,
  });

  @override
  CustomColors copyWith({
    Color? primaryColor,
    Color? backgroundColor,
    Color? cardColor,
    Color? surfaceColor,
    Color? borderInactiveColor,
    Color? borderActiveColor,
    Color? shadowColor,
    Color? elevationColor,
    Color? shimmerBaseColor,
    Color? shimmerHighlightColor,
    Color? buttonColor,
    Color? buttonTextColor,
    Color? linkColor,
    Color? warningColor,
    Color? infoColor,
    Color? loadingColor,
    Color? successColor,
    Color? errorColor,
    Color? confirmationColor,
    Color? inactiveColor,
    Color? iconColor,
    Color? textColor,
    Color? subtextColor,
    Color? dividerColor,
    Color? transparent,
    Color? whiteColor,
    Color? blackColor,
  }) {
    return CustomColors(
      primaryColor: primaryColor ?? this.primaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      cardColor: cardColor ?? this.cardColor,
      surfaceColor: surfaceColor ?? this.surfaceColor,
      borderInactiveColor: borderInactiveColor ?? this.borderInactiveColor,
      borderActiveColor: borderActiveColor ?? this.borderActiveColor,
      shadowColor: shadowColor ?? this.shadowColor,
      elevationColor: elevationColor ?? this.elevationColor,
      shimmerBaseColor: shimmerBaseColor ?? this.shimmerBaseColor,
      shimmerHighlightColor:
          shimmerHighlightColor ?? this.shimmerHighlightColor,
      buttonColor: buttonColor ?? this.buttonColor,
      buttonTextColor: buttonTextColor ?? this.buttonTextColor,
      linkColor: linkColor ?? this.linkColor,
      warningColor: warningColor ?? this.warningColor,
      infoColor: infoColor ?? this.infoColor,
      loadingColor: loadingColor ?? this.loadingColor,
      successColor: successColor ?? this.successColor,
      errorColor: errorColor ?? this.errorColor,
      confirmationColor: confirmationColor ?? this.confirmationColor,
      inactiveColor: inactiveColor ?? this.inactiveColor,
      iconColor: iconColor ?? this.iconColor,
      textColor: textColor ?? this.textColor,
      subtextColor: subtextColor ?? this.subtextColor,
      dividerColor: dividerColor ?? this.dividerColor,
      transparent: transparent ?? this.transparent,
      whiteColor: whiteColor ?? this.whiteColor,
      blackColor: blackColor ?? this.blackColor,
    );
  }

  @override
  CustomColors lerp(ThemeExtension<CustomColors>? other, double t) {
    if (other is! CustomColors) {
      return this;
    }
    return CustomColors(
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t)!,
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t)!,
      cardColor: Color.lerp(cardColor, other.cardColor, t)!,
      surfaceColor: Color.lerp(surfaceColor, other.surfaceColor, t)!,
      borderInactiveColor:
          Color.lerp(borderInactiveColor, other.borderInactiveColor, t)!,
      borderActiveColor:
          Color.lerp(borderActiveColor, other.borderActiveColor, t)!,
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t)!,
      elevationColor: Color.lerp(elevationColor, other.elevationColor, t)!,
      shimmerBaseColor:
          Color.lerp(shimmerBaseColor, other.shimmerBaseColor, t)!,
      shimmerHighlightColor:
          Color.lerp(shimmerHighlightColor, other.shimmerHighlightColor, t)!,
      buttonColor: Color.lerp(buttonColor, other.buttonColor, t)!,
      buttonTextColor: Color.lerp(buttonTextColor, other.buttonTextColor, t)!,
      linkColor: Color.lerp(linkColor, other.linkColor, t)!,
      warningColor: Color.lerp(warningColor, other.warningColor, t)!,
      infoColor: Color.lerp(infoColor, other.infoColor, t)!,
      loadingColor: Color.lerp(loadingColor, other.loadingColor, t)!,
      successColor: Color.lerp(successColor, other.successColor, t)!,
      errorColor: Color.lerp(errorColor, other.errorColor, t)!,
      confirmationColor:
          Color.lerp(confirmationColor, other.confirmationColor, t)!,
      inactiveColor: Color.lerp(inactiveColor, other.inactiveColor, t)!,
      iconColor: Color.lerp(iconColor, other.iconColor, t)!,
      textColor: Color.lerp(textColor, other.textColor, t)!,
      subtextColor: Color.lerp(subtextColor, other.subtextColor, t)!,
      dividerColor: Color.lerp(dividerColor, other.dividerColor, t)!,
      transparent: Color.lerp(transparent, other.transparent, t)!,
      whiteColor: Color.lerp(whiteColor, other.whiteColor, t)!,
      blackColor: Color.lerp(blackColor, other.blackColor, t)!,
    );
  }

  static CustomColors light = CustomColors(
    primaryColor: Color(0xFF4544AE),
    backgroundColor: Color(0xFFF5F5F5),
    cardColor: Color(0xFFFFFFFF),
    surfaceColor: Color(0xFFEBEBEB),
    borderInactiveColor: Color(0xFFBEBEBE),
    borderActiveColor: Color(0xFF6200EE),
    shadowColor: Color(0x1F000000),
    elevationColor: Color(0xFFF0F0F0),
    shimmerBaseColor: Color(0xFFE0E0E0),
    shimmerHighlightColor: Color(0xFFF5F5F5),
    buttonColor: Color(0xFF6200EE),
    buttonTextColor: Color(0xFFFFFFFF),
    linkColor: Color(0xFF0000EE),
    errorColor: const Color(0xFFFF4D6E),
    successColor: const Color(0xFF52D17F),
    warningColor: const Color(0xFF00A6B3),
    infoColor: const Color(0xFFFFE25D),
    loadingColor: const Color(0xFF4E8BFF),
    confirmationColor: const Color(0xFF55D9D0),
    inactiveColor: Color(0xFF9E9E9E),
    iconColor: Color(0xFF212121),
    textColor: Color(0xFF212121),
    subtextColor: Color(0xFF757575),
    dividerColor: Color(0xFFBDBDBD),
    transparent: Colors.transparent,
    whiteColor: Colors.white,
    blackColor: Colors.black,
  );

  static CustomColors dark = CustomColors(
    primaryColor: Color(0xFF1C1A4E),
    backgroundColor: Color(0xFF121212),
    cardColor: Color(0xFF2C2F38),
    surfaceColor: Color(0xFF2C2C2C),
    borderInactiveColor: Color(0xFF444444),
    borderActiveColor: Color(0xFFBB86FC),
    shadowColor: Color(0x40000000),
    elevationColor: Color(0xFF2B2B2B),
    shimmerBaseColor: Color(0xFF3A3A3A),
    shimmerHighlightColor: Color(0xFF606060),
    buttonColor: Color(0xFF3700B3),
    buttonTextColor: Color(0xFFEEEEEE),
    linkColor: Color(0xFFBB86FC),
    errorColor: const Color(0xFF9C0027),
    successColor: const Color(0xFF087A3C),
    warningColor: const Color(0xFF006F74),
    infoColor: const Color(0xFFF5C23A),
    loadingColor: const Color(0xFF0077B5),
    confirmationColor: const Color(0xFF1E8B8A),
    inactiveColor: Color(0xFF757575),
    iconColor: Color(0xFFEEEEEE),
    textColor: Color(0xFFEEEEEE),
    subtextColor: Color(0xFFBDBDBD),
    dividerColor: Color(0xFF424242),
    transparent: Colors.transparent,
    whiteColor: Colors.white,
    blackColor: Colors.black,
  );

  Color getTaskStatusColor({
    required TaskStatus taskStatus,
    required bool isLightTheme,
  }) {
    switch (taskStatus) {
      case TaskStatus.working:
        return isLightTheme
            ? light.buttonColor
            : dark.buttonColor; // Use primary color for working status
      case TaskStatus.open:
        return isLightTheme
            ? light.errorColor
            : dark.errorColor; // Use error color for open status
      case TaskStatus.pendingReview:
        return isLightTheme
            ? light.warningColor
            : dark.warningColor; // Use warning color for pending review
      case TaskStatus.overdue:
        return isLightTheme
            ? light.errorColor
            : dark.errorColor; // Dark error for overdue
      case TaskStatus.template:
        return isLightTheme
            ? light.inactiveColor
            : dark.inactiveColor; // Grey for templates
      case TaskStatus.completed:
        return isLightTheme
            ? light.successColor
            : dark.successColor; // Green for completed tasks
      case TaskStatus.cancelled:
        return isLightTheme
            ? light.inactiveColor
            : dark.inactiveColor; // Dark green for closed tasks
      default:
        return isLightTheme
            ? light.buttonColor
            : dark.buttonColor; // Default to dark primary
    }
  }

  Color getPriorityColor({
    required TaskPriority taskPriority,
    required bool isLightTheme,
  }) {
    switch (taskPriority) {
      case TaskPriority.low:
        return isLightTheme
            ? light.successColor
            : dark.successColor; // Green for low priority
      case TaskPriority.medium:
        return isLightTheme
            ? light.warningColor
            : dark.warningColor; // Yellow for medium priority
      case TaskPriority.high:
        return isLightTheme
            ? light.errorColor
            : dark.errorColor; // Red for high priority
      default:
        return isLightTheme
            ? light.subtextColor
            : dark.subtextColor; // Default grey for unknown priority
    }
  }

  Color getExpenseColor({
    required ExpenseStatus expenseStatus,
    required bool isLightTheme,
  }) {
    switch (expenseStatus) {
      case ExpenseStatus.approved:
        return isLightTheme
            ? light.successColor
            : dark.successColor; // Green for approved
      case ExpenseStatus.cancelled:
        return isLightTheme
            ? light.errorColor
            : dark.errorColor; // Red for cancelled
      case ExpenseStatus.draft:
        return isLightTheme
            ? light.inactiveColor
            : dark.inactiveColor; // Grey for draft
      default:
        return isLightTheme
            ? light.buttonColor
            : dark.buttonColor; // Default to primary color
    }
  }

  Color getBorderColor({
    required BorderState borderState,
    required bool isLightTheme,
  }) {
    switch (borderState) {
      case BorderState.active:
        return isLightTheme ? light.borderActiveColor : dark.borderActiveColor;
      case BorderState.inactive:
        return isLightTheme
            ? light.borderInactiveColor
            : dark.borderInactiveColor;
      default:
        return isLightTheme
            ? light.borderInactiveColor
            : dark.borderInactiveColor;
    }
  }

  Color getShimmerColor({
    required bool isBaseColor,
    required bool isLightTheme,
  }) {
    return isBaseColor
        ? isLightTheme
            ? light.shimmerBaseColor
            : dark.shimmerBaseColor
        : isLightTheme
            ? light.shimmerHighlightColor
            : dark.shimmerHighlightColor;
  }
}

class _CustomColorsProxy {
  final BuildContext context;

  _CustomColorsProxy(this.context);

  CustomColors get appColors {
    return Theme.of(context).extension<CustomColors>()!;
  }

  bool get isLightTheme {
    return Theme.of(context).brightness == Brightness.light;
  }

  Color get primaryColor => appColors.primaryColor;
  Color get backgroundColor => appColors.backgroundColor;
  Color get cardColor => appColors.cardColor;
  Color get surfaceColor => appColors.surfaceColor;
  Color get borderInactiveColor => appColors.borderInactiveColor;
  Color get borderActiveColor => appColors.borderActiveColor;
  Color get shadowColor => appColors.shadowColor;
  Color get elevationColor => appColors.elevationColor;
  Color get shimmerBaseColor => appColors.shimmerBaseColor;
  Color get shimmerHighlightColor => appColors.shimmerHighlightColor;
  Color get buttonColor => appColors.buttonColor;
  Color get buttonTextColor => appColors.buttonTextColor;
  Color get linkColor => appColors.linkColor;
  Color get warningColor => appColors.warningColor;
  Color get infoColor => appColors.infoColor;
  Color get loadingColor => appColors.loadingColor;
  Color get successColor => appColors.successColor;
  Color get errorColor => appColors.errorColor;
  Color get confirmationColor => appColors.confirmationColor;
  Color get inactiveColor => appColors.inactiveColor;
  Color get iconColor => appColors.iconColor;
  Color get textColor => appColors.textColor;
  Color get subtextColor => appColors.subtextColor;
  Color get dividerColor => appColors.dividerColor;
  Color get transparent => appColors.transparent;
  Color get whiteColor => appColors.whiteColor;
  Color get blackColor => appColors.blackColor;

  Color getTaskStatusColor({
    required TaskStatus taskStatus,
  }) {
    return appColors.getTaskStatusColor(
      taskStatus: taskStatus,
      isLightTheme: isLightTheme,
    );
  }

  Color getPriorityColor({
    required TaskPriority taskPriority,
  }) {
    return appColors.getPriorityColor(
      taskPriority: taskPriority,
      isLightTheme: isLightTheme,
    );
  }

  Color getExpenseColor({
    required ExpenseStatus expenseStatus,
  }) {
    return appColors.getExpenseColor(
      expenseStatus: expenseStatus,
      isLightTheme: isLightTheme,
    );
  }

  Color getBorderColor({
    required BorderState borderState,
  }) {
    return appColors.getBorderColor(
      borderState: borderState,
      isLightTheme: isLightTheme,
    );
  }

  Color getShimmerColor({
    required bool isBaseColor,
  }) {
    return appColors.getShimmerColor(
      isBaseColor: isBaseColor,
      isLightTheme: isLightTheme,
    );
  }
}

extension CustomColorsX on BuildContext {
  _CustomColorsProxy get appColors => _CustomColorsProxy(this);
}
