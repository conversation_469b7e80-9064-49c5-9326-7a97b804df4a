import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomBottomSheetTheme {
  static BottomSheetThemeData lightTheme = BottomSheetThemeData(
    backgroundColor: Colors.grey.shade200, // Light background color
    modalBackgroundColor: Colors.grey.shade200, // Background color of the modal
    shape: RoundedRectangleBorder(
      borderRadius:
          BorderRadius.vertical(top: Radius.circular(16.r)), // Rounded corners
    ),
    elevation: 8.0, // Shadow effect
  );

  static BottomSheetThemeData darkTheme = BottomSheetThemeData(
    backgroundColor: Colors.grey.shade800, // Dark background color
    modalBackgroundColor: Colors.grey.shade800, // Background color of the modal
    shape: RoundedRectangleBorder(
      borderRadius:
          BorderRadius.vertical(top: Radius.circular(16.r)), // Rounded corners
    ),
    elevation: 8.0, // Shadow effect
  );
}
