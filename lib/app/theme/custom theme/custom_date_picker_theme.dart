import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomDatePickerTheme {
  // Light theme configuration for the date picker
  static DatePickerThemeData lightTheme = DatePickerThemeData(
    backgroundColor: Colors.white,
    elevation: 4,
    shadowColor: Colors.black54,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12), // Rounded corners
    ),
    headerBackgroundColor: Colors.blueAccent,
    headerForegroundColor: Colors.white,
    headerHeadlineStyle: TextStyle(
      color: Colors.white,
      fontSize: 18.sp,
      fontWeight: FontWeight.bold,
    ),
    headerHelpStyle: TextStyle(
      color: Colors.white70,
      fontSize: 14.sp,
    ),
    weekdayStyle: TextStyle(
      color: Colors.black,
      fontSize: 14.sp,
      fontWeight: FontWeight.bold,
    ),
    dayStyle: TextStyle(
      color: Colors.black,
      fontSize: 14.sp,
    ),
    todayForegroundColor: WidgetStateProperty.all(Colors.blueAccent),
    todayBackgroundColor: WidgetStateProperty.all(Colors.blue[100]),
    todayBorder: const BorderSide(color: Colors.blueAccent, width: 2),
    yearStyle: TextStyle(
      color: Colors.black,
      fontSize: 12.sp,
    ),
  );

  // Dark theme configuration for the date picker
  static DatePickerThemeData darkTheme = DatePickerThemeData(
    backgroundColor: Colors.grey.shade900,
    elevation: 4,
    shadowColor: Colors.black,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(12), // Rounded corners
    ),
    headerBackgroundColor: Colors.blueAccent,
    headerForegroundColor: Colors.white,
    headerHeadlineStyle: TextStyle(
      color: Colors.white,
      fontSize: 18.sp,
      fontWeight: FontWeight.bold,
    ),
    headerHelpStyle: TextStyle(
      color: Colors.white70,
      fontSize: 14.sp,
    ),
    weekdayStyle: TextStyle(
      color: Colors.white,
      fontSize: 14.sp,
      fontWeight: FontWeight.bold,
    ),
    dayStyle: TextStyle(
      color: Colors.white,
      fontSize: 14.sp,
    ),
    todayForegroundColor: WidgetStateProperty.all(Colors.yellowAccent),
    todayBackgroundColor: WidgetStateProperty.all(Colors.grey.shade800),
    todayBorder: const BorderSide(color: Colors.yellowAccent, width: 2),
    yearStyle: TextStyle(
      color: Colors.white,
      fontSize: 12.sp,
    ),
  );
}
