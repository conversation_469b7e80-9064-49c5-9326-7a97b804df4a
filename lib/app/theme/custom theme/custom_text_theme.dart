import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomTextTheme {
  CustomTextTheme._(); // to avoid creating instance

// Font size constants
  // static const double _kFontSizeDisplayLarge = 57.0;
  // static const double _kFontSizeDisplayMedium = 45.0;
  // static const double _kFontSizeDisplaySmall = 36.0;

  static const double _kFontSizeHeadlineLarge = 32.0;
  static const double _kFontSizeHeadlineMedium = 28.0;
  static const double _kFontSizeHeadlineSmall = 24.0;

  static const double _kFontSizeTitleLarge = 22.0;
  static const double _kFontSizeTitleMedium = 16.0;
  static const double _kFontSizeTitleSmall = 14.0;

  static const double _kFontSizeBodyLarge = 16.0;
  static const double _kFontSizeBodyMedium = 14.0;
  static const double _kFontSizeBodySmall = 12.0;

  static const double _kFontSizeLabelLarge = 14.0;
  static const double _kFontSizeLabelMedium = 12.0;
  static const double _kFontSizeLabelSmall = 10.0;

  //-- light mode
  static final lightTextTheme = TextTheme(
    //
    headlineLarge: TextStyle(
      fontSize: _kFontSizeHeadlineLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.light.textColor,
    ),
    headlineMedium: TextStyle(
      fontSize: _kFontSizeHeadlineMedium.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.light.textColor,
    ),
    headlineSmall: TextStyle(
      fontSize: _kFontSizeHeadlineSmall.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.light.textColor,
    ),

    //
    titleLarge: TextStyle(
      fontSize: _kFontSizeTitleLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.light.textColor,
    ),
    titleMedium: TextStyle(
      fontSize: _kFontSizeTitleMedium.sp,
      color: CustomColors.light.textColor,
    ),
    titleSmall: TextStyle(
      fontSize: _kFontSizeTitleSmall.sp,
      color: CustomColors.light.textColor,
    ),

    //
    bodyLarge: TextStyle(
      fontSize: _kFontSizeBodyLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.light.textColor,
    ),
    bodyMedium: TextStyle(
      fontSize: _kFontSizeBodyMedium.sp,
      color: CustomColors.light.textColor,
    ),
    bodySmall: TextStyle(
      fontSize: _kFontSizeBodySmall.sp,
      color: CustomColors.light.textColor,
    ),

    //
    labelLarge: TextStyle(
      fontSize: _kFontSizeLabelLarge.sp,
      color: CustomColors.light.subtextColor,
    ),

    labelMedium: TextStyle(
      fontSize: _kFontSizeLabelMedium.sp,
      color: CustomColors.light.subtextColor,
    ),
    labelSmall: TextStyle(
      fontSize: _kFontSizeLabelSmall.sp,
      color: CustomColors.light.subtextColor,
    ),

    //
  );

  //-- dark mode
  static final darkTextTheme = TextTheme(
    //
    headlineLarge: TextStyle(
      fontSize: _kFontSizeHeadlineLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.dark.textColor,
    ),
    headlineMedium: TextStyle(
      fontSize: _kFontSizeHeadlineMedium.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.dark.textColor,
    ),
    headlineSmall: TextStyle(
      fontSize: _kFontSizeHeadlineSmall.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.dark.textColor,
    ),

    //
    titleLarge: TextStyle(
      fontSize: _kFontSizeTitleLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.dark.textColor,
    ),
    titleMedium: TextStyle(
      fontSize: _kFontSizeTitleMedium.sp,
      color: CustomColors.dark.textColor,
    ),
    titleSmall: TextStyle(
      fontSize: _kFontSizeTitleSmall.sp,
      color: CustomColors.dark.textColor,
    ),

    //
    bodyLarge: TextStyle(
      fontSize: _kFontSizeBodyLarge.sp,
      fontWeight: FontWeight.bold,
      color: CustomColors.dark.textColor,
    ),
    bodyMedium: TextStyle(
      fontSize: _kFontSizeBodyMedium.sp,
      color: CustomColors.dark.textColor,
    ),
    bodySmall: TextStyle(
      fontSize: _kFontSizeBodySmall.sp,
      color: CustomColors.dark.textColor,
    ),

    //
    labelLarge: TextStyle(
      fontSize: _kFontSizeLabelLarge.sp,
      color: CustomColors.dark.subtextColor,
    ),

    labelMedium: TextStyle(
      fontSize: _kFontSizeLabelMedium.sp,
      color: CustomColors.dark.subtextColor,
    ),
    labelSmall: TextStyle(
      fontSize: _kFontSizeLabelSmall.sp,
      color: CustomColors.dark.subtextColor,
    ),
  );

  static TextStyle getErrorStyle({
    required BuildContext context,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.bold,
    TextDecoration textDecorateion = TextDecoration.none,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return (isDarkMode
        ? darkTextTheme.bodyMedium!.copyWith(
            color: CustomColors.dark.errorColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          )
        : lightTextTheme.bodyMedium!.copyWith(
            color: CustomColors.light.errorColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          ));
  }

  static TextStyle getLinkStyle({
    required BuildContext context,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.bold,
    TextDecoration textDecorateion = TextDecoration.underline,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return (isDarkMode
        ? darkTextTheme.bodyMedium!.copyWith(
            color: CustomColors.dark.linkColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          )
        : lightTextTheme.bodyMedium!.copyWith(
            color: CustomColors.light.linkColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          ));
  }

  static TextStyle getLightStyle({
    required BuildContext context,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.bold,
    TextDecoration textDecorateion = TextDecoration.none,
  }) {
    return (darkTextTheme.bodyMedium!.copyWith(
      color: CustomColors.dark.textColor,
      fontWeight: fontWeight,
      fontSize: fontSize.sp,
      decoration: textDecorateion,
    ));
  }

  static TextStyle getDarkStyle({
    required BuildContext context,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.bold,
    TextDecoration textDecorateion = TextDecoration.none,
  }) {
    return (lightTextTheme.bodyMedium!.copyWith(
      color: CustomColors.light.textColor,
      fontWeight: fontWeight,
      fontSize: fontSize.sp,
      decoration: textDecorateion,
    ));
  }

  static TextStyle getGreyStyle({
    required BuildContext context,
    double fontSize = 14,
    FontWeight fontWeight = FontWeight.bold,
    TextDecoration textDecorateion = TextDecoration.none,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    return (isDarkMode
        ? darkTextTheme.labelMedium!.copyWith(
            color: CustomColors.dark.subtextColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          )
        : lightTextTheme.labelMedium!.copyWith(
            color: CustomColors.light.subtextColor,
            fontWeight: fontWeight,
            fontSize: fontSize.sp,
            decoration: textDecorateion,
          ));
  }
}
