import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomBottomNavigationBarTheme {
  CustomBottomNavigationBarTheme._(); // to avoid making insatnce

  //-- light theme
  static final lightTheme = BottomNavigationBarThemeData(
    backgroundColor: CustomColors.light.surfaceColor,
    type: BottomNavigationBarType.fixed,
    elevation: 8,
    selectedItemColor: Colors.blue,
    showUnselectedLabels: false,
    showSelectedLabels: false,
  );

  //- dark theme
  static final darkTheme = BottomNavigationBarThemeData(
    backgroundColor: CustomColors.dark.surfaceColor,
    type: BottomNavigationBarType.fixed,
    elevation: 8,
    selectedItemColor: Colors.blue,
    showUnselectedLabels: false,
    showSelectedLabels: false,
  );
}
