import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomElevatedButtonTheme {
  CustomElevatedButtonTheme._(); // to avoid creating instance

  //--- light theme
  static final ElevatedButtonThemeData lightStyle = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 0,
      backgroundColor: CustomColors.light.buttonColor,
      foregroundColor: CustomColors.light.buttonTextColor,
      disabledBackgroundColor: CustomColors.light.inactiveColor,
      disabledForegroundColor: CustomColors.light.buttonTextColor,
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      textStyle: TextStyle(
        fontSize: 16.sp,
        color: CustomColors.light.buttonTextColor,
      ),
    ),
  );

  //--- dark theme
  static final ElevatedButtonThemeData darkStyle = ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      elevation: 0,
      backgroundColor: CustomColors.dark.buttonColor,
      foregroundColor: CustomColors.dark.buttonTextColor,
      disabledBackgroundColor: CustomColors.dark.inactiveColor,
      disabledForegroundColor: CustomColors.dark.buttonTextColor,
      padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 10.w),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      textStyle: TextStyle(
        fontSize: 16.sp,
        color: CustomColors.dark.buttonTextColor,
      ),
    ),
  );
}
