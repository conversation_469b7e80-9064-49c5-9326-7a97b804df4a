import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomAppBarTheme {
  CustomAppBarTheme._(); // to avoid creating instance

  // light mode app bar theme
  static final lightAppBarTheme = AppBarTheme(
    backgroundColor: CustomColors.light.surfaceColor,
    foregroundColor: CustomColors.light.backgroundColor,
    // systemOverlayStyle: SystemUiOverlayStyle.light,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor:
          CustomColors.light.transparent, // Make status bar transparent
      statusBarIconBrightness: Brightness.dark, // Dark icons for light mode
      statusBarBrightness: Brightness.light, // Light theme status bar
    ),
    elevation: 0,
    scrolledUnderElevation: 0,
    surfaceTintColor: CustomColors.light.transparent,
    shadowColor: CustomColors.light.transparent,
    iconTheme: IconThemeData(
      color: CustomColors.light.iconColor,
    ),
    titleTextStyle: TextStyle(
      color: CustomColors.light.textColor,
    ),
  );

  // dark mode app bar theme
  static final darkAppBarTheme = AppBarTheme(
    backgroundColor: CustomColors.dark.surfaceColor,
    foregroundColor: CustomColors.dark.backgroundColor,
    elevation: 0,
    scrolledUnderElevation: 0,
    surfaceTintColor: CustomColors.dark.transparent,
    shadowColor: CustomColors.dark.transparent,
    systemOverlayStyle: SystemUiOverlayStyle(
      statusBarColor:
          CustomColors.dark.transparent, // Make status bar transparent
      statusBarIconBrightness: Brightness.light, // Light icons for dark mode
      statusBarBrightness: Brightness.dark, // Dark theme status bar
      // systemStatusBarContrastEnforced: false,
    ),
    iconTheme: IconThemeData(
      color: CustomColors.dark.iconColor,
    ),
    titleTextStyle: TextStyle(
      color: CustomColors.dark.textColor,
      fontWeight: FontWeight.bold,
    ),
  );
}
