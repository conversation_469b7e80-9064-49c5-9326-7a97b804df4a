import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/custom_text_theme.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomInputDecorationTheme {
  CustomInputDecorationTheme._(); // to avoid creating instance

  static const int _borderRadius = 16;

  //-- light theme
  static InputDecorationTheme lightTheme = InputDecorationTheme(
    activeIndicatorBorder: BorderSide(
      color: CustomColors.light.borderActiveColor,
    ),
    fillColor: CustomColors.light.transparent,
    filled: true,
    hintStyle: CustomTextTheme.lightTextTheme.bodyMedium,
    labelStyle: CustomTextTheme.lightTextTheme.labelMedium,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.borderInactiveColor,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.borderInactiveColor,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.borderActiveColor,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.errorColor,
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.light.errorColor,
      ),
    ),
  );

  //-- dark theme
  static InputDecorationTheme darkTheme = InputDecorationTheme(
    activeIndicatorBorder: BorderSide(
      color: CustomColors.dark.borderActiveColor,
    ),
    hintStyle: CustomTextTheme.darkTextTheme.bodyMedium,
    labelStyle: CustomTextTheme.darkTextTheme.labelMedium,
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.borderInactiveColor,
      ),
    ),
    fillColor: CustomColors.dark.transparent,
    filled: true,
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.borderInactiveColor,
      ),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.borderActiveColor,
      ),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.errorColor,
      ),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(_borderRadius.r),
      borderSide: BorderSide(
        color: CustomColors.dark.errorColor,
      ),
    ),
  );
}
