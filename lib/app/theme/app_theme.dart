import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/theme/custom theme/custom_text_theme.dart';
import 'package:rasiin_tasks_app/app/theme/custom theme/custome_app_bar_theme.dart';
import 'package:rasiin_tasks_app/app/theme/custom theme/custome_elevated_button_theme.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/custom_bottom_sheet_theme.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/custom_date_picker_theme.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/input_decoration_theme.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'custom theme/custom_bottom_navigation_bar_theme.dart';

class AppTheme {
  // -- light mode
  static final ThemeData lightMode = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: CustomColors.light.primaryColor,
    scaffoldBackgroundColor: CustomColors.light.backgroundColor,
    dividerColor: CustomColors.light.dividerColor,
    cardColor: CustomColors.light.cardColor,
    appBarTheme: CustomAppBarTheme.lightAppBarTheme,
    elevatedButtonTheme: CustomElevatedButtonTheme.lightStyle,
    textTheme: CustomTextTheme.lightTextTheme,
    bottomNavigationBarTheme: CustomBottomNavigationBarTheme.lightTheme,
    bottomSheetTheme: CustomBottomSheetTheme.lightTheme,
    datePickerTheme: CustomDatePickerTheme.lightTheme,
    inputDecorationTheme: CustomInputDecorationTheme.lightTheme,
    // dialogTheme: DialogTheme(
    //   backgroundColor: CustomColors.light.surfaceColor,
    // ),
    dialogTheme: DialogThemeData(
      backgroundColor: CustomColors.light.surfaceColor,
    ),
    extensions: <ThemeExtension<CustomColors>>[
      CustomColors.light,
    ],
  );

  //-- dark mode
  static final ThemeData darkMode = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,
    scaffoldBackgroundColor: CustomColors.dark.backgroundColor,
    primaryColor: CustomColors.dark.primaryColor,
    dividerColor: CustomColors.dark.dividerColor,
    cardColor: CustomColors.dark.cardColor,
    appBarTheme: CustomAppBarTheme.darkAppBarTheme,
    elevatedButtonTheme: CustomElevatedButtonTheme.darkStyle,
    textTheme: CustomTextTheme.darkTextTheme,
    bottomNavigationBarTheme: CustomBottomNavigationBarTheme.darkTheme,
    bottomSheetTheme: CustomBottomSheetTheme.darkTheme,
    datePickerTheme: CustomDatePickerTheme.darkTheme,
    inputDecorationTheme: CustomInputDecorationTheme.darkTheme,
    // dialogTheme: DialogTheme(
    //   backgroundColor: CustomColors.dark.surfaceColor,
    // ),
    dialogTheme: DialogThemeData(
      backgroundColor: CustomColors.dark.surfaceColor,
    ),
    // pageTransitionsTheme: const PageTransitionsTheme(
    //   builders: <TargetPlatform, PageTransitionsBuilder>{
    //     TargetPlatform.android: OpenUpwardsPageTransitionsBuilder(),
    //     TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
    //     TargetPlatform.linux: OpenUpwardsPageTransitionsBuilder(),
    //   },
    // ),
    extensions: <ThemeExtension<CustomColors>>[
      CustomColors.dark,
    ],
  );
}
