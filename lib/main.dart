// import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:rasiin_tasks_app/app/app.dart';
import 'package:rasiin_tasks_app/app/app_config.dart';
import 'package:rasiin_tasks_app/app/app_dependencies.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
// import 'package:rasiin_tasks_app/core/network/ssl%20validation/app_http_overrides.dart';
import 'firebase_options.dart';

import 'core/services/notification_services.dart';

/// open ios/Runner.xcworkspace 
/*
 rm -rf Pods Podfile.lock Runner.xcworkspace
 pod deintegrate
 pod install --repo-update
 flutter clean
 flutter pub get

*/
// Future<void> main() async {
//   WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();

//   final logger = AppLogger();

//   // Preserve the splash screen until initialization is complete
//   FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

//   try {
//     /// Setup custom SSL validation
//     // HttpOverrides.global = AppHttpOverrides();

//     // Initialize firebase
//     await Firebase.initializeApp(
//       options: DefaultFirebaseOptions.currentPlatform,
//     );

//     await Future.wait(
//       [
//         // Set up logging
//         logger.setupLogging(),
//         // load enviroment variables
//         AppConfig.loadEnv(),
//       ],
//     );

//     // Register dependencies
//     await registerDependencies();

//     // Initialize the NotificationService
//     final notificationService = getIt<NotificationService>();
//     await notificationService.initialize();
//   } catch (error, stackTrace) {
//     logger.error(
//       'Failed to initialize app',
//       error: error,
//       stackTrace: stackTrace,
//     );
//   }

//   // Run the app
//   runApp(const MyApp());
// }

/*

  // privacy policy
  https://www.freeprivacypolicy.com/live/************************************

*/

//? ---------------- Testing --------------

Future<void> main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  final logger = AppLogger();
  await logger.setupLogging();

  // Set up global error handlers for UTF-16 and other Flutter errors
  FlutterError.onError = (FlutterErrorDetails details) {
    if (details.exception.toString().contains('not well-formed UTF-16')) {
      logger.error('🔤 UTF-16 Error caught globally: ${details.exception}');
      logger.error('📍 Stack trace: ${details.stack}');
    } else {
      logger.error('🚨 Flutter Error: ${details.exception}',
          error: details.exception, stackTrace: details.stack);
    }
  };

  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);
  logger.info('Splash screen preserved, initialization started...');

  try {
    // Step 1: Initialize Firebase
    try {
      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      logger.info('Firebase initialized.');
    } on FirebaseException catch (e) {
      if (e.code == 'duplicate-app') {
        logger.warning('⚠️ Firebase already initialized, skipping.');
      } else {
        logger.error('❌ Unexpected Firebase error : ${e.toString()}', error: e);
        // rethrow;
      }
    }

    // Step 2: Set up logging & environment
    try {
      logger.info('Setting up logging and loading environment...');
      await Future.wait([
        AppConfig.loadEnv(),
      ]);
      logger.info('Logging and environment setup complete.');
    } catch (e, st) {
      logger.error('Failed during logger/env setup.', error: e, stackTrace: st);
      // rethrow;
    }

    // Step 3: Register Dependencies
    try {
      logger.info('Registering dependencies...');
      await registerDependencies();
      logger.info('Dependencies registered successfully.');
    } catch (e, st) {
      logger.error('Dependency registration failed.', error: e, stackTrace: st);
      // rethrow;
    }

    // Step 4: Initialize Notification Service
    try {
      logger.info('Initializing NotificationService...');
      final notificationService = getIt<NotificationService>();
      await notificationService.initialize();
      logger.info('NotificationService initialized.');
    } catch (e, st) {
      logger.error('Failed to initialize NotificationService. ${e.toString()}',
          error: e, stackTrace: st);
      // rethrow;
    }
  } catch (error, stackTrace) {
    logger.error(
      '❌ App initialization failed. : $error',
      error: error,
      stackTrace: stackTrace,
    );
  }

  logger.info('🚀 Running MyApp...');
  runApp(const MyApp());
}
