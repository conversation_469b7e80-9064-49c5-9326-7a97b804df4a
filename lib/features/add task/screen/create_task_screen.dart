import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/models/project_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/date_validation_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/task_priority_enums.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animated_button_row_wideget.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class CreateTaskScreen extends StatefulWidget {
  const CreateTaskScreen({super.key});

  @override
  State<CreateTaskScreen> createState() => _CreateTaskScreenState();
}

class _CreateTaskScreenState extends State<CreateTaskScreen> {
  @override
  void initState() {
    super.initState();
    context.tasksBloc.add(GetAllProjectsEvent());
  }

  final GlobalKey<FormState> _addTaskFormKey = GlobalKey<FormState>();
  final TextEditingController _descriptionController = TextEditingController();
  final TextEditingController _dateController = TextEditingController();
  final TextEditingController _subjController = TextEditingController();

  String? selectedDate;
  TaskStatus selectedStatus = TaskStatus.open;
  TaskPriority? selectedPriority;
  ProjectModel? selectedProject;

  @override
  void dispose() {
    _descriptionController.dispose();
    _dateController.dispose();
    _subjController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Create New Task',
      ),
      body: BlocListener<TasksBloc, TasksState>(
        listener: (context, state) {
          _handleBlocState(context, state);
        },
        child: _buildForm(),
      ),
    );
  }

  // Bloc state handling
  void _handleBlocState(BuildContext context, TasksState state) {
    final dialogCubit = context.dialogCubit;
    if (state is TasksStateError) {
      final errorMessage = state.appFailure.getErrorMessage();

      //
      dialogCubit.showErrorDialog(message: errorMessage);
    } else if (state is TasksStateSuccess) {
      context.tasksBloc.add(GetAllTasksEvent(
        userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      ));
      dialogCubit.showSuccessDialog(
        message: state.message,
        onConfirm: () => Navigator.pop(context),
      );
    } else if (state is TasksStateLoading) {
      dialogCubit.showLoadingDialog();
    }
  }

  // Build the main form
  Widget _buildForm() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: Form(
        key: _addTaskFormKey,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 40.h),
              _buildProjectSelector(),
              SizedBox(height: 20.h),
              _buildSubjectField(),
              SizedBox(height: 20.h),
              _buildDescriptionField(),
              SizedBox(height: 20.h),
              _buildPriorityDropdown(),
              SizedBox(height: 20.h),
              _buildDatePicker(),
              SizedBox(height: 20.h),
              _buildStatusDropdown(),
              SizedBox(height: 40.h),
              _buildSubmitSection(),
            ],
          ),
        ),
      ),
    );
  }

  // Encapsulated widgets
  Widget _buildProjectSelector() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 300),
      child: BlocBuilder<TasksBloc, TasksState>(
        builder: (context, state) {
          return CustomSelectFieldDisplayer<ProjectModel>(
            leadingIcon: Icons.business_center,
            displayItem: (project) => project.projectName,
            displaySubTitle: (project) => project.projectId,
            labelText: "Project",
            selectionType: SelectionType.SingleSelection,
            selectedItems: selectedProject != null ? [selectedProject!] : [],
            onSelectionChanged: (items) {
              setState(() {
                selectedProject = items.isNotEmpty ? items[0] : null;
              });
            },
            options: context.tasksBloc.projects,
            bottomSheetTitle: "Select Project",
            validator: (value) {
              if (selectedProject == null) {
                return 'required!';
              }
              return null;
            },
          );
        },
      ),
    );
  }

  Widget _buildSubjectField() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 400),
      child: CustomTextField(
        controller: _subjController,
        labelText: "Subject",
        isObsecureText: false,
        maxLine: 1,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'required!';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDescriptionField() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 500),
      child: CustomTextField(
        controller: _descriptionController,
        labelText: "Description",
        isObsecureText: false,
        maxLine: 5,
        keyboardType: TextInputType.multiline,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return 'required!';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildPriorityDropdown() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 600),
      child: CustomDropDown<TaskPriority>(
        leadingImgPath: ImageConstants.power_svg,
        items: TaskPriority.values,
        displayItem: (value) => value?.displayName,
        labelText: "Priority*",
        onChanged: (value) {
          setState(() {
            selectedPriority = value;
          });
        },
        validator: (value) {
          if (value == null) {
            return 'required!';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildDatePicker() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 700),
      child: CustomDatePickerField(
        dateValidationType: DateValidationType.after,
        onDateSelected: (date) {
          setState(() {
            selectedDate = date;
          });
        },
        validator: (value) {
          if (selectedDate == null || selectedDate!.isEmpty) {
            return 'required!';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildStatusDropdown() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 800),
      child: CustomDropDown<TaskStatus>(
        value: selectedStatus,
        leadingImgPath: ImageConstants.home_svg,
        items: TaskStatus.values,
        displayItem: (value) => value?.displayName,
        labelText: "Status*",
        onChanged: (value) {
          if (value != null) {
            setState(() {
              selectedStatus = value;
            });
          }
        },
        validator: (value) {
          if (value == null) {
            return 'required!';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildSubmitSection() {
    return BlocBuilder<TasksBloc, TasksState>(
      builder: (context, state) {
        final bool isLoading = state is TasksStateLoading;
        return _buildSubmitButton(isLoading);
      },
    );
  }

  Widget _buildSubmitButton(bool isLoading) {
    return AnimatedButtonRow(
      submitButtonState: isLoading ? ButtonState.loading : ButtonState.normal,
      onCancelTap: () {
        Navigator.pop(context);
      },
      onSubmitTap: () {
        if (_addTaskFormKey.currentState!.validate()) {
          context.tasksBloc.add(CreateTaskEvent(
            selectedProjectId: selectedProject?.projectId ?? '',
            selectedProjectName: selectedProject?.projectName ?? '',
            selectedPriority: selectedPriority?.displayName ?? '',
            selectedStatus: selectedStatus.displayName,
            selectedTime: selectedDate ?? '',
            subject: _subjController.text.trim(),
            description: _descriptionController.text.trim(),
            userRoles: context.usersBloc.currentUser?.roleNames ?? [],
          ));
        }
      },
    );
  }
}
