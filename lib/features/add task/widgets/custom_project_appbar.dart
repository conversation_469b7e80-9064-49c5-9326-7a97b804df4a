import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class ShowProjectAppBar extends StatelessWidget {
  const ShowProjectAppBar({
    super.key,
    required this.title,
    required this.onClear,
    required this.onClose,
    required this.onChanged,
  });

  final Function()? onClear;
  final Function()? onClose;
  final String title;
  final Function(String)? onChanged;

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      margin: EdgeInsets.symmetric(vertical: 10.h),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              TextButton(
                  onPressed: onClose,
                  child: Text(
                    "Clear",
                    style: GoogleFonts.roboto(
                      fontSize: 16.sp,
                      color: Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  )),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              TextButton(
                onPressed: onClear,
                child: const Text("Done"),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: CustomTextField(
              labelText: "search",
              isObsecureText: false,
              fillColor: Theme.of(context).scaffoldBackgroundColor,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}
