import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/posts/screen/diplay_events_screen.dart';
import 'package:rasiin_tasks_app/features/posts/screen/display_posts_screen.dart';

class PostsScreen extends StatelessWidget {
  const PostsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return DefaultTabController(
      length: 2, // Number of tabs
      child: Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          toolbarHeight: 40.h,
          backgroundColor: appColors.backgroundColor,
          elevation: 0,
          title: TabBar(
            indicatorColor: appColors.primaryColor,
            labelColor: appColors.textColor,
            unselectedLabelColor: appColors.subtextColor,
            labelStyle: textTheme.bodyLarge,
            unselectedLabelStyle: textTheme.bodyLarge,
            tabs: const [
              Tab(text: "Posts"),
              Tab(text: "Events"),
            ],
          ),
        ),
        //! TabBarView for displaying tabs' content
        body: TabBarView(
          children: [
            DisplayPostsScreen(),
            DiplayEventsScreen(),
          ],
        ),
      ),
    );
  }
}
