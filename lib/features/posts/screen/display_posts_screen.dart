import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/posts/widgets/post_with_media_widget.dart';
import 'package:rasiin_tasks_app/features/posts/widgets/post_with_poll_widget.dart';

import '../../../app/theme/custom theme/custom_text_theme.dart';

class DisplayPostsScreen extends StatefulWidget {
  const DisplayPostsScreen({super.key});

  @override
  State<DisplayPostsScreen> createState() => _DisplayPostsScreenState();
}

class _DisplayPostsScreenState extends State<DisplayPostsScreen> {
  // final ScrollController _scrollController = ScrollController();
  // bool _isLoadingMore = false;
  // bool _hasMoreData = true;
  // static const int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    // _scrollController.addListener(_onScroll);
    _loadPosts();
  }

  @override
  void dispose() {
    // _scrollController.dispose();
    super.dispose();
  }

  // void _onScroll() {
  //   if (_scrollController.position.pixels >=
  //       _scrollController.position.maxScrollExtent - 200) {
  //     _loadMorePosts();
  //   }
  // }

  Future<void> _loadPosts({
    bool forceRefresh = false,
  }) async {
    final userEmail = context.usersBloc.currentUser?.email ?? "";

    context.postBloc.add(
      PostGetAllEvent(
        forceRefresh: forceRefresh,
        userEmail: userEmail,
        // limit: _pageSize,
        offset: 0,
      ),
    );

    // if (forceRefresh) {
    //   setState(() {
    //     _hasMoreData = true;
    //   });
    // }
  }

  // Future<void> _loadMorePosts() async {
  //   if (_isLoadingMore || !_hasMoreData) return;

  //   setState(() {
  //     _isLoadingMore = true;
  //   });

  //   final userEmail = context.usersBloc.currentUser?.email ?? "";
  //   final currentPosts = context.postBloc.posts;

  //   context.postBloc.add(
  //     PostLoadMoreEvent(
  //       userEmail: userEmail,
  //       limit: _pageSize,
  //       offset: currentPosts.length,
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: RefreshIndicator.adaptive(
        onRefresh: () async {
          await _loadPosts(forceRefresh: true);
        },
        child: BlocConsumer<PostBloc, PostState>(
          buildWhen: (_, currentState) {
            return currentState is PostGetAllEmpty ||
                currentState is PostGetAllFailure ||
                currentState is PostGetAllLoaded ||
                currentState is PostGetAllLoading ||
                currentState is PostLoadMoreLoaded ||
                currentState is PostLoadMoreFailure;
          },
          listener: (context, state) {
            // Handle load more states
            // if (state is PostLoadMoreLoaded) {
            //   setState(() {
            //     _isLoadingMore = false;
            //     _hasMoreData = state.hasMoreData;
            //   });
            // }
            // if (state is PostLoadMoreFailure) {
            //   setState(() {
            //     _isLoadingMore = false;
            //   });
            //   SnackBarHelper.showErrorSnackBar(
            //     context: context,
            //     message: "Failed to load more posts",
            //   );
            // }

            // Existing listeners...
            if (state is PostToggleLikeLoading) {
              SnackBarHelper.showInfoSnackBar(
                context: context,
                message: "Toggling like...",
              );
            }
            // ... rest of existing listeners
          },
          builder: (context, state) {
            final posts = context.postBloc.posts;
            final isInitialLoading =
                state is PostGetAllLoading && posts.isEmpty;

            return CustomListGridView(
              // controller: _scrollController,
              items: posts,
              padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
              isLoading: isInitialLoading,
              isEmpty: state is PostGetAllEmpty,
              layoutType: LayoutType.listView,
              onRefresh: () {
                _loadPosts(forceRefresh: true);
              },
              itemBuilder: (context, post) {
                final isPollPost = post.isPoll;
                final currentUserEmail =
                    context.usersBloc.currentUser?.email ?? "";
                final isLiking = state is PostToggleLikeLoading;

                return isPollPost
                    ? PostWithPollBuilder(
                        post: post,
                        onComment: () {},
                        onLike: isLiking
                            ? null
                            : () {
                                context.postBloc.add(
                                  PostToggleLikeEvent(
                                    postId: post.postId,
                                    userEmail: currentUserEmail,
                                  ),
                                );
                              },
                        onMore: () {},
                        onVote: (optionId) {
                          context.postBloc.add(
                            PostVoteEvent(
                              postId: post.postId,
                              userEmail: currentUserEmail,
                              optionId: optionId,
                            ),
                          );
                        },
                      )
                    : PostWIthMediaBuilder(
                        post: post,
                        onComment: () {},
                        onLike: () {
                          context.postBloc.add(
                            PostToggleLikeEvent(
                              postId: post.postId,
                              userEmail: currentUserEmail,
                            ),
                          );
                        },
                        onMore: () {},
                      );
              },
              // footerBuilder:
              //     _isLoadingMore ? () => _buildLoadingFooter() : null,
            );
          },
        ),
      ),
    );
  }

  // Widget _buildLoadingFooter() {
  //   return Container(
  //     padding: EdgeInsets.all(16.h),
  //     alignment: Alignment.center,
  //     child: CircularProgressIndicator(),
  //   );
  // }

  PreferredSizeWidget _buildAppBar() {
    final appColors = context.appColors;

    return AppBar(
      backgroundColor: appColors.backgroundColor,
      elevation: 0,
      toolbarHeight: 10.h,
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(50),
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, ScreenConstants.createPost);
                  },
                  child: Row(
                    children: [
                      SizedBox(width: 30.w),
                      Icon(Icons.add_box_outlined,
                          color: appColors.subtextColor.withValues(alpha: 0.5),
                          size: 25.r),
                      SizedBox(width: 10.w),
                      Text(
                        "Create post",
                        style: CustomTextTheme.getGreyStyle(
                          context: context,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 40.w),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    Navigator.pushNamed(context, ScreenConstants.createPoll);
                  },
                  child: Row(
                    children: [
                      FaIcon(
                        FontAwesomeIcons.squarePollVertical,
                        color: appColors.subtextColor.withValues(alpha: 0.5),
                        size: 25.r,
                      ),
                      SizedBox(width: 10.w),
                      Text(
                        "Create poll",
                        style: CustomTextTheme.getGreyStyle(
                          context: context,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
