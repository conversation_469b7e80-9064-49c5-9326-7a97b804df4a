import 'dart:io' show File;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animated_button_row_wideget.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';
import 'package:video_player/video_player.dart';

class CreatePostScreen extends StatefulWidget {
  const CreatePostScreen({super.key});

  @override
  State<CreatePostScreen> createState() => _CreatePostScreenState();
}

class _CreatePostScreenState extends State<CreatePostScreen> {
  final _formKey = GlobalKey<FormState>();
  final _postController = TextEditingController();

  @override
  void dispose() {
    _postController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final userBloc = context.usersBloc;
    final userName = userBloc.currentUser?.employeeName ?? 'username';
    final userImage = userBloc.currentUser?.profileImage ?? '';
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        appBar: AppBar(
          // backgroundColor: appColors.backgroundColor,
          // foregroundColor: appColors.primaryColor,
          title: Text(
            'Create Post',
            style: textTheme.titleMedium,
          ),
        ),
        body: BlocConsumer<PostBloc, PostState>(
          listener: (context, state) {
            if (state is PostVideoPickFailure) {
              final errorMessage = state.failure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }
            if (state is PostMultipleImagesPickFailure) {
              final errorMessage = state.failure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }

            // if (state is PostVideoPicked) {
            //   SnackBarHelper.showSuccessSnackBar(
            //     context: context,
            //     message: 'Video picked successfully',
            //   );
            // }

            // if (state is PostMultipleImagesPicked) {
            //   SnackBarHelper.showSuccessSnackBar(
            //     context: context,
            //     message: 'Images picked successfully',
            //   );
            // }

            // post creation
            if (state is PostCreatePostLoading) {
              SnackBarHelper.showInfoSnackBar(
                context: context,
                message: 'Posting in progress uploading in background....',
                duration: const Duration(seconds: 10),
              );
              Navigator.pop(context);
            }
            // if (state is PostCreatePostSuccess) {
            //   SnackBarHelper.showSuccessSnackBar(
            //     context: context,
            //     message: state.message,
            //   );
            //   Navigator.pop(context);
            // }
            // if (state is PostCreatePostFailure) {
            //   final errorMessage = state.failure.getErrorMessage();
            //   SnackBarHelper.showErrorSnackBar(
            //     context: context,
            //     message: errorMessage,
            //   );
            // }
          },
          builder: (context, state) {
            final images = context.postBloc.images;
            final video = context.postBloc.video;
            final isPicking = state is PostVideoPickLoading ||
                state is PostMultipleImagesPickLoading;
            return Form(
              key: _formKey,
              child: Column(
                children: [
                  SizedBox(height: 10.h),
                  //
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          CustomContainer(
                            padding: EdgeInsets.symmetric(
                                horizontal: 20.w, vertical: 10.h),
                            child: Column(
                              children: [
                                //!
                                Row(
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          CustomImagePickerCard(
                                            radius: 25.r,
                                            isProfile: true,
                                            showIcon: false,
                                            imageUrl: userImage,
                                            userName: userName,
                                          ),
                                          SizedBox(width: 10.w),
                                          Text(
                                            userName,
                                            style: textTheme.bodySmall,
                                          ),
                                        ],
                                      ),
                                    ),
                                    if (isPicking)
                                      const CircularProgressIndicator()
                                    else
                                      Row(
                                        children: [
                                          IconButton(
                                            onPressed: () async {
                                              context.postBloc.add(
                                                const PostVideoPickEvent(
                                                  maxDuration:
                                                      Duration(minutes: 1),
                                                ),
                                              );
                                            },
                                            icon: Icon(
                                              // Icons.video_camera_back,
                                              FontAwesomeIcons.video,
                                              color: appColors.subtextColor,
                                            ),
                                          ),
                                          SizedBox(width: 5.w),
                                          IconButton(
                                            onPressed: () async {
                                              //
                                              context.postBloc.add(
                                                  PostMultipleImagesPickEvent());
                                            },
                                            icon: Icon(
                                              FontAwesomeIcons.image,
                                              color: appColors.subtextColor,
                                            ),
                                          ),
                                        ],
                                      ),
                                  ],
                                ),
                                //!!!
                                SizedBox(height: 20.h),
                                CustomTextField(
                                  controller: _postController,
                                  // labelText: "What is on your mind?",
                                  hintText: "What is on your mind?",
                                  isObsecureText: false,
                                  maxLine: 2,
                                  keyboardType: TextInputType.multiline,
                                  textInputAction: TextInputAction.newline,
                                  fillColor: appColors.transparent,
                                  focusedBorderColor: appColors.transparent,
                                  enabledBorderColor: appColors.transparent,
                                  // validator: (value){
                                  //   if (value == null || value.trim().isEmpty) {
                                  //     return 'Post cannot be empty';
                                  //   }
                                  //   return null;
                                  // },
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 30.h),
                          if (images.isNotEmpty)
                            CustomListGridView<XFile>(
                              items: images,
                              isLoading: false,
                              showFooter: false,
                              isEmpty: images.isEmpty,
                              layoutType: LayoutType.gridView,
                              // gridCrossAxisCount: images.length.isEven ? 2 : 3,
                              onRefresh: () {},
                              itemBuilder: (context, xfile) {
                                // return Image.file(File(xfile.path));
                                return BuildMediaGridItem(
                                  key: ValueKey(xfile.path),
                                  file: xfile,
                                  isVideo: false,
                                  onRemove: () {
                                    context.postBloc.add(
                                        PostRemoveImageEvent(image: xfile));
                                  },
                                );
                              },
                            )
                          else if (video != null)
                            BuildMediaGridItem(
                              key: ValueKey(video.path),
                              file: video,
                              isVideo: true,
                              onRemove: () {
                                context.postBloc.add(
                                  PostRemoveVideoEvent(
                                    video: video,
                                  ),
                                );
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                  //!
                  AnimatedButtonRow(
                    submitButtonState: ButtonState.normal,
                    submitText: "Post",
                    onCancelTap: () {
                      //
                      Navigator.pop(context);
                    },
                    onSubmitTap: () {
                      //
                      final postText = _postController.text.trim();
                      // final hasMedia = images.isNotEmpty || video != null;
                      if (postText.isEmpty) {
                        SnackBarHelper.showErrorSnackBar(
                          context: context,
                          message: 'Please add some text to your post',
                        );
                      }

                      ///
                      else {
                        context.postBloc.add(
                          PostCreatePostEvent(
                            userEmail: context.usersBloc.currentUser!.email,
                            content: postText,
                            pickedVideo: video,
                            pickedImages: images,
                          ),
                        );
                      }
                    },
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

class BuildMediaGridItem extends StatefulWidget {
  final XFile file;
  final bool isVideo;
  final VoidCallback onRemove;

  const BuildMediaGridItem({
    super.key,
    required this.file,
    required this.isVideo,
    required this.onRemove,
  });

  @override
  State<BuildMediaGridItem> createState() => _BuildMediaGridItemState();
}

class _BuildMediaGridItemState extends State<BuildMediaGridItem> {
  VideoPlayerController? _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  @override
  void didUpdateWidget(covariant BuildMediaGridItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // If the file path changed, reinitialize
    if (widget.isVideo && widget.file.path != oldWidget.file.path) {
      _disposeController();
      _initializeController();
    }
  }

  void _initializeController() {
    try {
      if (widget.isVideo) {
        final file = File(widget.file.path);
        _controller = VideoPlayerController.file(file)
          ..initialize().then((_) {
            if (!mounted) return;
            _controller!.setLooping(false);
            // _controller!.play();
            setState(() => _isInitialized = true);
          }).catchError((e) {
            debugPrint("Video init error: $e");
          });
      } else {
        _isInitialized = true;
      }
    } catch (e) {
      debugPrint("Controller init error: $e");
    }
  }

  void _disposeController() {
    try {
      _controller?.pause();
      _controller?.dispose();
    } catch (e) {
      debugPrint("Controller dispose error: $e");
    }
    _controller = null;
    _isInitialized = false;
  }

  @override
  void dispose() {
    _disposeController();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;

    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return SizedBox(
      height: widget.isVideo ? 170.h : context.screenHeight * 0.5,
      child: Stack(
        fit: StackFit.expand,
        children: [
          widget.isVideo
              ? GestureDetector(
                  onTap: () {
                    if (_controller == null) return;
                    final isPlaying = _controller!.value.isPlaying;
                    isPlaying ? _controller!.pause() : _controller!.play();
                    setState(() {});
                  },
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16.r),
                      child: AspectRatio(
                        aspectRatio: _controller!.value.aspectRatio,
                        child: VideoPlayer(_controller!),
                      ),
                    ),
                  ),
                )
              : ClipRRect(
                  borderRadius: BorderRadius.circular(16.r),
                  child: AspectRatio(
                    aspectRatio: 1,
                    child:
                        Image.file(File(widget.file.path), fit: BoxFit.cover),
                  ),
                ),

          // ❌ Close icon
          Positioned(
            top: 4,
            right: 15,
            child: GestureDetector(
              onTap: widget.onRemove,
              child: Container(
                padding: EdgeInsets.all(4.r),
                decoration: BoxDecoration(
                  color: appColors.whiteColor.withValues(alpha: 0.6),
                  border: Border.all(color: appColors.errorColor),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.close,
                  size: 16.r,
                  color: appColors.errorColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
