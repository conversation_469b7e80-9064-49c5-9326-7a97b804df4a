import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/enums/image_source_type_enum.dart';
import 'package:rasiin_tasks_app/core/params/full_screen_image_arguments.dart';

class FullScreenImage extends StatelessWidget {
  final FullScreenImageArguments fullScreenImageArguments;
  const FullScreenImage({
    Key? key,
    required this.fullScreenImageArguments,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String imagePath = fullScreenImageArguments.imagePath;
    String heroTag = fullScreenImageArguments.heroTag;
    ImageSourceType imageSourceType = fullScreenImageArguments.imageSourceType;
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Scaffold(
        // backgroundColor: Colors.white,
        body: Center(
          child: InteractiveViewer(
            minScale: 0.5,
            maxScale: 2.0,
            child: Hero(
              tag: heroTag,
              child: _getImageWidget(imageSourceType, imagePath),
            ),
          ),
        ),
      ),
    );
  }

  Widget _getImageWidget(ImageSourceType imageSourceType, String imagePath) {
    switch (imageSourceType) {
      case ImageSourceType.asset:
        return Image.asset(
          imagePath,
          fit: BoxFit.contain,
        );
      case ImageSourceType.network:
        return CachedNetworkImage(
          imageUrl: imagePath,
          fit: BoxFit.contain,
          placeholder: (context, url) =>
              const Center(child: CircularProgressIndicator()),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        );
      case ImageSourceType.file:
        return Image.file(
          File(imagePath), // imagePath should be the file path
          fit: BoxFit.contain,
        );
    }
  }
}
