import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/poll_duration.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animated_button_row_wideget.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

import '../../../app/theme/custom theme/custom_text_theme.dart';

class CreatePollScreen extends StatefulWidget {
  const CreatePollScreen({super.key});

  @override
  State<CreatePollScreen> createState() => _CreatePollScreenState();
}

class _CreatePollScreenState extends State<CreatePollScreen> {
  final GlobalKey<FormState> createPollFormkey = GlobalKey<FormState>();
  final TextEditingController _questionController = TextEditingController();
  final TextEditingController _option1Controller = TextEditingController();
  final TextEditingController _option2Controller = TextEditingController();
  final TextEditingController _option3Controller = TextEditingController();
  final TextEditingController _option4Controller = TextEditingController();

  late PollDuration selectedPollDuration;

  List<PollDuration> pollDuration = PollDuration.values;

  @override
  void initState() {
    super.initState();
    selectedPollDuration = pollDuration.first;
  }

  @override
  void dispose() {
    super.dispose();
    _questionController.dispose();
    _option1Controller.dispose();
    _option2Controller.dispose();
    _option3Controller.dispose();
    _option4Controller.dispose();
  }

  String? _requiredValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'required!';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    return Scaffold(
      appBar: AppBar(
        // backgroundColor: appColors.backgroundColor,
        // foregroundColor: appColors.primaryColor,
        title: Text(
          'Create Poll',
          style: textTheme.titleMedium,
        ),
      ),
      body: BlocConsumer<PostBloc, PostState>(
        listener: (context, state) {
          if (state is PostCreatePollFailure) {
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: state.failure.getErrorMessage(),
            );
          }
          if (state is PostCreatePollSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: state.message,
            );
            context.postBloc.add(
              PostGetAllEvent(
                forceRefresh: true,
                userEmail: context.usersBloc.currentUser?.email ?? "",
              ),
            );
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          final isLoading = state is PostCreatePollLoading;
          return SingleChildScrollView(
            child: Column(
              children: [
                CustomContainer(
                  // margin: EdgeInsets.only(top: 20.h),
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
                  child: Form(
                    key: createPollFormkey,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 5.h),
                          CustomTextField(
                            controller: _questionController,
                            labelText: "Your Question*",
                            isObsecureText: false,
                            maxLine: 4,
                            validator: _requiredValidator,
                          ),
                          SizedBox(height: 20.h),
                          CustomTextField(
                            controller: _option1Controller,
                            labelText: "Option 1*",
                            isObsecureText: false,
                            validator: _requiredValidator,
                          ),
                          SizedBox(height: 15.h),
                          CustomTextField(
                            controller: _option2Controller,
                            labelText: "Option 2*",
                            isObsecureText: false,
                            validator: _requiredValidator,
                          ),
                          SizedBox(height: 15.h),
                          CustomTextField(
                            controller: _option3Controller,
                            labelText: "Option 3",
                            isObsecureText: false,
                          ),
                          SizedBox(height: 5.h),
                          Text(
                            "Optional",
                            style: CustomTextTheme.getLinkStyle(
                              context: context,
                              textDecorateion: TextDecoration.none,
                            ),
                          ),
                          SizedBox(height: 15.h),
                          CustomTextField(
                            controller: _option4Controller,
                            labelText: "Option 4",
                            isObsecureText: false,
                          ),
                          SizedBox(height: 5.h),
                          Text(
                            "Optional",
                            style: CustomTextTheme.getLinkStyle(
                              context: context,
                              textDecorateion: TextDecoration.none,
                            ),
                          ),
                          SizedBox(height: 15.h),
                          CustomDropDown(
                            value: selectedPollDuration,
                            items: pollDuration,
                            displayItem: (value) => value?.name,
                            labelText: "poll Duration*",
                            onChanged: (value) {
                              if (value == null) return;
                              setState(() {
                                selectedPollDuration = value;
                              });
                            },
                            validator: (value) {
                              if (value == null) {
                                return 'required!';
                              }
                              return null;
                            },
                          ),
                          SizedBox(height: 20.h),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 20.h),

                //
                AnimatedButtonRow(
                  onCancelTap: () => Navigator.pop(context),
                  submitButtonState:
                      isLoading ? ButtonState.loading : ButtonState.normal,
                  onSubmitTap: () {
                    if (isLoading) return;

                    if (createPollFormkey.currentState!.validate()) {
                      context.read<PostBloc>().add(
                            PostCreatePollEvent(
                              question: _questionController.text.trim(),
                              option1: _option1Controller.text.trim(),
                              option2: _option2Controller.text.trim(),
                              option3: _option3Controller.text.trim(),
                              option4: _option4Controller.text.trim(),
                              durationMins: selectedPollDuration.inMinutes,
                              userEmail:
                                  context.usersBloc.currentUser?.email ?? "",
                            ),
                          );
                    }
                  },
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
