import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/custom_text_theme.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:shimmer/shimmer.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';

import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/core/params/comment_screen_argument_params.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/features/common/widgets/comment_textfield.dart';
import 'package:rasiin_tasks_app/features/common/widgets/empty_tasks_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rasiin_tasks_app/features/common/widgets/loader.dart';

import '../../../core/bloc/task bloc/tasks_bloc.dart';

class CommentsScreen extends StatefulWidget {
  final CommentScreenArgumentParams? commentScreenArgumentParams;
  const CommentsScreen({super.key, this.commentScreenArgumentParams});

  @override
  State<CommentsScreen> createState() => _CommentsScreenState();
}

class _CommentsScreenState extends State<CommentsScreen> {
  final commentFormKey = GlobalKey<FormState>();
  final TextEditingController commentController = TextEditingController();

  bool isSendEnabled = false;

  @override
  void initState() {
    super.initState();
    context.tasksBloc.add(
      GetTasksCommentEvent(
        load: true,
        refrenceType: widget.commentScreenArgumentParams?.refrenceType ?? '',
        taskId: widget.commentScreenArgumentParams?.taskId ?? '',
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    commentController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = context.usersBloc.currentUser;
    final taskBloc = context.tasksBloc;

    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Scaffold(
      appBar: AppBar(title: Text("Comments", style: textTheme.titleMedium)),
      body: BlocConsumer<TasksBloc, TasksState>(
        listener: (context, state) {
          //
          if (state is TasksStateError) {
            final errorMessage = state.appFailure.getErrorMessage();
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: errorMessage,
            );
          }
          if (state is TasksStateSuccess) {
            isSendEnabled = false;
            // SnackBarHelper.showSuccessSnackBar(context, state.message);
            commentController.clear();
            context.tasksBloc.add(
              //
              GetTasksCommentEvent(
                refrenceType:
                    widget.commentScreenArgumentParams?.refrenceType ?? '',
                taskId: widget.commentScreenArgumentParams?.taskId ?? '',
              ),
            );
          }
        },
        builder: (context, state) {
          // Profile image URL from the current user
          final String? profileImageUrl =
              context.usersBloc.currentUser?.profileImage;

          // Default image to be used when no profile image is available

          final List<CommentModel> comments = taskBloc.comments;
          final isLoading = state is TasksStateCommentsLoading;
          final isCommentSending = state is TasksStateLoading;
          return RefreshIndicator.adaptive(
            onRefresh: () async {
              context.tasksBloc.add(
                GetTasksCommentEvent(
                  load: true,
                  refrenceType:
                      widget.commentScreenArgumentParams?.refrenceType ?? '',
                  taskId: widget.commentScreenArgumentParams?.taskId ?? '',
                ),
              );
            },
            child: Column(
              children: [
                Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                  child: Row(
                    children: [
                      // CircleAvatar(
                      //   radius: 20.r,
                      //   backgroundImage: ResizeImage(
                      //     AssetImage(ImageConstants.profile2_jpg),
                      //     width: (20.r * 2).toInt(),
                      //     height: (20.r * 2).toInt(),
                      //   ),
                      // ),
                      ClipOval(
                        child: CachedNetworkImage(
                          imageUrl: profileImageUrl ?? '',
                          width: 60.w,
                          height: 60.w,
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(
                            width: 60.w,
                            height: 60.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: appColors.subtextColor,
                            ),
                            child: Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                          errorWidget: (context, url, error) => Container(
                            width: 60.w,
                            height: 60.w,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: appColors.subtextColor,
                            ),
                            child: Icon(
                              Icons.image,
                              color: appColors.whiteColor,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 10.w),
                      Flexible(
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                currentUser?.employeeName ?? '',
                                style: textTheme.bodyMedium,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            SizedBox(width: 5.w),
                            Container(
                              padding: EdgeInsets.all(3.r),
                              decoration: BoxDecoration(
                                color: appColors.primaryColor,
                                borderRadius: BorderRadius.circular(5.r),
                              ),
                              child: Text(
                                "Author",
                                style: CustomTextTheme.getLightStyle(
                                  context: context,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Divider(),
                SizedBox(height: 10.h),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        if (isLoading)
                          ListView.separated(
                            itemCount: 10,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) => Shimmer.fromColors(
                              baseColor: appColors.getShimmerColor(
                                isBaseColor: true,
                              ),
                              highlightColor: appColors.getShimmerColor(
                                isBaseColor: false,
                              ),
                              child: CommentLoadingSkeleton(),
                            ),
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 20.h),
                          )
                        else if (comments.isEmpty)
                          Container(
                            child: Center(
                              child: EmptyTasksWidget(
                                message: "Be the first comment",
                              ),
                            ),
                          )
                        //
                        else
                          ListView.separated(
                            itemCount: comments.length,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemBuilder: (context, index) {
                              return CommentBuilderWidget(
                                commentModel: comments[index],
                              );
                            },
                            separatorBuilder: (context, index) =>
                                SizedBox(height: 15.h),
                          ),
                        SizedBox(height: 20.h),
                        Text(
                          "That's all you got!",
                          style: textTheme.titleSmall,
                        ),
                        SizedBox(height: 20.h),
                      ],
                    ),
                  ),
                ),
                Material(
                  color: appColors.surfaceColor,
                  borderRadius: BorderRadius.circular(10.r),
                  elevation: 8,
                  child: Padding(
                    padding: const EdgeInsets.all(18.0),
                    child: CommentTextFiled(
                      controller: commentController,
                      labelText: "Be The first to comment ",
                      onChanged: (value) {
                        // This ensures the UI updates when the text changes
                        setState(() {
                          isSendEnabled = value.trim().isNotEmpty;
                        });
                      },
                      suffixWidget: isCommentSending
                          ? SizedBox(
                              width: 25.w,
                              height: 25.h,
                              child: const Loader(),
                            )
                          : Text(
                              "Send",
                              style: isSendEnabled
                                  ? CustomTextTheme.getLinkStyle(
                                      context: context,
                                      textDecorateion: TextDecoration.none,
                                    )
                                  : CustomTextTheme.getGreyStyle(
                                      context: context,
                                    ),
                            ),
                      onComment: isSendEnabled && !isCommentSending
                          ? () {
                              //
                              FocusScope.of(context).unfocus();

                              //
                              taskBloc.add(
                                SendCommentInAllTasksEvent(
                                  referenceType: widget
                                          .commentScreenArgumentParams
                                          ?.refrenceType ??
                                      '',
                                  comment: commentController.text.trim(),
                                  taskId: widget.commentScreenArgumentParams
                                          ?.taskId ??
                                      '',
                                  userEmail: currentUser?.email ?? '',
                                ),
                              );
                            }
                          : null,
                      isEnabled: isSendEnabled,
                    ),
                  ),
                ),

                //!!!
                SizedBox(height: 5.h),
              ],
            ),
          );
        },
      ),
    );
  }
}

class CommentLoadingSkeleton extends StatelessWidget {
  const CommentLoadingSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Row(
        children: [
          Container(
            width: 60.w,
            height: 50.h,
            decoration: BoxDecoration(
              color: appColors.subtextColor.withValues(alpha: 0.5),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 20.w),
          // (
          Expanded(
            child: Column(
              children: [
                Container(
                  // width: 180.w,
                  height: 20,
                  decoration: BoxDecoration(
                    color: appColors.subtextColor.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                SizedBox(height: 10.h),
                Container(
                  // width: 180.w,
                  height: 40,
                  decoration: BoxDecoration(
                    color: appColors.subtextColor.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CommentBuilderWidget extends StatelessWidget {
  final CommentModel commentModel;

  const CommentBuilderWidget({super.key, required this.commentModel});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final formattedDate = commentModel.getTimeDifference;

    return Row(
      children: [
        CustomImagePickerCard(
          isLoading: false,
          showIcon: false,
          imageUrl: commentModel.profileImage,
          radius: 25,
          isProfile: true,
          userName: commentModel.email,
        ),
        SizedBox(width: 10.w),
        Expanded(
          child: CustomContainer(
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              commentModel.email,
                              style: textTheme.bodyMedium,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 5.w),
                        ],
                      ),
                    ),
                    Text(
                      formattedDate,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: textTheme.bodyMedium,
                    ),
                  ],
                ),
                SizedBox(height: 10.h),
                Text(
                  commentModel.formattedComment,
                  style: textTheme.bodyMedium,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
