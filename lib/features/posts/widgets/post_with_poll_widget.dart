import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';
import 'package:rasiin_tasks_app/core/services/bottom_sheet_services.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/posts/widgets/post_widget.dart';

class PostWithPollBuilder extends StatelessWidget {
  final Function()? onComment;
  final Function()? onLike;
  final Function()? onMore;
  final Function(String optionId)? onVote;

  final PostModel post;

  const PostWithPollBuilder({
    super.key,
    required this.onComment,
    required this.onLike,
    required this.onMore,
    required this.onVote,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    return PostWidget(
      onComment: onComment,
      onLike: onLike,
      onMore: onMore,
      userName: post.postuserInfo.target?.fullName ?? '',
      userProfileImage: post.postuserInfo.target?.userImage ?? '',
      formattedPostDate: post.formattedCreatedAt,
      likesCount: post.likeCount,
      commentsCount: post.commentCount,
      isLiked: post.isLiked,
      likedUsers: post.likedPostUserInfo,
      postContent: _postContentWidget(
        context: context,
        post: post,
      ),
    );
  }

  Widget _postContentWidget({
    required BuildContext context,
    required PostModel post,
  }) {
    final theme = Theme.of(context);
    final colors = theme.colorScheme;
    final textTheme = theme.textTheme;
    final totalVotes = post.totalVotes;
    final isExpired = post.isExpired;
    final formattedPollExpiry = post.formattedPollExpiry;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Poll question
        Text(
          post.content,
          style: textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            height: 1.4,
          ),
        ),
        SizedBox(height: 16.h),

        // Poll container
        Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            color: colors.surfaceVariant.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
              color: colors.outline.withOpacity(0.2),
            ),
            boxShadow: [
              BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 8,
                  offset: Offset(0, 2))
            ],
          ),
          child: Column(
            children: [
              // Poll options
              ...post.pollOptions.map((option) => _buildPollOption(
                    context,
                    option,
                    totalVotes,
                  )),

              // Divider
              Padding(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                child: Divider(
                  height: 1,
                  thickness: 1,
                  color: colors.outline.withOpacity(0.1),
                ),
              ),

              // Poll status and votes
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Time remaining
                  Row(
                    children: [
                      Icon(
                        isExpired
                            ? Icons.hourglass_disabled
                            : Icons.hourglass_top,
                        size: 16.r,
                        color: isExpired
                            ? colors.error
                            : colors.onSurface.withOpacity(0.6),
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        formattedPollExpiry,
                        style: textTheme.bodySmall?.copyWith(
                          color: isExpired
                              ? colors.error
                              : colors.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),

                  // Vote count
                  GestureDetector(
                    onTap: () {
                      final votedUsers = post.votedPostUserInfo;
                      if (votedUsers.isNotEmpty) {
                        // show bottom sheet that displays liked user info
                        BottomSheetServices.showBottomSheet(
                          context,
                          headerTitleText: "Voted By",
                          headerTitleTextStyle: textTheme.titleLarge?.copyWith(
                            fontSize: 16.sp,
                          ),
                          formFields: _buildLikedUsersList(context: context),
                        );
                      }
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.people_alt_outlined,
                          size: 16.r,
                          color: colors.onSurface.withOpacity(0.6),
                        ),
                        SizedBox(width: 4.w),
                        Text(
                          '$totalVotes ${totalVotes == 1 ? 'vote' : 'votes'}',
                          style: textTheme.bodySmall?.copyWith(
                            color: colors.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPollOption(
    BuildContext context,
    PollOption option,
    int totalVotes,
  ) {
    final theme = Theme.of(context);
    final colors = theme.colorScheme;
    final textTheme = theme.textTheme;
    final percentage = totalVotes > 0 ? (option.votes / totalVotes) : 0.0;
    final isSelected = option.isVoted;

    return BlocBuilder<PostBloc, PostState>(
      buildWhen: (previous, currentState) {
        return currentState is PostVoteLoading ||
            currentState is PostVoteSuccess ||
            currentState is PostVoteFailure;
      },
      builder: (context, state) {
        final isVoteLoading = state is PostVoteLoading &&
            state.postId == post.postId &&
            state.optionId == option.pollOptionId;
        return GestureDetector(
          onTap: () => isVoteLoading ? null : _handleVote(context, option),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Left: Radio or loading spinner
                isVoteLoading
                    ? Padding(
                        padding: EdgeInsets.only(right: 10),
                        child: SizedBox(
                          width: 15.w,
                          height: 15.h,
                          child: CircularProgressIndicator(
                            strokeWidth: 4,
                            color: colors.primary,
                          ),
                        ),
                      )
                    : AnimatedContainer(
                        duration: Duration(milliseconds: 200),
                        width: 20.r,
                        height: 20.r,
                        margin: EdgeInsets.only(right: 12.w),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected
                                ? colors.primary
                                : colors.onSurface.withOpacity(0.4),
                            width: 2.w,
                          ),
                        ),
                        child: AnimatedScale(
                          scale: isSelected ? 1 : 0,
                          duration: Duration(milliseconds: 200),
                          child: Center(
                            child: Container(
                              width: 12.r,
                              height: 12.r,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: colors.primary,
                              ),
                            ),
                          ),
                        ),
                      ),

                // Text + percentage + progress bar
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Option text + percentage
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              option.optionText,
                              style: textTheme.bodyMedium?.copyWith(
                                fontWeight: isSelected ? FontWeight.w600 : null,
                                color: isSelected
                                    ? colors.primary
                                    : colors.onSurface,
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          if (totalVotes > 0)
                            Text(
                              '${(percentage * 100).toStringAsFixed(1)}%',
                              style: textTheme.bodyMedium?.copyWith(
                                fontWeight: isSelected ? FontWeight.w600 : null,
                                color: isSelected
                                    ? colors.primary
                                    : colors.onSurface.withOpacity(0.7),
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 8.h),

                      // Progress bar with animation
                      LayoutBuilder(
                        builder: (context, constraints) {
                          return Stack(
                            children: [
                              Container(
                                height: 6.h,
                                width: constraints.maxWidth,
                                decoration: BoxDecoration(
                                  color: colors.surfaceVariant.withOpacity(0.5),
                                  borderRadius: BorderRadius.circular(3.r),
                                ),
                              ),
                              AnimatedContainer(
                                duration: Duration(milliseconds: 600),
                                curve: Curves.easeOutQuart,
                                height: 6.h,
                                width: constraints.maxWidth * percentage,
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? colors.primary
                                      : colors.primary.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(3.r),
                                  boxShadow: [
                                    if (isSelected)
                                      BoxShadow(
                                        color: colors.primary.withOpacity(0.3),
                                        blurRadius: 4,
                                        spreadRadius: 1,
                                      )
                                  ],
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleVote(BuildContext context, PollOption option) {
    // Check if the post is expired before allowing the user to vote
    if (post.isExpired) {
      SnackBarHelper.showErrorSnackBar(
        context: context,
        message: "Poll has expired",
      );
      return;
    }
    // Check if the user has already voted
    if (option.isVoted) {
      // SnackBarHelper.showErrorSnackBar(
      //   context: context,
      //   message: "You have already voted for this option",
      // );
      return;
    }

    // Call the onVote function to handle the voting logic
    onVote?.call(option.pollOptionId);
  }

  Widget _buildLikedUsersList({
    required BuildContext context,
  }) {
    //
    // final appColors = context.appColors;
    final textTheme = context.textTheme;
    final isLoading = context.postBloc.state is PostGetAllLoading;
    final isEmpty = context.postBloc.state is PostGetAllEmpty;

    return CustomListGridView<VotedPostUserInfo>(
      items: post.votedPostUserInfo,
      isLoading: isLoading,
      isEmpty: isEmpty,
      showFooter: false,
      layoutType: LayoutType.listView,
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
      onRefresh: () {},
      itemBuilder: (context, item) {
        return Row(
          children: [
            CustomImagePickerCard(
              radius: 20,
              imageUrl: item.userImage,
              isProfile: true,
              userName: item.fullName,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                item.fullName,
                style: textTheme.bodyLarge?.copyWith(
                  fontSize: 14.sp,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }
}
