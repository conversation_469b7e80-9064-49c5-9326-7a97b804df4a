import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/post-bloc/post_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';
import 'package:rasiin_tasks_app/core/services/bottom_sheet_services.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';

class PostWidget extends StatelessWidget {
  final Function()? onComment;
  final Function()? onLike;
  final Function()? onMore;
  final String userName;
  final String userProfileImage;
  final String formattedPostDate;
  final int likesCount;
  final int commentsCount;
  final bool isLiked;
  final Widget postContent;
  final List<LikedPostUserInfo> likedUsers;

  const PostWidget({
    super.key,
    required this.onComment,
    required this.onLike,
    required this.onMore,
    required this.userName,
    required this.userProfileImage,
    required this.formattedPostDate,
    required this.likesCount,
    required this.commentsCount,
    required this.isLiked,
    required this.postContent,
    required this.likedUsers,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return CustomContainer(
      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //! post title
          Row(
            children: [
              // CircleAvatar(
              //   radius: 25.r,
              //   child: ClipOval(
              //     child: CachedNetworkImage(
              //       imageUrl: userProfileImage,
              //       fit: BoxFit.cover,
              //     ),
              //   ),
              // ),
              CustomImagePickerCard(
                radius: 25,
                imageUrl: userProfileImage,
                isProfile: true,
                userName: userName,
              ),
              SizedBox(width: 10.w),
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    userName,
                    style: textTheme.bodyLarge,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    // '3 months ago',
                    formattedPostDate,
                    // style: textTheme.bodyMedium,
                    style: textTheme.labelMedium,
                  ),
                ],
              )),
              IconButton(
                onPressed: () async => onMore?.call(),
                icon: Icon(
                  Icons.more_vert,
                  color: appColors.iconColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 20.h),

          //!post content
          // Text(
          //   "Let's Learn something",
          //   style: textTheme.bodyMedium,
          // ),
          postContent,
          SizedBox(height: 20.h),

          /// like and comment section
          Row(
            children: [
              GestureDetector(
                onTap: onLike,
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                  decoration: BoxDecoration(
                    color: appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(26),
                  ),
                  child: Icon(
                    // Icons.thumb_up,
                    isLiked
                        ? FontAwesomeIcons.solidThumbsUp
                        : FontAwesomeIcons.thumbsUp,
                    color: isLiked
                        ? appColors.primaryColor
                        : appColors.subtextColor,
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              GestureDetector(
                onTap: () {
                  print("Liked Users: $likedUsers");
                  if (likedUsers.isNotEmpty) {
                    // show bottom sheet that displays liked user info
                    BottomSheetServices.showBottomSheet(
                      context,
                      headerTitleText: "Liked By",
                      headerTitleTextStyle: textTheme.titleLarge?.copyWith(
                        fontSize: 16.sp,
                      ),
                      formFields: _buildLikedUsersList(context: context),
                    );
                  }
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                  decoration: BoxDecoration(
                    color: appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(26),
                  ),
                  child: Text(
                    // "10 Likes",
                    "$likesCount Likes",
                    style: textTheme.labelLarge,
                  ),
                ),
              ),
              SizedBox(width: 10.w),
              GestureDetector(
                onTap: onComment,
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                  decoration: BoxDecoration(
                    color: appColors.surfaceColor,
                    borderRadius: BorderRadius.circular(26),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        // Icons.comment,
                        FontAwesomeIcons.comment,
                        color: appColors.subtextColor,
                      ),
                      SizedBox(width: 5.w),
                      Text(
                        // "2 Comments",
                        "$commentsCount Comments",
                        style: textTheme.labelMedium,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLikedUsersList({
    required BuildContext context,
  }) {
    //
    // final appColors = context.appColors;
    final textTheme = context.textTheme;
    final isLoading = context.postBloc.state is PostGetAllLoading;
    final isEmpty = context.postBloc.state is PostGetAllEmpty;

    return CustomListGridView<LikedPostUserInfo>(
      items: likedUsers,
      isLoading: isLoading,
      isEmpty: isEmpty,
      showFooter: false,
      layoutType: LayoutType.listView,
      padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 10.h),
      onRefresh: () {},
      itemBuilder: (context, item) {
        return Row(
          children: [
            CustomImagePickerCard(
              radius: 20,
              imageUrl: item.userImage,
              isProfile: true,
              userName: item.fullName,
            ),
            SizedBox(width: 15.w),
            Expanded(
              child: Text(
                item.fullName,
                style: textTheme.bodyLarge?.copyWith(
                  fontSize: 14.sp,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        );
      },
    );
  }
}

