import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/models/post_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/posts/widgets/post_widget.dart';

class PostWIthMediaBuilder extends StatelessWidget {
  final Function()? onComment;
  final Function()? onLike;
  final Function()? onMore;
  final PostModel post;

  const PostWIthMediaBuilder({
    super.key,
    required this.onComment,
    required this.onLike,
    required this.onMore,
    required this.post,
  });

  @override
  Widget build(BuildContext context) {
    return PostWidget(
      onComment: onComment,
      onLike: onLike,
      onMore: onMore,
      userName: post.postuserInfo.target?.fullName ?? '',
      userProfileImage: post.postuserInfo.target?.userImage ?? '',
      formattedPostDate: post.formattedCreatedAt,
      likesCount: post.likeCount,
      commentsCount: post.commentCount,
      isLiked: post.isLiked,
      likedUsers: post.likedPostUserInfo,
      postContent: _postContentWidget(
        context: context,
        post: post,
      ),
    );
  }

  Widget _postContentWidget({
    required BuildContext context,
    required PostModel post,
  }) {
    final textTheme = context.textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Post text content
        if (post.content.isNotEmpty)
          Padding(
            padding: EdgeInsets.only(bottom: post.hasMedia ? 12.h : 0),
            child: Text(
              post.content,
              style: textTheme.bodyLarge,
            ),
          ),

        // Media display
        if (post.hasMedia) _buildMediaDisplay(context, post.media),
      ],
    );
  }

  Widget _buildMediaDisplay(BuildContext context, List<PostMedia> media) {
    final time = DateTime.now().millisecondsSinceEpoch;
    // final uniqueHeroTag = 'media_${post.postId}_${media.first.fileUrl}';
    final uniqueHeroTag = 'media_${post.postId}_${media.first.fileUrl}_$time';

    if (media.length == 1) {
      return GestureDetector(
        onTap: () => _openFullScreenMedia(context, media.first, uniqueHeroTag),
        child: Hero(
          tag: uniqueHeroTag,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.r),
            child: Stack(
              children: [
                _buildMediaItem(media.first),
                if (!media.first.isImage)
                  Center(
                    child: Icon(
                      Icons.play_circle_fill,
                      size: 48.r,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 0.h,
        childAspectRatio: 1,
      ),
      itemCount: min(4, media.length),
      // itemCount: media.length,
      itemBuilder: (context, index) {
        final heroTag = 'media_${post.postId}_${media[index].fileUrl}_$index';

        if (index == 3 && media.length > 4) {
          return GestureDetector(
            onTap: () => _openMediaGallery(context, media, index),
            child: Stack(
              fit: StackFit.expand,
              children: [
                _buildMediaItem(media[index]),
                Container(
                  color: Colors.black54,
                  child: Center(
                    child: Text(
                      '+${media.length - 4}',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return GestureDetector(
          onTap: () => _openFullScreenMedia(context, media[index], heroTag),
          child: Hero(
            tag: heroTag,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8.r),
              child: Stack(
                children: [
                  _buildMediaItem(media[index]),
                  if (!media[index].isImage)
                    Center(
                      child: Icon(
                        Icons.play_circle_fill,
                        size: 32.r,
                        color: Colors.white.withOpacity(0.8),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMediaItem(PostMedia media) {
    return media.isImage
        ? CachedNetworkImage(
            imageUrl: media.fileUrl,
            fit: BoxFit.cover,
            progressIndicatorBuilder: (context, url, progress) => Center(
              child: CircularProgressIndicator(
                value: progress.progress,
              ),
            ),
            errorWidget: (context, url, error) => Icon(Icons.error),
          )
        : Container(
            color: Colors.black12,
            child: Center(
              child: Icon(Icons.videocam, size: 48.r),
            ),
          );
  }

  void _openFullScreenMedia(
      BuildContext context, PostMedia media, String heroTag) {
    // Navigator.pushNamed(
    //   context,
    //   ScreenConstants.fullScreenMedia,
    //   arguments: FullScreenMediaArguments(
    //     media: media,
    //     heroTag: heroTag,
    //   ),
    // );
  }

  void _openMediaGallery(
      BuildContext context, List<PostMedia> media, int index) {
    // Navigator.pushNamed(
    //   context,
    //   ScreenConstants.mediaGallery,
    //   arguments: MediaGalleryArguments(
    //     mediaList: media,
    //     initialIndex: index,
    //   ),
    // );
  }
}
