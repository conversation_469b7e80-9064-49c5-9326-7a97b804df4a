import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';

class AnimatedButtonRow extends StatelessWidget {
  final String cancelText;
  final String submitText;
  final ButtonState cancelButtonState;
  final ButtonState submitButtonState;
  final VoidCallback onCancelTap;
  final VoidCallback onSubmitTap;
  final Duration cancelAnimationDelay;
  final Duration submitAnimationDelay;
  final AnimationDirection cancelAnimationDirection;
  final AnimationDirection submitAnimationDirection;
  final ButtonStyleType cancelButtonStyleType;
  final ButtonStyleType submitButtonStyleType;
  final double horizentalMarging;
  final double verticalMargin;

  const AnimatedButtonRow({
    super.key,
    this.cancelText = 'Cancel',
    this.submitText = 'Submit',
    required this.onCancelTap,
    required this.onSubmitTap,
    required this.submitButtonState,
    this.cancelButtonState = ButtonState.normal,
    this.cancelAnimationDelay = const Duration(milliseconds: 300),
    this.submitAnimationDelay = const Duration(milliseconds: 300),
    this.cancelAnimationDirection = AnimationDirection.leftToRight,
    this.submitAnimationDirection = AnimationDirection.rightToLeft,
    this.cancelButtonStyleType = ButtonStyleType.disabled,
    this.submitButtonStyleType = ButtonStyleType.filled,
    this.horizentalMarging = 20,
    this.verticalMargin = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: horizentalMarging.w,
        vertical: verticalMargin.h,
      ),
      child: Row(
        children: [
          Expanded(
            child: AnimatedItemWrapper(
              delay: cancelAnimationDelay,
              animationDirection: cancelAnimationDirection,
              child: CustomButton(
                buttonText: cancelText,
                buttonState: cancelButtonState,
                buttonStyleType: cancelButtonStyleType,
                onTap: onCancelTap,
              ),
            ),
          ),
          SizedBox(width: 20.w),
          Expanded(
            child: AnimatedItemWrapper(
              delay: submitAnimationDelay,
              animationDirection: submitAnimationDirection,
              child: CustomButton(
                buttonText: submitText,
                buttonState: submitButtonState,
                buttonStyleType: submitButtonStyleType,
                onTap: onSubmitTap,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
