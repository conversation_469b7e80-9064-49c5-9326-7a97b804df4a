import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class CustomSmallContainer extends StatelessWidget {
  final double horizontalPadding;
  final double verticalPadding;
  final String text;
  final double? fontSize;
  final Color? textColor;
  final Color? backgroundColor;
  final double borderRadius;
  final bool showBorder;

  const CustomSmallContainer({
    super.key,
    this.horizontalPadding = 16,
    this.verticalPadding = 8,
    required this.text,
    this.textColor,
    this.fontSize,
    this.backgroundColor,
    this.borderRadius = 16,
    this.showBorder = true,
  });

  Color _getBackgroundColor({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return backgroundColor?.withValues(alpha: 0.1) ?? appColors.cardColor;
  }

  Color _getTextColor({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return textColor ?? appColors.textColor;
  }

  Color _getBorderColor({
    required BuildContext context,
    required bool hasBackgroundColor,
  }) {
    final appColors = context.appColors;
    return hasBackgroundColor
        ? _getTextColor(context: context)
        : appColors.borderInactiveColor;
  }

  @override
  Widget build(BuildContext context) {
    final effectiveBackgroundColor = _getBackgroundColor(context: context);
    final effectiveTextColor = _getTextColor(context: context);
    final effectiveBorderColor = _getBorderColor(
        context: context, hasBackgroundColor: backgroundColor != null);

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding.w, vertical: verticalPadding.h),
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(borderRadius.r),
        border: showBorder ? Border.all(color: effectiveBorderColor) : null,
      ),
      child: FittedBox(
        child: Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: effectiveTextColor,
                fontSize: fontSize?.sp,
                fontWeight: FontWeight.bold,
              ),
        ),
      ),
    );
  }
}
