import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';

class EmptyTasksWidget extends StatelessWidget {
  final String message;
  final double? marginTop;
  final double? marginBottom;
  const EmptyTasksWidget({
    super.key,
    this.message = "No Tasks Assigned Yet",
    this.marginTop,
    this.marginBottom,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return GestureDetector(
      onTap: () {
        //
      },
      child: Center(
        child: Container(
          margin: EdgeInsets.only(
            top: marginTop?.h ?? 20,
            bottom: marginBottom?.h ?? 20,
          ),
          height: 180.h,
          width: MediaQuery.of(context).size.width - 60.w,
          // padding: EdgeInsets.symmetric(),
          clipBehavior: Clip.hardEdge,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: appColors.backgroundColor,
            borderRadius: BorderRadius.circular(16.r),
            boxShadow: [
              BoxShadow(
                color: appColors.blackColor.withValues(alpha: 0.1),
                spreadRadius: 0.8,
                blurRadius: 1,
              ),
            ],
          ),
          child: Column(
            spacing: 15.h,
            children: [
              //!
              Container(
                height: 100.h,
                // width: double.infinity,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                      ImageConstants.backGround_jpg,
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: 10.h,
                      left: 10.w,
                      child: Container(
                        height: 40.h,
                        width: 40.w,
                        padding: EdgeInsets.all(7.w),
                        decoration: BoxDecoration(
                          color: appColors.primaryColor.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(10.r),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  appColors.whiteColor.withValues(alpha: 0.1),
                              blurRadius: 1,
                            ),
                          ],
                        ),
                        child: Image.asset(
                          ImageConstants.bag_png,
                          fit: BoxFit.cover,
                          color: appColors.backgroundColor,
                        ),
                      ),
                    ),
                    //!
                    Positioned(
                      top: 20.h,
                      left: 120.w,
                      child: Image.asset(
                        ImageConstants.backGround_jpg,
                        height: 60.h,
                        width: 50.w,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ],
                ),
              ),
              // SizedBox(
              //   height: 10.h,
              // ),
              //!
              Text(
                message,
                textAlign: TextAlign.center,
                style: textTheme.bodyLarge?.copyWith(
                  color: appColors.primaryColor,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
