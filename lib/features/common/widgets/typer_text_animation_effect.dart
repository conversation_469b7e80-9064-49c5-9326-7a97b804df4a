import 'package:flutter/material.dart';

class TypewriterTextAnimationEffect extends StatefulWidget {
  final String text;
  final TextStyle? textStyle;
  final Duration duration;

  const TypewriterTextAnimationEffect({
    Key? key,
    required this.text,
    this.textStyle,
    this.duration = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  _TypewriterTextAnimationEffectState createState() =>
      _TypewriterTextAnimationEffectState();
}

class _TypewriterTextAnimationEffectState
    extends State<TypewriterTextAnimationEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _charCount;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    _charCount =
        StepTween(begin: 0, end: widget.text.length).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final String visibleString = widget.text.substring(0, _charCount.value);
        return Text(
          visibleString,
          style: widget.textStyle ?? Theme.of(context).textTheme.bodySmall,
        );
      },
    );
  }
}
