import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomDropDown<T> extends StatelessWidget {
  const CustomDropDown({
    super.key,
    required this.items,
    required this.displayItem,
    required this.labelText,
    required this.onChanged,
    this.onTap,
    this.leadingImgPath,
    this.validator,
    this.value,
    this.fillColor,
  });

  final List<T> items;
  final void Function(T?)? onChanged;
  final String? Function(T?)? displayItem;
  final String labelText;
  final Function()? onTap;
  final String? leadingImgPath;
  final String? Function(T?)? validator;
  final T? value;
  final Color? fillColor;
  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white : Colors.black;
    return DropdownButtonFormField<T>(
      value: value,
      onTap: onTap,
      items: items.map((status) {
        return DropdownMenuItem(
          value: status,
          child: Text(
            displayItem!(status)!,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        );
      }).toList(),
      onChanged: onChanged,
      validator: validator,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        label: Text(
          labelText,
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        prefix: leadingImgPath != null
            ? Padding(
                padding: EdgeInsets.only(right: 10.w),
                child: SvgPicture.asset(
                  leadingImgPath!,
                  width: 20.w,
                  height: 20.h,
                  colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
                ),
              )
            : null,
        fillColor: fillColor ?? Colors.transparent,
        filled: true,
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15.r),
          borderSide: BorderSide(color: Colors.blue.withValues(alpha: 0.5)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15.r),
          borderSide: BorderSide(color: Colors.blue.withValues(alpha: 0.7)),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15.r),
          borderSide: const BorderSide(color: Colors.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(15.r),
          borderSide: const BorderSide(color: Colors.red),
        ),
      ),
    );
  }
}
