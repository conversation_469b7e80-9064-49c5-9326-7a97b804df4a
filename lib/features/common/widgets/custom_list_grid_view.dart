import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_list_item.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_loading_skeleton.dart';

import 'custom_footer.dart';

class CustomListGridView<T> extends StatelessWidget {
  final List<T> items;
  final int? itemCount;
  final Widget Function(BuildContext context, T item) itemBuilder;
  final Widget? seperatedWidget;
  final bool isLoading;
  final bool isEmpty;
  final bool showFooter;
  final String emtypWidgetMessage;
  final int loadingItemCount;
  final double loadingItemHeight;
  final Widget Function()? footerBuilder;
  final Widget Function()? emptyDataBuilder;
  final LayoutType layoutType;
  final int? gridCrossAxisCount;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final Duration animationDuration;
  final double? marginTopEptyWidget;
  final double? marginBottomEptyWidget;
  final void Function()? onRefresh;
  final EdgeInsetsGeometry? padding;
  final ScrollController? controller;

  const CustomListGridView({
    super.key,
    required this.items,
    this.itemCount,
    required this.itemBuilder,
    this.seperatedWidget,
    required this.isLoading,
    required this.isEmpty,
    this.showFooter = true,
    this.emtypWidgetMessage = 'No data available',
    this.loadingItemCount = 12,
    this.loadingItemHeight = 70,
    this.footerBuilder,
    this.emptyDataBuilder,
    required this.layoutType,
    this.gridCrossAxisCount,
    this.physics,
    this.scrollDirection = Axis.vertical,
    this.animationDuration = const Duration(milliseconds: 500),
    this.marginTopEptyWidget,
    this.marginBottomEptyWidget,
    required this.onRefresh,
    this.padding,
    this.controller,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return CustomLoadingSkeleton(
        loadingItems: [
          LoadingSkeletonItem(
            scrollDirection: scrollDirection,
            layoutType: layoutType,
            itemCount: loadingItemCount,
            height: loadingItemHeight,
          ),
        ],
      );
    }

    if (isEmpty) {
      return emptyDataBuilder != null
          ? emptyDataBuilder!.call()
          : Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  emtypWidgetMessage,
                  style: context.textTheme.bodyLarge,
                  textAlign: TextAlign.center,
                ),
                if (onRefresh != null) ...[
                  SizedBox(height: 10.h),
                  CustomButton(
                    buttonState: ButtonState.normal,
                    buttonText: "Retry",
                    width: 80,
                    onTap: onRefresh,
                  ),
                ],
              ],
            );
    }

    // Use itemCount when items is empty or null
    // int displayItemCount = items.isEmpty ? (itemCount ?? 0) : items.length;
    int displayItemCount = (items.isEmpty && itemCount != null)
        ? itemCount!
        : (itemCount != null && itemCount! < items.length && items.isNotEmpty)
            ? itemCount!
            : items.length;
    // ListView implementation
    if (layoutType == LayoutType.listView) {
      return ListView.separated(
        controller: controller,
        itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
        shrinkWrap: true,
        padding:
            padding ?? EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.h),
        scrollDirection: scrollDirection,
        physics: physics ?? const AlwaysScrollableScrollPhysics(),
        separatorBuilder: (context, index) =>
            seperatedWidget ?? SizedBox(height: 15.h),
        itemBuilder: (context, index) {
          if (showFooter && index == displayItemCount) {
            return footerBuilder?.call() ?? const CustomFooter();
          }

          // Handle rendering of individual item
          T? item = items.isNotEmpty ? items[index] : null;

          // Handle case when item is null (e.g., empty list), render placeholder
          return CustomAnimatedItem(
            index: index,
            itemCount: displayItemCount,
            duration: animationDuration,
            animationDirection: index % 2 == 0
                ? (scrollDirection == Axis.vertical
                    ? AnimationDirection.topToBottom
                    : AnimationDirection.leftToRight)
                : (scrollDirection == Axis.vertical
                    ? AnimationDirection.bottomToTop
                    : AnimationDirection.rightToLeft),
            child: itemBuilder(context, item ?? null as T), // Use `null` safely
          );
        },
      );
    }

    // GridView implementation
    if (layoutType == LayoutType.gridView) {
      return GridView.builder(
        controller: controller,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: gridCrossAxisCount ?? 3,
          crossAxisSpacing: 10.w,
          mainAxisSpacing: 10.h,
          childAspectRatio: 1.0,
        ),
        itemCount: showFooter ? displayItemCount + 1 : displayItemCount,
        shrinkWrap: true,
        scrollDirection: scrollDirection,
        physics: physics ?? const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          if (showFooter &&
              index == displayItemCount &&
              (items.isNotEmpty || (itemCount != null && itemCount! > 0))) {
            return footerBuilder?.call() ??
                const CustomFooter(
                  bottomPadding: 0,
                  topPadding: 0,
                );
          }
          T? item = items.isNotEmpty ? items[index] : null;

          // Handle case when item is null (e.g., empty list), render placeholder
          return CustomAnimatedItem(
            index: index,
            duration: animationDuration,
            itemCount: displayItemCount,
            animationDirection: index % 2 == 0
                ? (scrollDirection == Axis.vertical
                    ? AnimationDirection.topToBottom
                    : AnimationDirection.leftToRight)
                : (scrollDirection == Axis.vertical
                    ? AnimationDirection.bottomToTop
                    : AnimationDirection.rightToLeft),
            child: itemBuilder(context, item ?? null as T), // Use `null` safely
          );
        },
      );
    }

    return const SizedBox
        .shrink(); // Return a fallback if no valid content type
  }
}
