import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/shape_type_enum.dart';

class CustomFloatingActionButton extends StatelessWidget {
  const CustomFloatingActionButton({
    super.key,
    required this.onPressed,
    this.shapeType = ShapeType.circular,
    this.borderRadius = 16.0,
    this.icon = const Icon(Icons.add),
    this.borderColor,
    this.backgroundColor,
    this.foregroundColor,
    this.tooltip,
  });

  final void Function()? onPressed;
  final ShapeType shapeType;
  final double borderRadius;
  final Icon icon;
  final Color? borderColor;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final String? tooltip;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    Color _foregroundColor = appColors.whiteColor;
    return FloatingActionButton(
      // foregroundColor: foregroundColor ?? _foregroundColor,
      foregroundColor: _foregroundColor,
      backgroundColor: backgroundColor ?? getBackgroundColor(context),
      shape: _getShape(context),
      onPressed: onPressed,
      tooltip: tooltip, // Accessibility hint
      child: icon,
    );
  }

  Color getBackgroundColor(BuildContext context) {
    final appColors = context.appColors;
    return appColors.buttonColor;
    // return shapeType == ShapeType.circular
    //     ? appColors.primaryColor
    //     : appColors.backgroundColor;
  }

  // Method to return shape based on ShapeType
  ShapeBorder _getShape(BuildContext context) {
    final appColors = context.appColors;
    final effectiveBorderColor = borderColor ?? appColors.transparent;

    switch (shapeType) {
      case ShapeType.roundedRectangle:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(borderRadius.r),
          side: BorderSide(color: effectiveBorderColor),
        );
      case ShapeType.rectangle:
        return RoundedRectangleBorder(
          borderRadius: BorderRadius.zero,
          side: BorderSide(color: effectiveBorderColor),
        );
      case ShapeType.circular:
        return CircleBorder(
          side: BorderSide(color: effectiveBorderColor),
        );
    }
  }
}
