// ignore_for_file: public_member_api_docs, sort_constructors_first, must_be_immutable
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import '../../../app/theme/colors/app_colors.dart';

class CustomTextField extends StatefulWidget {
  final TextEditingController? controller;
  final FaIcon? faIcon;
  final String? labelText;
  final String? hintText;
  final String? initialValue;
  final TextInputType? keyboardType;
  final int? maxLine;
  bool isObsecureText;
  final bool isPasswordField;
  final Function(String)? onChanged;
  final AutovalidateMode autovalidateMode;
  final String? Function(String?)? validator;
  final double borderRadius;
  final bool isReadOnly;
  final Function()? onTap;
  final Color? fillColor;
  final Color? focusedBorderColor;
  final Color? enabledBorderColor;
  final String? leadingIconPath;
  final double? height;
  final TextInputAction? textInputAction;
  final List<String>? autofillHints;
  final Function()? onEditingComplete;
  final Widget? suffixWidget;

  CustomTextField({
    super.key,
    this.initialValue,
    this.height,
    this.isReadOnly = false,
    this.focusedBorderColor,
    this.enabledBorderColor,
    this.leadingIconPath,
    this.fillColor,
    this.onTap,
    this.controller,
    this.faIcon,
    this.labelText,
    this.hintText,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.isObsecureText = false,
    this.textInputAction,
    this.isPasswordField = false,
    this.maxLine = 1, // Default single line, set to null for multiline
    this.keyboardType = TextInputType.text, // Default to single line input
    this.borderRadius = 16,
    this.validator,
    this.onChanged,
    this.autofillHints,
    this.onEditingComplete,
    this.suffixWidget,
  });

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Container(
      width: MediaQuery.of(context).size.width.w,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(widget.borderRadius.r),
      ),
      child: TextFormField(
        onTap: widget.onTap,
        style: textTheme.bodyMedium,
        initialValue: widget.initialValue,
        enableSuggestions: true,
        controller: widget.controller,
        keyboardType: widget.maxLine != 1
            ? TextInputType
                .multiline // Set multiline keyboard for multi-line input
            : widget.keyboardType,
        maxLines:
            widget.maxLine ?? null, // Allow multiline input if maxLine is null
        textInputAction: widget.textInputAction ??
            (widget.maxLine! <= 1 ? TextInputAction.next : null),
        obscureText: widget.isObsecureText,
        onChanged: widget.onChanged,
        autovalidateMode: widget.autovalidateMode,
        validator: widget.validator,
        readOnly: widget.isReadOnly,
        autofillHints: widget.autofillHints,
        onEditingComplete: widget.onEditingComplete,
        onTapOutside: (event) {
          FocusScope.of(context).unfocus();
        },
        decoration: InputDecoration(
          hintText: widget.hintText,
          label: widget.labelText != null
              ? Text(
                  widget.labelText!,
                  style: textTheme.labelMedium,
                )
              : null,
          fillColor: widget.fillColor,
          filled: true,
          prefixIcon: widget.faIcon != null
              ? Padding(
                  padding: EdgeInsets.all(8.0.w),
                  child: widget.faIcon,
                )
              : widget.leadingIconPath != null
                  ? Padding(
                      padding: EdgeInsets.all(8.0.w),
                      child: SvgPicture.asset(
                        widget.leadingIconPath ?? '',
                        width: 20.w,
                        height: 20.h,
                        colorFilter: ColorFilter.mode(
                          appColors.subtextColor,
                          BlendMode.srcIn,
                        ),
                      ),
                    )
                  : null,
          suffixIcon: widget.suffixWidget ??
              (widget.isPasswordField
                  ? IconButton(
                      onPressed: () {
                        setState(() {
                          widget.isObsecureText = !widget.isObsecureText;
                        });
                      },
                      icon: widget.isObsecureText
                          ? const Icon(
                              Icons.visibility_off,
                            )
                          : Icon(
                              Icons.visibility,
                              color: appColors.primaryColor,
                            ),
                    )
                  : null),
        ),
      ),
    );
  }
}
