import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';

/// A text widget that provides error boundary for UTF-16 rendering issues
class ErrorBoundaryText extends StatelessWidget {
  final String text;
  final TextStyle? style;
  final TextAlign? textAlign;
  final TextDirection? textDirection;
  final bool? softWrap;
  final TextOverflow? overflow;
  final double? textScaleFactor;
  final int? maxLines;
  final String? semanticsLabel;
  final TextWidthBasis? textWidthBasis;
  final TextHeightBehavior? textHeightBehavior;
  final String fallbackText;

  const ErrorBoundaryText(
    this.text, {
    Key? key,
    this.style,
    this.textAlign,
    this.textDirection,
    this.softWrap,
    this.overflow,
    this.textScaleFactor,
    this.maxLines,
    this.semanticsLabel,
    this.textWidthBasis,
    this.textHeightBehavior,
    this.fallbackText = '[Text Error]',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    try {
      final safeText = text.safeText;
      
      // Additional validation: try to create a TextSpan to catch UTF-16 issues early
      TextSpan(text: safeText);
      
      return Text(
        safeText,
        style: style,
        textAlign: textAlign,
        textDirection: textDirection,
        softWrap: softWrap,
        overflow: overflow,
        textScaleFactor: textScaleFactor,
        maxLines: maxLines,
        semanticsLabel: semanticsLabel,
        textWidthBasis: textWidthBasis,
        textHeightBehavior: textHeightBehavior,
      );
    } catch (e) {
      print('🚨 Text rendering error caught: $e');
      print('🔤 Original text: $text');
      
      // Return fallback text with error styling
      return Text(
        fallbackText,
        style: (style ?? const TextStyle()).copyWith(
          color: Colors.red[300],
          fontStyle: FontStyle.italic,
        ),
        textAlign: textAlign,
        textDirection: textDirection,
        softWrap: softWrap,
        overflow: overflow,
        textScaleFactor: textScaleFactor,
        maxLines: maxLines,
        semanticsLabel: semanticsLabel,
        textWidthBasis: textWidthBasis,
        textHeightBehavior: textHeightBehavior,
      );
    }
  }
}
