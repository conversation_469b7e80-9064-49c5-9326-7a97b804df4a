// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:shimmer/shimmer.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'custom_container.dart';

class HomeLoadingSkeleton extends StatelessWidget {
  final List<Widget> loadingItems; // A list of loading items to display

  const HomeLoadingSkeleton({
    super.key,
    this.loadingItems = const [],
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        children: [
          ...loadingItems, // Add the list of loading items dynamically
        ],
      ),
    );
  }
}

class CustomLoadingSkeleton extends StatelessWidget {
  final List<Widget> loadingItems; // A list of loading items to display

  const CustomLoadingSkeleton({
    super.key,
    this.loadingItems = const [],
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      physics: const AlwaysScrollableScrollPhysics(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ...loadingItems, // Add the list of loading items dynamically
        ],
      ),
    );
  }
}

//
class LoadingSkeletonItem extends StatelessWidget {
  final double height;
  final double? width;
  final int itemCount;
  final LayoutType layoutType;
  final ScrollPhysics? physics;
  final Axis scrollDirection;

  const LoadingSkeletonItem({
    super.key,
    this.height = 150,
    this.width,
    required this.layoutType,
    this.itemCount = 9,
    this.physics,
    this.scrollDirection = Axis.vertical,
  });

  @override
  Widget build(BuildContext context) {
    switch (layoutType) {
      case LayoutType.gridView:
        return SizedBox(
          height: ((itemCount > 0 ? (itemCount / 3).ceil() : 1) * height.h),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 10.w),
            child: GridView.builder(
              scrollDirection: scrollDirection,
              shrinkWrap: true,
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
              physics: physics ?? const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 15.w,
                mainAxisSpacing: 15.h,
                childAspectRatio: 0.9,
              ),
              itemCount: itemCount,
              itemBuilder: (context, index) {
                return _buildShimmerContainer(context);
              },
            ),
          ),
        );
      case LayoutType.listView:
        return SizedBox(
          height: height.h * itemCount,
          child: ListView.separated(
            scrollDirection: scrollDirection,
            shrinkWrap: true,
            physics: physics ?? const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
            itemCount: itemCount,
            separatorBuilder: (context, index) {
              if (scrollDirection == Axis.vertical) {
                return SizedBox(height: 15.h);
              }

              return SizedBox(width: 10.h);
            },
            itemBuilder: (context, index) {
              return _buildShimmerContainer(context);
            },
          ),
        );
      case LayoutType.custom:
      default:
        // Build a regular list view
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          child: _buildShimmerContainer(context),
        );
    }
  }

  Widget _buildShimmerContainer(BuildContext context) {
    return Shimmer.fromColors(
      period: const Duration(milliseconds: 1000),
      baseColor: context.appColors.getShimmerColor(isBaseColor: true),
      highlightColor: context.appColors.getShimmerColor(isBaseColor: false),
      child: CustomContainer(
        padding: const EdgeInsets.all(0),
        margin: const EdgeInsets.all(2),
        height: height,
        width: width,
        child: null,
      ),
    );
  }
}
