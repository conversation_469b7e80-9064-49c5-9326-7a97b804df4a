// ignore_for_file: unreachable_switch_default

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';

class CustomButton extends StatelessWidget {
  const CustomButton({
    super.key,
    required this.onTap,
    this.buttonText,
    required this.buttonState,
    this.textStyle,
    this.loadingIndicator,
    this.buttonStyleType = ButtonStyleType.filled,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  });

  // Named Constructor for Filled Button
  const CustomButton.filled({
    super.key,
    required this.onTap,
    this.buttonText,
    required this.buttonState,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.filled;

// Named Constructor for Outline Button
  const CustomButton.outline({
    super.key,
    required this.onTap,
    this.buttonText,
    required this.buttonState,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.outline;

// Named Constructor for Inline Button
  const CustomButton.inline({
    super.key,
    required this.onTap,
    this.buttonText,
    required this.buttonState,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.inline;

// Named Constructor for Disabled Button
  const CustomButton.disabled({
    super.key,
    this.onTap,
    this.buttonText,
    required this.buttonState,
    this.textStyle,
    this.loadingIndicator,
    this.customBorderSide,
    this.borderRadius = 16,
    this.leadingIcon,
    this.backgroundColor,
    this.rippleColor,
    this.semanticLabel,
    this.width = 200,
    this.height = 30,
    this.padding,
    this.isBorderActive = false,
  }) : buttonStyleType = ButtonStyleType.disabled;

  final Function()? onTap;
  final String? buttonText;
  final ButtonState buttonState;
  final TextStyle? textStyle;
  final Widget? loadingIndicator;
  final ButtonStyleType buttonStyleType;
  final BorderSide? customBorderSide;
  final double borderRadius;
  final Widget? leadingIcon;
  final Color? backgroundColor;
  final Color? rippleColor;
  final String? semanticLabel;
  final double width;
  final double height;
  final EdgeInsetsGeometry? padding;
  final bool isBorderActive;

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel ?? buttonText,
      button: true,
      enabled: buttonState != ButtonState.disabled,
      child: Opacity(
        opacity: buttonState == ButtonState.disabled ? 0.5 : 1,
        child: Align(
          alignment: Alignment.center,
          child: ElevatedButton(
            onPressed: _getOnPressed(),
            style: _getButtonStyle(context: context),
            child: _getButtonContent(context: context),
          ),
        ),
      ),
    );
  }

  // Determine the onPressed behavior based on button state
  Function()? _getOnPressed() {
    switch (buttonState) {
      case ButtonState.normal:
        return onTap;
      case ButtonState.loading:
      case ButtonState.disabled:
        return null;
      default:
        return null;
    }
  }

  // Get button content based on the state (either loading indicator or button text)
  Widget _getButtonContent({
    required BuildContext context,
  }) {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: buttonState == ButtonState.loading
          ? loadingIndicator ?? _defaultLoadingIndicator(context: context)
          : Container(
              width: width.w,
              height: height.h, // Fixed height for the button content
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (leadingIcon != null) ...[
                    leadingIcon!,
                  ],
                  if (leadingIcon != null && buttonText != null)
                    SizedBox(width: 8.w),
                  if (buttonText != null)
                    Flexible(
                      // Ensure text adapts to the available space
                      child: Text(
                        buttonText!,
                        key: ValueKey(buttonText),
                        style: textStyle ?? _getTextStyle(context: context),
                        maxLines: 1, // Prevent text from wrapping
                        overflow: TextOverflow.ellipsis, // Handle text overflow
                        textAlign: TextAlign.center, // Center-align the text
                      ),
                    ),
                ],
              ),
            ),
    );
  }

  // Get button style based on the style type and state
  ButtonStyle _getButtonStyle({
    required BuildContext context,
  }) {
    switch (buttonStyleType) {
      case ButtonStyleType.outline:
        return _getOutlineButtonStyle(context: context);
      case ButtonStyleType.inline:
        return _getInlineButtonStyle(context: context);
      case ButtonStyleType.disabled:
        return _getDisabledButtonStyle(context: context);
      case ButtonStyleType.filled:
        return _getFilledButtonStyle(context: context);
    }
  }

  // Filled button style (default)
  ButtonStyle _getFilledButtonStyle({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      splashFactory: InkSplash.splashFactory,
      shadowColor: appColors.transparent,
      // overlayColor: AppColors.transparent,
      // surfaceTintColor: AppColors.transparent,
      backgroundColor: backgroundColor != null
          ? backgroundColor
          : _getButtonBackgroundColor(context: context),
      disabledBackgroundColor: backgroundColor != null
          ? appColors.transparent
          : _getButtonBackgroundColor(context: context),

      side: customBorderSide ??
          BorderSide(
            color: appColors.transparent,
            width: 2,
          ),
      foregroundColor:
          appColors.textColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
      minimumSize: Size(width.w, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Outline button style (with border)
  ButtonStyle _getOutlineButtonStyle({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return ElevatedButton.styleFrom(
      // elevation: _getButtonElevation(),
      elevation: 0,
      splashFactory: InkSplash.splashFactory,
      shadowColor: appColors.transparent,
      // overlayColor: AppColors.transparent,
      // surfaceTintColor: AppColors.transparent,

      disabledBackgroundColor: backgroundColor ?? appColors.transparent,
      side: customBorderSide ??
          BorderSide(
            color: _getBorderColor(context: context),
            width: 2,
          ),
      backgroundColor: appColors.transparent,
      foregroundColor:
          appColors.textColor, // Ensuring text color is always white

      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      minimumSize: Size(width.w, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // DisAbled button style (with border)
  ButtonStyle _getDisabledButtonStyle({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    Color bgColor = appColors.inactiveColor;

    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      side: customBorderSide ??
          BorderSide(
            color: appColors.transparent,
            width: 2,
          ),
      backgroundColor: backgroundColor ?? bgColor,
      disabledBackgroundColor: backgroundColor ?? bgColor,
      splashFactory: InkSplash.splashFactory,
      shadowColor: appColors.transparent,

      // overlayColor: AppColors.transparent,
      // surfaceTintColor: AppColors.transparent,
      foregroundColor:
          appColors.textColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      minimumSize: Size(width.w, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Inline button style (no background and no border)
  ButtonStyle _getInlineButtonStyle({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return ElevatedButton.styleFrom(
      elevation: _getButtonElevation(),
      backgroundColor: backgroundColor ?? appColors.transparent,
      splashFactory: InkSplash.splashFactory,
      shadowColor: appColors.transparent,
      // overlayColor: AppColors.transparent,
      // surfaceTintColor: AppColors.transparent,
      foregroundColor:
          appColors.textColor, // Ensuring text color is always white
      padding: padding ?? EdgeInsets.symmetric(vertical: 5.h, horizontal: 5.w),
      side: customBorderSide ??
          BorderSide(
            color: _getBorderColor(context: context),
            width: 2,
          ),
      minimumSize: Size(width.w, height.h), // Control size
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
    );
  }

  // Button elevation logic based on the state
  double _getButtonElevation() {
    return (buttonState == ButtonState.normal ||
            buttonState == ButtonState.error)
        ? 4
        : 2;
  }

  // Get button background color based on the state
  Color _getButtonBackgroundColor({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    switch (buttonState) {
      case ButtonState.normal:
        return appColors.buttonColor;
      case ButtonState.loading:
        return appColors.buttonColor;
      case ButtonState.disabled:
        return appColors.inactiveColor;
      case ButtonState.error:
        return appColors.errorColor;
      default:
        return appColors.buttonColor;
    }
  }

  // Default loading indicator widget
  Widget _defaultLoadingIndicator({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // SizedBox(width: 8.w),-
        SizedBox(
          width: 15.w,
          height: 15.h,
          child: CircularProgressIndicator(
            color: appColors.buttonTextColor,
            strokeWidth: 2.w,
          ),
        ),
        // SizedBox(width: 16.w),
        // Text('Processing...', style: _getTextStyle(context: context)),
      ],
    );
  }

  // Define the text style for the button
  TextStyle _getTextStyle({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return TextStyle(
      fontSize: 16.sp,
      color: appColors.buttonTextColor, // Ensure text color is always white
    );
  }

  // Helper method to get the border color based on the isBorderActive flag
  Color _getBorderColor({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return isBorderActive
        ? appColors.primaryColor
        : appColors.borderInactiveColor;
  }
}
