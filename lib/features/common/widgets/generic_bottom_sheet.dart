import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class GenericBottomSheet extends StatelessWidget {
  final String? headerTitleText;
  final List<Widget> formFields;
  // final Function()? onSubmit;
  // final Function()? onCancel;
  // final Function()? onDelete;
  // final String? submitButtonText;
  // final String? cancelButtonText;
  // final String? deleteButtonText;
  final double minHeight;
  final double maxHeight;
  final double initialChildSize;
  final Color? backgroundColor;
  final EdgeInsetsGeometry padding;
  final double borderRadius;

  GenericBottomSheet({
    this.headerTitleText,
    required this.formFields,
    // this.onSubmit,
    // this.onCancel,
    // this.onDelete,
    // required this.submitButtonText,
    // required this.cancelButtonText,
    // required this.deleteButtonText,
    required this.minHeight,
    required this.maxHeight,
    required this.initialChildSize,
    this.backgroundColor,
    required this.padding,
    required this.borderRadius,
  })  : assert(minHeight >= 0 && minHeight <= 1,
            'minHeight must be between 0 and 1'),
        assert(maxHeight >= minHeight && maxHeight <= 1,
            'maxHeight must be >= minHeight and <= 1'),
        assert(initialChildSize >= minHeight && initialChildSize <= maxHeight,
            'initialChildSize must be between minHeight and maxHeight');

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    Color defaultBackgroundColor = appColors.surfaceColor;
    Color dividerColor = appColors.dividerColor;
    return DraggableScrollableSheet(
      minChildSize: minHeight,
      maxChildSize: maxHeight,
      initialChildSize: initialChildSize,
      builder: (context, scrollController) {
        return CustomContainer(
          color: backgroundColor ?? defaultBackgroundColor,
          customBorderRadius: BorderRadius.only(
            topLeft: Radius.circular(borderRadius.r),
            topRight: Radius.circular(borderRadius.r),
          ),
          child: SingleChildScrollView(
            controller: scrollController,
            child: Padding(
              padding: padding,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Center(
                    child: CustomContainer(
                      width: 50.w,
                      height: 5.h,
                      color: dividerColor,
                      borderRadius: 20,
                      child: null,
                    ),
                  ),
                  SizedBox(
                    height: 15.h,
                  ),
                  if (headerTitleText != null)
                    Center(
                      child: Text(
                        headerTitleText!,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ),
                  SizedBox(height: 25.h),
                  ...formFields,
                  SizedBox(height: 16.h),
                  // Row(
                  //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  //   children: [
                  //     if (onDelete != null)
                  //       CustomButton(
                  //         width: 45,
                  //         padding: EdgeInsets.all(3),
                  //         height: 35,
                  //         borderRadius: 60,
                  //         leadingIcon: Align(
                  //           child: Icon(
                  //             Icons.delete,
                  //             color: Colors.red,
                  //           ),
                  //         ),
                  //         buttonState: ButtonState.normal,
                  //         onTap: onDelete,
                  //         buttonText: deleteButtonText,
                  //       ),
                  //     if (onCancel != null)
                  //       CustomButton(
                  //         width: 80,
                  //         height: 30,
                  //         textStyle: Theme.of(context).textTheme.bodyLarge,
                  //         buttonState: ButtonState.normal,
                  //         buttonStyleType: ButtonStyleType.outline,
                  //         onTap: onCancel,
                  //         buttonText: cancelButtonText,
                  //       ),
                  //     if (onSubmit != null)
                  //       CustomButton(
                  //         width: 70,
                  //         buttonState: ButtonState.normal,
                  //         onTap: onSubmit,
                  //         buttonText: submitButtonText,
                  //       ),
                  //   ],
                  // )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
