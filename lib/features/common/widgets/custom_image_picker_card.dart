import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';

class CustomImagePickerCard extends StatelessWidget {
  final Function()? onCamera;
  final Function()? onGallery;
  final bool isLoading;
  final XFile? imageFile;
  final String? imageUrl;
  final String defaultImage;
  final double radius;
  final bool showIcon;
  final bool isProfile;
  final String? userName;

  const CustomImagePickerCard({
    super.key,
    this.onCamera,
    this.onGallery,
    this.isLoading = true,
    this.defaultImage = ImageConstants.man_png,
    this.imageFile,
    this.imageUrl,
    this.radius = 50.0,
    this.showIcon = false,
    this.isProfile = false,
    this.userName,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: isLoading
            ? null
            : () {
                showImagePickerDialog(
                  context: context,
                  onCamera: () => onCamera?.call(),
                  onGallery: () => onGallery?.call(),
                );
              },
        child: CircleAvatar(
          backgroundColor: Colors.grey,
          radius: radius.r,
          child: Stack(
            children: [
              _buildImageContent(context: context),
              if (showIcon) _buildAddPhotoIcon(context: context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImageContent({required BuildContext context}) {
    final appColors = context.appColors;
    if (imageFile != null) {
      return ClipOval(
        child: Image.file(
          File(imageFile!.path),
          fit: BoxFit.cover,
          width: radius * 2.w,
          height: radius * 2.h,
        ),
      );
    } else if (imageUrl != null && imageUrl!.isNotEmpty) {
      return ClipOval(
        child: CachedNetworkImage(
          imageUrl: imageUrl ?? '',
          placeholder: (context, url) => const CircularProgressIndicator(),
          errorWidget: (context, url, error) => _shouldShowInitials()
              ? _buildInitials(context)
              : Icon(
                  Icons.image,
                  size: radius.w,
                  color: appColors.whiteColor,
                ),
          useOldImageOnUrlChange: true,
          fit: BoxFit.cover,
          width: radius * 2.w,
          height: radius * 2.h,
        ),
      );
    } else {
      return _shouldShowInitials()
          ? _buildInitials(context)
          : Icon(
              Icons.image,
              size: radius.w,
              color: appColors.whiteColor,
            );
    }
  }

  bool _shouldShowInitials() {
    return isProfile && userName != null && userName!.trim().isNotEmpty;
  }

  Widget _buildInitials(BuildContext context) {
    final appColors = context.appColors;
    final initials = _getInitials(userName ?? '');

    return Container(
      width: radius * 2.w,
      height: radius * 2.h,
      decoration: BoxDecoration(
        color: appColors.subtextColor.withValues(alpha: 0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          initials,
          style: TextStyle(
            fontSize: radius.w * 0.8,
            color: appColors.whiteColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  String _getInitials(String name) {
    final words = name.trim().split(' ');
    if (words.isEmpty || words[0].isEmpty) {
      return '?';
    }
    if (words.length == 1) {
      return words[0][0].toUpperCase();
    } else {
      if (words[1].isEmpty) {
        return words[0][0].toUpperCase();
      }
      return (words[0][0] + words[1][0]).toUpperCase();
    }
  }

  Widget _buildAddPhotoIcon({required BuildContext context}) {
    final appColors = context.appColors;
    Color backgroundColor = appColors.backgroundColor;
    Color iconColor = appColors.iconColor;

    return Positioned(
      right: 3,
      bottom: 1,
      child: Container(
        padding: EdgeInsets.all(5.w),
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: backgroundColor,
          boxShadow: [
            BoxShadow(
              color: iconColor,
              spreadRadius: 1,
              blurRadius: 1,
            ),
          ],
        ),
        child: Icon(
          Icons.add_a_photo,
          color: iconColor,
        ),
      ),
    );
  }

  void showImagePickerDialog({
    required BuildContext context,
    required Function() onGallery,
    required Function() onCamera,
  }) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog.adaptive(
          content: Material(
            color: Colors.transparent,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 20.h),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  InkWell(
                    onTap: onCamera,
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.photo_camera, size: 50.w),
                          SizedBox(height: 10.w),
                          const Text("Camera"),
                        ],
                      ),
                    ),
                  ),
                  InkWell(
                    onTap: onGallery,
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: EdgeInsets.all(8.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.photo, size: 50.w),
                          SizedBox(height: 10.w),
                          const Text("Gallery"),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
