import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';

class AnimatedAppBar extends StatefulWidget implements PreferredSizeWidget {
  final String title;
  final AnimationDirection animationDirection;
  final Duration duration;
  final double appBarHeight;
  final List<Widget>? actions;
  final PreferredSizeWidget? bottom;
  final TextStyle? titleStyle;

  const AnimatedAppBar({
    super.key,
    required this.title,
    this.actions,
    this.bottom,
    this.animationDirection = AnimationDirection.topToBottom,
    this.duration = const Duration(milliseconds: 800),
    this.appBarHeight = kToolbarHeight,
    this.titleStyle,
  });

  @override
  AnimatedAppBarState createState() => AnimatedAppBarState();

  @override
  Size get preferredSize => Size.fromHeight(appBarHeight);
}

class AnimatedAppBarState extends State<AnimatedAppBar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    Offset beginOffset;

    // Determine the animation direction
    switch (widget.animationDirection) {
      case AnimationDirection.leftToRight:
        beginOffset = const Offset(-1.0, 0.0); // Slide in from left
        break;
      case AnimationDirection.rightToLeft:
        beginOffset = const Offset(1.0, 0.0); // Slide in from right
        break;
      case AnimationDirection.topToBottom:
        beginOffset = const Offset(0.0, -1.0); // Slide in from top
        break;
      case AnimationDirection.bottomToTop:
        beginOffset = const Offset(0.0, 1.0); // Slide in from bottom
        break;
    }

    // Slide and fade animations
    final slideAnimation = Tween<Offset>(
      begin: beginOffset,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    final fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeIn,
      ),
    );

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: AppBar(
          title: Text(
            widget.title,
            style: widget.titleStyle ?? textTheme.titleSmall,
          ),
          actions: widget.actions,
          bottom: widget.bottom,
        ),
      ),
    );
  }
}
