import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';

class CommentTextFiled extends StatelessWidget {
  final TextEditingController? controller;
  final String labelText;
  final TextInputType? keyboardType;
  final int? maxLine;
  final Function(String)? onChanged;
  final AutovalidateMode autovalidateMode;
  final String? Function(String?)? validator;
  final double borderRadius;
  final Color? fillColor;
  final Color? focusedBorderColor;
  final Color? enabledBorderColor;
  final String? leadingImgPath;
  final FocusNode? focusNode;
  final Function()? onComment;
  final Widget? suffixWidget;
  final bool isEnabled;

  const CommentTextFiled({
    this.focusedBorderColor,
    this.focusNode,
    this.suffixWidget,
    this.enabledBorderColor,
    this.leadingImgPath = ImageConstants.comment_png,
    this.fillColor,
    this.controller,
    required this.labelText,
    this.autovalidateMode = AutovalidateMode.onUserInteraction,
    this.maxLine = 1,
    this.keyboardType = TextInputType.text,
    this.borderRadius = 15,
    this.validator,
    this.onChanged,
    required this.onComment,
    this.isEnabled = true, // Default is true
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width.w,
      padding: EdgeInsets.only(top: 5.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius.r),
      ),
      child: TextFormField(
        style: Theme.of(context).textTheme.bodyMedium,
        enableSuggestions: true,
        focusNode: focusNode,
        controller: controller,
        keyboardType: keyboardType,
        maxLines: maxLine,
        onChanged: onChanged,
        autovalidateMode: autovalidateMode,
        validator: validator,
        decoration: InputDecoration(
          hintText: labelText,
          hintStyle: Theme.of(context).textTheme.bodyMedium,
          fillColor: fillColor,
          filled: true,
          suffix: GestureDetector(
            onTap: isEnabled ? onComment : null,
            child: suffixWidget,
          ),
        ),
      ),
    );
  }
}
