import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';

import '../../../app/theme/colors/app_colors.dart';

class CustomListTile extends StatelessWidget {
  const CustomListTile({
    super.key,
    required this.title,
    this.onTap,
    this.widget,
    this.textStyle,
    this.subTextStyle,
  });

  final String title;
  final Function()? onTap;
  final Widget? widget;
  final TextStyle? textStyle;
  final TextStyle? subTextStyle;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: textStyle == null ? textTheme.bodyLarge : textStyle,
        ),
        GestureDetector(
          onTap: () {
            if (onTap != null) {
              onTap!();
            }
          },
          child: widget == null
              ? Row(
                  children: [
                    Text(
                      "View All",
                      style: subTextStyle ??
                          textTheme.bodyLarge?.copyWith(
                            color: appColors.buttonColor,
                          ),
                    ),
                    SizedBox(
                      width: 5.w,
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      color: appColors.primaryColor,
                      size: 20.w,
                    ),
                  ],
                )
              : widget,
        ),
      ],
    );
  }
}
