import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomFooter extends StatelessWidget {
  final String message;
  final double bottomPadding;
  final double topPadding;
  const CustomFooter({
    super.key,
    this.message = "That's all you got!.",
    this.bottomPadding = 30,
    this.topPadding = 20,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: topPadding.h, bottom: bottomPadding.h),
      child: Center(
        child: Text(
          message,
          style: Theme.of(context).textTheme.titleSmall,
        ),
      ),
    );
  }
}
