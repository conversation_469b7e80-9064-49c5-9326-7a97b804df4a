import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';

class FilterBottomSheet extends StatefulWidget {
  final String title;
  final Function(int year, Month month, List<AllUsersModel> selectedUsers)
      onApply;
  final Widget? customContent;
  final EdgeInsetsGeometry? padding;
  final int? initialYear;
  final Month? initialMonth;
  final List<AllUsersModel>? selectedUsers;

  const FilterBottomSheet({
    super.key,
    required this.title,
    required this.onApply,
    this.customContent,
    this.padding,
    this.initialYear,
    this.initialMonth,
    this.selectedUsers,
    TextStyle? headerTitleTextStyle,
  });

  @override
  State<FilterBottomSheet> createState() => _FilterBottomSheetState();
}

class _FilterBottomSheetState extends State<FilterBottomSheet> {
  late int selectedYear;
  late Month selectedMonth;
  late List<AllUsersModel> selectedUsers;
  @override
  void initState() {
    super.initState();
    final DateTime now = DateTime.now();
    selectedYear = widget.initialYear ?? now.year;
    selectedMonth = widget.initialMonth ?? Month.values[now.month - 1];
    selectedUsers = widget.selectedUsers ?? [];
  }

  List<Month> get months => Month.values;
  List<int> get years {
    final currentYear = DateTime.now().year;
    final pastYear = currentYear - 1;
    return [currentYear, pastYear];
  }

  @override
  Widget build(BuildContext context) {
    final texTheme = context.textTheme;
    final appColor = context.appColors;
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    final isHr = UserRolesHelper().canViewAllAttendance(userRoles);
    return Material(
      color: appColor.surfaceColor,
      child: Container(
        padding: widget.padding ??
            EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                height: 5.h,
                width: 50.w,
                decoration: BoxDecoration(
                  color: appColor.dividerColor,
                  borderRadius: BorderRadius.circular(10.r),
                ),
              ),
              SizedBox(height: 10.h),
              Text(
                widget.title,
                style: texTheme.titleLarge?.copyWith(
                  fontSize: 18.sp,
                ),
              ),
              SizedBox(height: 20.h),
              Row(
                children: [
                  Expanded(
                    child: CustomDropDown<int>(
                      items: years,
                      value: selectedYear,
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            selectedYear = value;
                          });
                        }
                      },
                      displayItem: (value) => value?.toString(),
                      labelText: "Year",
                    ),
                  ),
                  SizedBox(width: 10.w),
                  Expanded(
                    child: CustomSelectFieldDisplayer<Month>(
                      bottomSheetTitle: "Select Month",
                      labelText: "Month",
                      leadingIcon: null,
                      selectedItems: [selectedMonth],
                      displayItem: (Month month) => month.name,
                      selectionType: SelectionType.SingleSelection,
                      options: months,
                      showClearButton: false,
                      onSelectionChanged: (List<Month> month) {
                        if (month.isNotEmpty) {
                          setState(() {
                            selectedMonth = month.first;
                          });
                        }
                      },
                    ),
                  )
                ],
              ),
              if (widget.customContent != null) ...[
                SizedBox(height: 10.h),
                widget.customContent!,
              ],
              if (isHr) ...[
                SizedBox(height: 10.h),
                BlocBuilder<UsersBloc, UsersState>(
                  buildWhen: (previous, current) =>
                      current is UsersStateAllUserLoaded ||
                      current is UsersStateAllUserError ||
                      current is UsersStateAllUserLoading,
                  builder: (context, state) {
                    return CustomSelectFieldDisplayer<AllUsersModel>(
                      displayItem: (users) => users.employeeName,
                      displaySubTitle: (users) => users.prefredEmail,
                      displayImage: (users) => users.image,
                      labelText: "Users",
                      selectionType: SelectionType.MultiSelection,
                      selectedItems: selectedUsers,
                      onSelectionChanged: (items) {
                        setState(() {
                          selectedUsers = items.isNotEmpty ? items : [];
                        });
                      },
                      options: context.usersBloc.allUsers,
                      bottomSheetTitle: "Select Users",
                      validator: (value) {
                        if (selectedUsers.isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    );
                  },
                ),
              ],
              SizedBox(height: 30.h),
              CustomButton(
                buttonText: "Apply",
                buttonState: ButtonState.normal,
                width: context.screenWidth,
                onTap: () {
                  Navigator.pop(context);
                  widget.onApply(selectedYear, selectedMonth, selectedUsers);
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
