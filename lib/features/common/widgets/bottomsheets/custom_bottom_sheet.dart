import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

// Reusable Bottom Sheet Widget
class CustomBottomSheet<T> extends StatefulWidget {
  final SelectionType selectionType; // Enum for single or multi-select
  final List<T> options; // The list of options to select from
  final Function(List<T>) onSelectionChanged; // Callback when selection changes
  final List<T> selectedValues; // Preselected values
  final String Function(T)? displayTitle;
  final String Function(T)? displaySubTitle;
  final String? Function(T)? displayImage;
  final String bottomSheetTitle;
  final bool showClearButton;

  const CustomBottomSheet({
    Key? key,
    required this.selectionType,
    required this.displayTitle,
    required this.bottomSheetTitle,
    required this.displayImage,
    this.displaySubTitle,
    required this.options,
    required this.onSelectionChanged,
    this.selectedValues = const [],
    required this.showClearButton,
  }) : super(key: key);

  @override
  _CustomBottomSheetState<T> createState() => _CustomBottomSheetState<T>();
}

class _CustomBottomSheetState<T> extends State<CustomBottomSheet<T>> {
  List<T> _selectedValues = [];
  String _searchQuery = ''; // Search query for filtering options

  @override
  void initState() {
    super.initState();
    _selectedValues =
        List.from(widget.selectedValues); // Initialize with preselected values
  }

  @override
  Widget build(BuildContext context) {
    // Filter options based on the search query
    final filteredOptions = widget.options.where((option) {
      final title =
          widget.displayTitle != null ? widget.displayTitle!(option) : '';
      return title.toLowerCase().contains(_searchQuery.toLowerCase());
    }).toList();

    return Material(
      color: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(),
              const SizedBox(height: 10),
              _buildSearchField(),
              const SizedBox(height: 10),
              _buildOptionsList(filteredOptions),
              const SizedBox(height: 150),
            ],
          ),
        ),
      ),
    );
  }

  // Header with Clear, Title, and Done buttons
  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showClearButton)
          TextButton(
            onPressed: () {
              if (widget.selectionType == SelectionType.SingleSelection) {
                _selectedValues.clear(); // Clear selection for single select
                widget.onSelectionChanged(
                    []); // Notify parent about the clear action
              } else {
                _selectedValues.clear();
                widget.onSelectionChanged(
                    []); // Notify parent about the clear action for multi select
              }
              Navigator.pop(context);
            },
            child: Text('Clear'),
          ),
        Expanded(
          child: Center(
            child: Text(
              widget.bottomSheetTitle,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        TextButton(
          onPressed: () {
            if (widget.selectionType == SelectionType.SingleSelection) {
              widget.onSelectionChanged(
                  _selectedValues.isNotEmpty ? [_selectedValues[0]] : []);
            } else {
              widget.onSelectionChanged(_selectedValues);
            }
            Navigator.pop(context); // Done button action
          },
          child: Text('Done'),
        ),
      ],
    );
  }

  // Search text field for filtering options
  Widget _buildSearchField() {
    return CustomTextField(
      isObsecureText: false,
      onChanged: (value) {
        setState(() {
          _searchQuery = value;
        });
      },
      labelText: "Search....",
    );
  }

  // List of options to select from
  Widget _buildOptionsList(List<T> options) {
    final int listLength = options.length;

    return ConstrainedBox(
      constraints: BoxConstraints(
        // Set a minimum height based on the number of items and a maximum height
        maxHeight: listLength < 4
            ? listLength *
                70.0 // Adjust based on number of items (70.0 is approx height of one item)
            : MediaQuery.of(context).size.height *
                0.7, // Max height for larger lists
      ),
      child: CustomListGridView(
        items: options,
        isLoading: false,
        isEmpty: options.isEmpty,
        layoutType: LayoutType.listView,
        itemBuilder: (BuildContext context, T option) {
          //
          return widget.selectionType == SelectionType.SingleSelection
              ? _buildRadioButton(option)
              : _buildCheckbox(option);
        },
        seperatedWidget: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Divider(),
        ),
        onRefresh: () {
          // TODO: Implement refresh logic
        },
      ),
    );
  }

  // Single selection radio button widget
  Widget _buildRadioButton(T option) {
    return RadioListTile<T>(
      title: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Flexible(
                child: FittedBox(
                  child: Text(
                    widget.displayTitle != null
                        ? widget.displayTitle!(option)
                        : option.toString(),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5.h,
          ),
          if (widget.displaySubTitle != null)
            Text(
              widget.displaySubTitle!(option),
              style: Theme.of(context).textTheme.bodySmall,
            )
        ],
      ),
      value: option,
      groupValue: _selectedValues.isEmpty ? null : _selectedValues[0],
      onChanged: (value) {
        if (value != null) {
          _selectedValues = [value]; // Ensure only one selection
          widget.onSelectionChanged([value]); // Return the single selection
          setState(() {}); // Refresh UI
          Navigator.pop(context);
        }
      },
    );
  }

  Widget _buildCheckbox(T option) {
    return CheckboxListTile(
      dense: true, // Makes the tile more compact
      visualDensity: VisualDensity.compact, // Reduces vertical space
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (widget.displayImage != null) ...[
            CustomImagePickerCard(
              isLoading: true,
              radius: 25,
              showIcon: false,
              isProfile: true,
              userName: widget.displayTitle != null
                  ? widget.displayTitle!(option)
                  : '',
              imageUrl: widget.displayImage != null
                  ? widget.displayImage!(option)
                  : '',
            ),
            SizedBox(
                width:
                    10), // Space between image and text (use 10.w if using ScreenUtil)
          ],
          Expanded(
            // Ensures title and subtitle adjust to available space
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title with overflow handling
                Text(
                  widget.displayTitle != null
                      ? widget.displayTitle!(option)
                      : option.toString(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                  overflow:
                      TextOverflow.ellipsis, // Safeguard against long text
                ),
                // Subtitle with optional visibility and dynamic spacing
                if (widget.displaySubTitle != null) ...[
                  SizedBox(height: 4), // Space between title and subtitle
                  Text(
                    widget.displaySubTitle!(option),
                    style: Theme.of(context).textTheme.bodySmall,
                    overflow:
                        TextOverflow.ellipsis, // Ensure long subtitle fits
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      value: _selectedValues.contains(option),
      controlAffinity: ListTileControlAffinity.leading,
      onChanged: (value) {
        if (value != null) {
          setState(() {
            if (value) {
              _selectedValues.add(option);
            } else {
              _selectedValues.remove(option);
            }
          });
        }
      },
    );
  }
}
