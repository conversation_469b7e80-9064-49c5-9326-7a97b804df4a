import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:rasiin_tasks_app/app/app_logger.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/date_validation_type_enum.dart';

// ignore: must_be_immutable
class CustomDatePickerField extends StatefulWidget {
  final String labelText;
  final IconData? suffixIcon;
  final Function(String)? onDateSelected;
  final DateValidationType dateValidationType;
  final FormFieldValidator<String>? validator;
  final DateTime? initialDate;
  final DateTime? maxDate;
  final DateTime? minDate;
  final bool showDefaultDate;
  String? initialValue;

  CustomDatePickerField({
    super.key,
    this.labelText = "Select day*",
    this.suffixIcon,
    this.onDateSelected,
    this.dateValidationType = DateValidationType.none,
    this.validator,
    this.initialDate,
    this.maxDate,
    this.minDate,
    this.showDefaultDate = false,
    this.initialValue,
  });

  @override
  _CustomDatePickerFieldState createState() => _CustomDatePickerFieldState();
}

class _CustomDatePickerFieldState extends State<CustomDatePickerField> {
  String _selectedDate = '';
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    // Set the initial selected date to today
    if (widget.showDefaultDate) {
      _selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
    }
    if (widget.initialDate != null) {
      _selectedDate = DateFormat('yyyy-MM-dd').format(widget.initialDate!);
    }
    if (widget.initialValue != null) {
      widget.initialValue = _selectedDate;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    try {
      DateTime now = DateTime.now();
      DateTime today = DateTime(now.year, now.month, now.day);

      // Set the first and last dates dynamically based on the validation type
      DateTime firstDate;
      DateTime lastDate;
      DateTime initialDate = now;

      switch (widget.dateValidationType) {
        case DateValidationType.exact:
          _selectedDate = '';
          firstDate = today; // Only today’s date is allowed
          lastDate = today;
          break;
        case DateValidationType.before:
          _selectedDate = '';
          firstDate = DateTime(2000); // Some arbitrary old date
          lastDate = today; // Include today in the selectable dates
          break;
        case DateValidationType.after:
          _selectedDate = '';
          firstDate = today; // From today onwards
          lastDate = DateTime.utc(2040); // Some future date
          break;
        case DateValidationType.none:
          _selectedDate = '';
          firstDate = DateTime(2000); // No restriction on the start date
          lastDate = DateTime.utc(2040); // No restriction on the end date
          break;
      }

      // Adjust initialDate if it's earlier than firstDate
      initialDate = initialDate.isBefore(firstDate) ? firstDate : initialDate;

      DateTime? pickedDate = await showDatePicker(
        context: context,
        firstDate: firstDate,
        lastDate: lastDate,
        initialDate: initialDate, // Use the corrected initial date
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        },
      );

      if (pickedDate != null) {
        // Validation logic remains unchanged
        switch (widget.dateValidationType) {
          case DateValidationType.exact:
            if (pickedDate != today) {
              setState(() {
                _errorMessage = 'Please select today\'s date.';
              });
              return;
            }
            break;
          case DateValidationType.before:
            if (pickedDate.isAfter(today)) {
              setState(() {
                _errorMessage = 'Please select a date before today.';
              });
              return;
            }
            break;
          case DateValidationType.after:
            if (pickedDate.isBefore(today)) {
              setState(() {
                _errorMessage = 'Please select today or a future date.';
              });
              return;
            }
            break;
          case DateValidationType.none:
            break;
        }

        if (widget.initialValue != null) {
          widget.initialValue = _selectedDate;
        }

        _selectedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
        _errorMessage = '';
        widget.onDateSelected?.call(_selectedDate);
        setState(() {});
      }
    } catch (error, stackTrace) {
      setState(() {
        _errorMessage =
            'An error occurred while selecting the date. Please try again.';
      });
      AppLogger()
          .error('Error selecting date', error: error, stackTrace: stackTrace);
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final iconColor = isDarkMode ? Colors.white : Colors.black;
    return GestureDetector(
      onTap: () => _selectDate(context),
      child: FormField<String>(
        validator: widget.validator, // <-- Call validator
        builder: (FormFieldState<String> state) {
          return InputDecorator(
            decoration: InputDecoration(
              labelText: widget.labelText,
              prefixIcon: Padding(
                padding: EdgeInsets.all(10.h), // Add some padding if needed
                child: SvgPicture.asset(
                  ImageConstants.calendar_svg,
                  height: 10.h, // Set height directly here
                  width: 10.w, // Set width directly here
                  fit: BoxFit.contain,
                  colorFilter: ColorFilter.mode(iconColor, BlendMode.srcIn),
                ),
              ),
              suffixIcon:
                  widget.suffixIcon != null ? Icon(widget.suffixIcon) : null,
              fillColor: context.appColors.transparent,
              filled: true,
              errorText: state.hasError
                  ? state.errorText
                  : _errorMessage.isNotEmpty
                      ? _errorMessage
                      : null, // <-- Display form error
            ),
            child: Text(
              _selectedDate.isEmpty ? 'Select Date' : _selectedDate,
              style: Theme.of(context).textTheme.bodyLarge,
            ),
          );
        },
      ),
    );
  }
}
