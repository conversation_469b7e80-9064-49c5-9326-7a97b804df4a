import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';

class WarningDailog extends StatelessWidget {
  final String title;
  final String message;
  final String confitmBtnText;
  final String cancelBtnText;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final bool showConfirmButton;
  final bool showCancelButton;

  const WarningDailog({
    super.key,
    required this.title,
    required this.message,
    required this.confitmBtnText,
    required this.cancelBtnText,
    required this.onConfirm,
    required this.onCancel,
    required this.showConfirmButton,
    required this.showCancelButton,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    Color backgroundColor = appColors.surfaceColor;

    //
    Color warningColor = appColors.warningColor;

    return Dialog(
      backgroundColor: backgroundColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header Container with background color
          Container(
            height: 150.h,
            width: 300.w,
            decoration: BoxDecoration(
              color: warningColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Lottie.asset(
              ImageConstants.warningJson, // Confirmation animation
              height: 100.h,
              width: 150.w,
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(height: 20.h),
          // Title and Message
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Column(
              children: [
                Text(
                  title,
                  style: textTheme.headlineMedium,
                ),
                SizedBox(height: 10.h),
                Text(
                  message,
                  textAlign: TextAlign.center,
                  style: textTheme.bodyLarge,
                ),
              ],
            ),
          ),
          SizedBox(height: 20.h),
          // Confirm and Cancel buttons
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                if (showCancelButton)
                  CustomButton(
                    width: 80,
                    height: 20,
                    buttonState: ButtonState.normal,
                    buttonStyleType: ButtonStyleType.disabled,
                    buttonText: cancelBtnText,
                    onTap: onCancel,
                  ),

                //
                if (showConfirmButton)
                  CustomButton(
                    width: 50,
                    buttonState: ButtonState.normal,
                    buttonStyleType: ButtonStyleType.filled,
                    buttonText: confitmBtnText,
                    onTap: onConfirm,
                  ),
              ],
            ),
          ),

          // some space on bottom
          SizedBox(height: 30.h),
        ],
      ),
    );
  }
}
