// ignore_for_file: unreachable_switch_default
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/dialog_type.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class CustomDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmBtnText;
  final String cancelBtnText;
  final DialogType dialogType;
  final VoidCallback? onConfirm;
  final VoidCallback? onCancel;
  final double? confirmButtonwidth;
  final double? cancelButtonwidth;
  final double confirmButtonHeight;
  final double cancelButtonHeight;

  const CustomDialog({
    super.key,
    required this.title,
    required this.message,
    required this.confirmBtnText,
    required this.cancelBtnText,
    required this.dialogType,
    required this.onConfirm,
    required this.onCancel,
    required this.confirmButtonwidth,
    required this.cancelButtonwidth,
    this.cancelButtonHeight = 30,
    this.confirmButtonHeight = 30,
  });

  bool showCancelButton() =>
      dialogType == DialogType.confirmation ||
      dialogType == DialogType.warning ||
      dialogType == DialogType.info;

  bool showConfirmButton() => dialogType != DialogType.loading;

  String getAssetPath() {
    switch (dialogType) {
      case DialogType.confirmation:
        // return Assets.images.json.confirm;
        return ImageConstants.confirmJson;
      case DialogType.warning:
        // return Assets.images.json.warning;
        return ImageConstants.warningJson;
      case DialogType.info:
        // return Assets.images.json.info;
        return ImageConstants.infoJson;
      case DialogType.error:
        // return Assets.images.json.error;
        return ImageConstants.errorJson;
      case DialogType.success:
        // return Assets.images.json.success;
        return ImageConstants.successJson;
      case DialogType.loading:
        // return Assets.images.json.loading;
        return ImageConstants.loadingJson;
      default:
        // return Assets.images.json.info;
        return ImageConstants.infoJson;
    }
  }

  Color getBgColor({
    required BuildContext context,
  }) {
    final colors = context.appColors;
    switch (dialogType) {
      case DialogType.confirmation:
        return colors.confirmationColor;
      case DialogType.warning:
        return colors.warningColor;
      case DialogType.info:
        return colors.infoColor;
      case DialogType.error:
        return colors.errorColor;
      case DialogType.success:
        return colors.successColor;
      case DialogType.loading:
        return colors.loadingColor;
      default:
        return colors.infoColor;
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final appColors = context.appColors;

    return Dialog(
      backgroundColor: appColors.surfaceColor,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Header Container with background color
                CustomContainer(
                  height: 150.h,
                  backgroundDecoration: BoxDecoration(
                    color: getBgColor(context: context),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(16.r),
                      topRight: Radius.circular(16.r),
                    ),
                  ),
                  child: Lottie.asset(
                    getAssetPath(),
                    height: 100.h,
                    width: 150.w,
                    fit: BoxFit.contain,
                  ),
                ),
                SizedBox(height: 20.h),

                /// Title and Message
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Column(
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium,
                      ),
                      SizedBox(height: 10.h),
                      Text(
                        message,
                        textAlign: TextAlign.center,
                        style: textTheme.bodyMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 20.h),
                // Confirm and Cancel buttons
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      //
                      if (showCancelButton())
                        CustomButton.disabled(
                          buttonState: ButtonState.normal,
                          buttonText: cancelBtnText,
                          width: cancelButtonwidth ?? 70,
                          height: cancelButtonHeight,
                          onTap: onCancel,
                        ),

                      //
                      if (showConfirmButton())
                        CustomButton(
                          buttonState: ButtonState.normal,
                          buttonText: confirmBtnText,
                          width: confirmButtonwidth ?? 80,
                          height: confirmButtonHeight,
                          onTap: onConfirm,
                        ),
                    ],
                  ),
                ),

                // some space on bottom
                SizedBox(height: 30.h),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
