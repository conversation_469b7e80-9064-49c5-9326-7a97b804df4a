import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';

class SuccessDialog extends StatelessWidget {
  final String title;
  final String message;
  final String confirmBtnText;
  final VoidCallback? onPressed;

  const SuccessDialog({
    super.key,
    required this.title,
    required this.message,
    required this.confirmBtnText,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    Color backgroundColor = appColors.surfaceColor;

    //
    Color successColor = appColors.successColor;
    return Dialog(
      backgroundColor: backgroundColor,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            height: 150.h,
            width: 300.w,
            decoration: BoxDecoration(
              color: successColor, // Custom error color
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(16.r),
                topRight: Radius.circular(16.r),
              ),
            ),
            child: Lottie.asset(
              ImageConstants.successJson,
              height: 100.h,
              width: 150.w,
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(height: 20.h),
          Center(
            child: Column(
              children: [
                Text(
                  title,
                  style: textTheme.headlineMedium,
                ),
                SizedBox(height: 10.h),
                Text(
                  message,
                  style: textTheme.bodyLarge,
                ),
                SizedBox(height: 20.h),
                CustomButton(
                  width: 50,
                  buttonState: ButtonState.normal,
                  buttonStyleType: ButtonStyleType.filled,
                  buttonText: confirmBtnText,
                  onTap: onPressed,
                ),

                // some space on bottom
                SizedBox(height: 30.h),
              ],
            ),
          )
        ],
      ),
    );
  }
}
