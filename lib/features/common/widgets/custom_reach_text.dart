import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';

class CustomRichText extends StatelessWidget {
  final String title;
  final String subTitle;
  final Color? titleColor;
  final Color? subTitleColor;
  final double? titleFontSize;
  final double? subTitleFontSize;
  final EdgeInsetsGeometry? padding;
  final TextAlign textAlign;
  final int? subTitleMaxLines;
  final TextOverflow? textOverflow;
  final MainAxisAlignment mainAxisAlignment;

  const CustomRichText({
    super.key,
    required this.title,
    required this.subTitle,
    this.titleColor,
    this.subTitleColor,
    this.padding,
    this.textAlign = TextAlign.start,
    this.titleFontSize,
    this.subTitleFontSize,
    this.subTitleMaxLines = 1,
    this.textOverflow = TextOverflow.ellipsis,
    this.mainAxisAlignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final textColor = appColors.textColor;

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: mainAxisAlignment,
        children: [
          // title
          Text(
            title,
            style: textTheme.bodyMedium?.copyWith(
              color: titleColor ?? textColor,
              fontWeight: FontWeight.bold,
              fontSize: titleFontSize,
            ),
            textAlign: textAlign,
          ),

          // sub title
          Flexible(
            // Use Flexible instead of Expanded
            fit: FlexFit.loose, // Loose fit for less strict constraints
            child: Padding(
              padding: EdgeInsets.only(top: 3.h),
              child: Text(
                subTitle,
                style: textTheme.bodySmall?.copyWith(
                  color: subTitleColor ?? textColor,
                  fontSize: subTitleFontSize,
                ),
                textAlign: textAlign,
                overflow: textOverflow,
                maxLines: subTitleMaxLines,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
