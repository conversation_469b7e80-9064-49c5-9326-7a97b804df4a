import 'package:flutter/material.dart';

showConfirmDialog({
  required BuildContext context,
  required Function()? onSubmit,
  required String title,
  required String subtitle,
}) {
  final textTheme = Theme.of(context).textTheme;
  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: Text(
        title,
        style: textTheme.titleLarge,
      ),
      content: Text(
        subtitle,
        style: textTheme.bodyLarge,
      ),
      actions: [
        TextButton(
          onPressed: () {
            //
            Navigator.pop(context);
          },
          child: Text(
            "Cancel",
            style: textTheme.bodyLarge,
          ),
        ),
        TextButton(
          onPressed: onSubmit,
          child: Text(
            "Yes",
            style: textTheme.bodyLarge,
          ),
        ),
      ],
    ),
  );
}
