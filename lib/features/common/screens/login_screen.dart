import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/core/bloc/authentication%20bloc/authentication_bloc.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/dialog_helper.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final authBloc = context.authenticationBloc;
    final dialogCubit = context.dialogCubit;

    return Scaffold(
      body: BlocListener<DialogCubit, DialogState>(
        listener: listenDialogCubit,
        child: BlocConsumer<AuthenticationBloc, AuthenticationState>(
          listener: (context, state) async {
            print("state: $state");

            // if (state is AuthenticationStateLoading) {
            //   dialogCubit.showLoadingDialog();
            // }

            if (state is AuthenticationStateError) {
              // Close the loading dialog first
              // context.read<DialogCubit>().closeDialog();

              String errorMessage = state.appFailure.getErrorMessage();
              // dialogCubit.showErrorDialog(
              //   message: errorMessage,
              //   enableAutoClose: true,
              // );
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }

            if (state is AuthenticationStateAuthenticated) {
              /// Close the loading dialog
              dialogCubit.closeDialog();

              /// Reset the current user
              context.usersBloc.currentUser = null;

              /// Then navigate to the main screen
              Navigator.pushNamedAndRemoveUntil(
                context,
                ScreenConstants.main,
                (route) => false,
              );
            }
          },
          builder: (context, state) {
            final bool isLoading = state is AuthenticationStateLoading;
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w),
              child: Form(
                key: authBloc.loginFormKey,
                child: AutofillGroup(
                  child: SingleChildScrollView(
                    // reverse: true,
                    child: Column(
                      children: [
                        SizedBox(height: 50.h),
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 200),
                          animationDirection: AnimationDirection.bottomToTop,
                          child: Center(
                            child: Container(
                              height: 120.h,
                              width: 150.w,
                              child: Image.asset(
                                ImageConstants.rasiinLogo_png,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: 20.h),
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 300),
                          animationDirection: AnimationDirection.rightToLeft,
                          child: Text(
                            "Let's sign you in",
                            style: textTheme.titleMedium?.copyWith(
                              color: appColors.primaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 22.sp,
                            ),
                          ),
                        ),
                        SizedBox(height: 5.h),
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 400),
                          animationDirection: AnimationDirection.leftToRight,
                          child: Text(
                            "Enter the details below to continue",
                            style: textTheme.titleSmall,
                          ),
                        ),
                        SizedBox(height: 40.h),
                        // Wrapping the username text field
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 500),
                          animationDirection: AnimationDirection.bottomToTop,
                          child: CustomTextField(
                            controller: authBloc.usernameController,
                            labelText: "Username",
                            keyboardType: TextInputType.name,
                            autofillHints: [AutofillHints.username],
                            isObsecureText: false,
                            faIcon: FaIcon(
                              FontAwesomeIcons.user,
                              color: appColors.subtextColor,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your username';
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 20.h),
                        // Wrapping the password text field
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 600),
                          animationDirection: AnimationDirection.bottomToTop,
                          child: CustomTextField(
                            controller: authBloc.passwordController,
                            faIcon: FaIcon(
                              Icons.lock_open,
                              color: appColors.subtextColor,
                            ),
                            labelText: "Password",
                            keyboardType: TextInputType.visiblePassword,
                            autofillHints: [AutofillHints.password],
                            isObsecureText: true,
                            isPasswordField: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please enter your password';
                              }
                              return null;
                            },
                          ),
                        ),
                        SizedBox(height: 40.h),
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 700),
                          animationDirection: AnimationDirection.bottomToTop,
                          child: CustomButton(
                            buttonState: isLoading
                                ? ButtonState.loading
                                : ButtonState.normal,
                            buttonText: 'Login',
                            onTap: () {
                              if (authBloc.loginFormKey.currentState!
                                  .validate()) {
                                FocusScope.of(context).unfocus();
                                authBloc.add(AuthenticationLoginEvent());
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
