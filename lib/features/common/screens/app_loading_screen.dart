import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';

class AppLoadingScreen extends StatefulWidget {
  const AppLoadingScreen({Key? key}) : super(key: key);

  @override
  _AppLoadingScreenState createState() => _AppLoadingScreenState();
}

class _AppLoadingScreenState extends State<AppLoadingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    // Initialize the AnimationController
    _controller = AnimationController(
      vsync: this,
      // duration: Duration(seconds: 1), // Duration for one full rotation
      duration: Duration(milliseconds: 0),
    )..repeat(); // Repeat the animation indefinitely
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        // gradient: LinearGradient(
        //   begin: Alignment.topLeft,
        //   end: Alignment.bottomRight,
        //   colors: [
        //     // Colors.blue.shade500,
        //     // AppColors.bgColorLight,
        //     // AppColors.bgColorLight,
        //     // Colors.blue.shade400,
        //     // Colors.blue.shade200,
        //   ],
        // ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: AnimatedBuilder(
            animation: _controller,
            builder: (context, child) {
              return Transform.rotate(
                angle:
                    _controller.value * 2 * 3.141592653589793, // Full rotation
                child: child, // The child is the logo
              );
            },
            child: Image.asset(
              ImageConstants.rasiinLogo_png,
              height: 100.h, // Adjust the size as needed
              width: 100.h,
              fit: BoxFit.cover,
              cacheWidth:
                  (100.h * MediaQuery.of(context).devicePixelRatio).round(),
              cacheHeight:
                  (100.h * MediaQuery.of(context).devicePixelRatio).round(),
            ),
          ),
        ),
      ),
    );
  }
}
