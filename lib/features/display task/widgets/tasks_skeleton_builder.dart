import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class TaskSkeletonBuilder extends StatelessWidget {
  const TaskSkeletonBuilder({
    super.key,
    required this.hieght,
    required this.width,
    required this.color,
  });

  final double hieght;
  final double width;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: hieght.h,
      width: width.w,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(16.r),
      ),
    );
  }
}