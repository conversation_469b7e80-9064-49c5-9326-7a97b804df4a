import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/enums/assigned_tasks_status_enum.dart';
import 'package:rasiin_tasks_app/core/enums/filter_field_enum.dart';
import 'package:rasiin_tasks_app/core/enums/filter_operator_enum.dart';
import 'package:rasiin_tasks_app/core/enums/task_priority_enums.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';

class FilterDialog extends StatefulWidget {
  final bool isAssignedTask;
  final Function(FilterField field, FilterOperator operator, String value)
      onFilterApply;

  FilterDialog({required this.isAssignedTask, required this.onFilterApply});

  @override
  _FilterDialogState createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  FilterField selectedField = FilterField.status;
  FilterOperator selectedOperator = FilterOperator.equals;
  String? selectedValue;

  @override
  Widget build(BuildContext context) {
    List<String>? dropdownItems;
    if (selectedField == FilterField.priority) {
      dropdownItems =
          TaskPriority.values.map((prior) => prior.displayName).toList();
    } else if (selectedField == FilterField.status) {
      dropdownItems = widget.isAssignedTask
          ? AssignedTaskStatus.values.map((e) => e.displayName).toList()
          : TaskStatus.values.map((e) => e.displayName).toList();
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: AlertDialog(
        title: Text(widget.isAssignedTask
            ? "Filter Assigned Tasks"
            : "Filter All Tasks"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Searchable Dropdown for selecting the field
            DropdownSearch<FilterField>(
              items: (String filter, LoadProps? props) async {
                // Here, we're returning the list of FilterField values
                return FilterField.values;
              },
              onChanged: (value) {
                setState(() {
                  selectedField = value!;
                });
              },
              selectedItem: selectedField,
              popupProps: PopupProps.menu(
                showSearchBox: true, // Enable search functionality
                showSelectedItems: true, // Show selected items
                searchFieldProps: TextFieldProps(
                  decoration: InputDecoration(
                    labelText: "Search Field",
                  ),
                ),
              ),
              itemAsString: (FilterField? field) => field?.displayName ?? '',
              compareFn: (FilterField? a, FilterField? b) =>
                  a == b, // Compare function to fix the error
            ),

            //!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            // DropdownButtonFormField<FilterField>(
            //   decoration: InputDecoration(labelText: "Filter by"),
            //   value: selectedField,
            //   items: FilterField.values.map((FilterField field) {
            //     return DropdownMenuItem(
            //       value: field,
            //       child: Text(field.displayName),
            //     );
            //   }).toList(),
            //   onChanged: (value) {
            //     setState(() {
            //       selectedField = value!;
            //       selectedValue = null;
            //     });
            //   },
            // ),

            //!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!1
            DropdownButtonFormField<FilterOperator>(
              decoration: InputDecoration(labelText: "Operator"),
              value: selectedOperator,
              items: FilterOperator.values.map((FilterOperator operator) {
                return DropdownMenuItem(
                  value: operator,
                  child: Text(operator.displayName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedOperator = value!;
                });
              },
            ),
            DropdownButtonFormField<String>(
              decoration: InputDecoration(labelText: "Value"),
              value: selectedValue,
              items: dropdownItems?.map((String value) {
                return DropdownMenuItem(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedValue = value!;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: Text("Cancel"),
          ),
          ElevatedButton(
            onPressed: () {
              if (selectedValue != null) {
                widget.onFilterApply(
                    selectedField, selectedOperator, selectedValue!);
                Navigator.of(context).pop();
              }
            },
            child: Text("Apply"),
          ),
        ],
      ),
    );
  }
}
