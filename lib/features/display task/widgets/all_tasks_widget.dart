import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_reach_text.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class AllTasksWidget extends StatelessWidget {
  const AllTasksWidget({
    super.key,
    required this.onTap,
    required this.onComment,
    required this.task,
  });

  final Function()? onTap;
  final Function()? onComment;
  final TaskModel task;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final Color taskStatusColor = appColors.getTaskStatusColor(
      taskStatus: task.taskStatus.toTaskStatus(),
    );

    return CustomContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //
          InkWell(
            splashColor: appColors.transparent,
            onTap: onTap,
            child: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CustomSmallContainer(
                        text: task.prior,
                        verticalPadding: 5,
                      ),

                      CustomSmallContainer(
                        text: task.taskStatus,
                        verticalPadding: 5,
                        textColor: taskStatusColor,
                        backgroundColor: taskStatusColor,
                      ),

                      //!!!!!!!!!!!1
                    ],
                  ),
                  //!111
                  SizedBox(
                    height: 20.h,
                  ),

                  CustomRichText(
                    title: "Project : ",
                    subTitle: task.projectId,
                  ),
                  SizedBox(
                    height: 5.h,
                  ),
                  CustomRichText(
                    title: "Customer :  ",
                    subTitle: task.customerName,
                  ),

                  SizedBox(
                    height: 15.h,
                  ),
                ],
              ),
            ),
          ),
          const Divider(),
          GestureDetector(
            onTap: onComment,
            child: Row(
              children: [
                //
                FaIcon(
                  FontAwesomeIcons.facebookMessenger,
                  size: 15.w,
                ),
                SizedBox(
                  width: 2.w,
                ),
                Text(
                  // "1",
                  task.commentCount.toString(),
                  style: textTheme.bodyLarge,
                ),
                SizedBox(
                  width: 30.w,
                ),
                Expanded(
                  child: Text(
                    "Comments",
                    style: textTheme.bodyLarge,
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    softWrap: true,
                  ),
                ),
                CircleAvatar(
                  radius: 15.w,
                  backgroundColor: appColors.subtextColor,
                  backgroundImage: ResizeImage(
                    AssetImage(ImageConstants.user_png),
                    width: (15.r * 2).toInt(),
                    height: (15.r * 2).toInt(),
                  ),
                ),
                SizedBox(
                  width: 10.w,
                ),

                //
              ],
            ),
          ),
        ],
      ),
    );
  }
}
