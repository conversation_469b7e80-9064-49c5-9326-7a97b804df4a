import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/features/display%20task/widgets/tasks_skeleton_builder.dart';

class TasksSkeletonWidget extends StatelessWidget {
  const TasksSkeletonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      margin: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TaskSkeletonBuilder(
            hieght: 100,
            width: double.infinity,
            color: Colors.grey.withValues(alpha: 0.3),
          ),
        ],
      ),
    );
  }
}
