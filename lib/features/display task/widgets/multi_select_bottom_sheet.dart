import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/add%20task/widgets/custom_project_appbar.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';

class MultiSelectBottomSheet extends StatefulWidget {
  const MultiSelectBottomSheet({
    required this.items,
    required this.initialSelectedItems,
    super.key,
  });

  final List<UserModel> items;
  final List<UserModel>? initialSelectedItems;

  @override
  State<MultiSelectBottomSheet> createState() => _MultiSelectBottomSheetState();
}

class _MultiSelectBottomSheetState extends State<MultiSelectBottomSheet> {
  late List<UserModel>? selectedItems;

  @override
  void initState() {
    super.initState();
    selectedItems = widget.initialSelectedItems;
  }

  void _onChange(UserModel value, bool isSelected) {
    if (isSelected) {
      selectedItems?.add(value);
    } else {
      selectedItems?.remove(value);
    }
  }

  void _submit() {
    Navigator.pop(context, selectedItems);
  }

  void close() {
    setState(() {
      selectedItems = [];
    });
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Material(
      color: appColors.surfaceColor,
      child: Column(
        children: [
          ShowProjectAppBar(
            title: "Select Assign To",
            onClose: close,
            onClear: _submit,
            onChanged: (value) {
              //
            },
          ),
          Expanded(
            child: ListView(
              children: widget.items.map((value) {
                return CheckboxListTile(
                  controlAffinity: ListTileControlAffinity.leading,
                  title: Text(
                    value.employeeName,
                    style: textTheme.bodyLarge,
                  ),
                  value: selectedItems?.contains(value),
                  onChanged: (isChecked) {
                    setState(() {
                      _onChange(value, isChecked!);
                    });
                  },
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
