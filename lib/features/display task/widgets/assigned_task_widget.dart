import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_reach_text.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class AssignedTaskWidget extends StatefulWidget {
  const AssignedTaskWidget({
    super.key,
    required this.onTap,
    required this.onComment,
    required this.assignedModel,
  });

  final Function()? onTap;
  final Function()? onComment;
  final AssignedTasksModel assignedModel;

  @override
  State<AssignedTaskWidget> createState() => _AssignedTaskWidgetState();
}

class _AssignedTaskWidgetState extends State<AssignedTaskWidget> {
  String? currentStatus;

  @override
  void initState() {
    super.initState();
    currentStatus = widget.assignedModel.status;
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final Color taskStatusColor = appColors.getTaskStatusColor(
      taskStatus: widget.assignedModel.status.toTaskStatus(),
    );

    return InkWell(
      splashColor: appColors.transparent,
      onTap: widget.onTap,
      child: CustomContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CustomSmallContainer(
                  text: widget.assignedModel.priority,
                  verticalPadding: 5,
                  // textColor: taskPriorityColor,
                ),
                CustomSmallContainer(
                  text: widget.assignedModel.status,
                  verticalPadding: 5,
                  textColor: taskStatusColor,
                  backgroundColor: taskStatusColor,
                ),
              ],
            ),
            SizedBox(height: 20.h),
            CustomRichText(
              title: "Assigned to : ",
              subTitle: widget.assignedModel.allocatedTo,
            ),
            SizedBox(height: 5.h),
            Row(
              children: [
                Icon(Icons.calendar_month),
                SizedBox(width: 10.w),
                Text(
                  widget.assignedModel.date,
                  style: textTheme.bodyMedium,
                ),
              ],
            ),
            SizedBox(height: 10.h),
            const Divider(),
            GestureDetector(
              onTap: widget.onComment,
              child: Row(
                children: [
                  FaIcon(FontAwesomeIcons.facebookMessenger, size: 15.w),
                  SizedBox(width: 2.w),
                  Text(
                    widget.assignedModel.commentCount.toString(),
                    style: textTheme.bodyLarge,
                  ),
                  SizedBox(width: 30.w),
                  Expanded(
                    child: Text(
                      "Comments",
                      style: textTheme.bodyLarge,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      softWrap: true,
                    ),
                  ),
                  CircleAvatar(
                    radius: 15.w,
                    backgroundImage: AssetImage(ImageConstants.user_png),
                  ),
                  SizedBox(width: 10.w),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
