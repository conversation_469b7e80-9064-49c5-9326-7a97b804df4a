import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/tasks_status_enum.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_reach_text.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class ChangeTaskStatusScreen extends StatefulWidget {
  final AssignedTasksModel assignedModel;

  const ChangeTaskStatusScreen({
    Key? key,
    required this.assignedModel,
  }) : super(key: key);

  @override
  State<ChangeTaskStatusScreen> createState() => _ChangeTaskStatusScreenState();
}

class _ChangeTaskStatusScreenState extends State<ChangeTaskStatusScreen> {
  String? currentStatus;

  @override
  void initState() {
    super.initState();
    currentStatus = widget.assignedModel.status;
  }

  //!!!
  String _parseHtmlString(String htmlString) {
    // Parse the HTML content and extract raw text
    final String parsedString = htmlString.parseHtmlString();

    // Use a regular expression to detect numbered points (like 1., 2., etc.)
    // and insert new lines before each number.
    final formattedString = parsedString.replaceAllMapped(
      RegExp(r'(\d+\.\s)'),
      (Match match) => '\n${match.group(1)}',
    );

    return formattedString.trim();
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final Color taskStatusColor = appColors.getTaskStatusColor(
      taskStatus: widget.assignedModel.status.toTaskStatus(),
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Change Task Status',
          style: textTheme.titleLarge,
        ),
      ),
      body: BlocListener<IssueBloc, IssueState>(
        listener: (context, state) {
          if (state is IssueCreationStateError) {
            String errorMessage = state.appFailure.getErrorMessage();
            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }

          if (state is IssueStateSuccess) {
            context.tasksBloc.add(GetAllAssignedTasksEvent(
              currentUserEmail: context.usersBloc.currentUser?.email ?? '',
            ));

            dialogCubit.showSuccessDialog(
              message: state.message,
              onConfirm: () => Navigator.pop(context),
            );
          }
        },
        child: BlocConsumer<TasksBloc, TasksState>(
          listener: (context, state) {
            if (state is TasksStateError) {
              String errorMessage = state.appFailure.getErrorMessage();

              dialogCubit.showErrorDialog(
                message: errorMessage,
              );
            }

            if (state is TasksStateSuccess) {
              context.tasksBloc.add(GetAllAssignedTasksEvent(
                currentUserEmail: context.usersBloc.currentUser?.email ?? '',
              ));

              dialogCubit.showSuccessDialog(
                message: state.message,
                onConfirm: () => Navigator.pop(context),
              );
            }
          },
          builder: (context, state) {
            bool isLoading = state is TasksStateLoading;
            bool isDisAbled = currentStatus == "Closed";
            final buttonState = isDisAbled
                ? ButtonState.disabled
                : isLoading
                    ? ButtonState.loading
                    : ButtonState.normal;

            return CustomContainer(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 30.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 300),
                      child: CustomRichText(
                        title: "Refrence type : ",
                        subTitle: widget.assignedModel.referenceType,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 500),
                      child: CustomRichText(
                        title: "Refrence name : ",
                        subTitle: widget.assignedModel.referenceName,
                      ),
                    ),
                    SizedBox(height: 25.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 700),
                          animationDirection: AnimationDirection.leftToRight,
                          child: CustomSmallContainer(
                            text: widget.assignedModel.priority,
                          ),
                        ),
                        SizedBox(width: 90.w),
                        AnimatedItemWrapper(
                          delay: Duration(milliseconds: 700),
                          animationDirection: AnimationDirection.rightToLeft,
                          child: CustomSmallContainer(
                            text: currentStatus ?? '',
                            textColor: taskStatusColor,
                            backgroundColor: taskStatusColor,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 20.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 800),
                      child: CustomRichText(
                        title: "Assigned  by : ",
                        subTitle: widget.assignedModel.assignedBy,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 800),
                      child: CustomRichText(
                        title: "Assigned to : ",
                        subTitle: widget.assignedModel.allocatedTo,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 800),
                      child: CustomRichText(
                        title: "Date : ",
                        subTitle: widget.assignedModel.date,
                      ),
                    ),
                    SizedBox(height: 5.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 900),
                      child: CustomRichText(
                        title: "Description : ",
                        subTitle: _parseHtmlString(
                          widget.assignedModel.description,
                        ),
                        subTitleMaxLines: 5,
                      ),
                    ),
                    SizedBox(height: 50.h),
                    AnimatedItemWrapper(
                        delay: Duration(milliseconds: 1100),
                        child: Center(
                          child: CustomButton(
                            buttonState: buttonState,
                            buttonText: isDisAbled ? 'Closed' : 'Complete',
                            onTap: isDisAbled
                                ? null
                                : () {
                                    _showCompletionDialog(context);
                                  },
                          ),
                        )),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  void _showCompletionDialog(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    dialogCubit.showConfirmDialog(
      title: "Complete Task",
      message: "Are you sure you have completed the task?",
      barrierDismissible: true,
      onConfirm: () {
        final referenceType = widget.assignedModel.referenceType;
        final refrenceName = widget.assignedModel.referenceName;
        final status = "Completed";

        // Show loading dialog
        dialogCubit.showLoadingDialog();

        if (referenceType == 'Issue') {
          context.issuesBloc.add(
            ChangeIssueStatus(
              issueId: refrenceName,
              issueStatus: status,
            ),
          );
        } else {
          context.tasksBloc.add(
            ChangeTaskStatus(
              taksId: refrenceName,
              taskStatus: status,
            ),
          );
        }
      },
    );
  }
}
