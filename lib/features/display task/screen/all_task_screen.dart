// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/params/comment_screen_argument_params.dart';
import 'package:rasiin_tasks_app/features/display%20task/widgets/all_tasks_widget.dart';

class AllTasksScreen extends StatefulWidget {
  const AllTasksScreen({super.key});

  @override
  State<AllTasksScreen> createState() => _AllTasksScreenState();
}

class _AllTasksScreenState extends State<AllTasksScreen> {
  @override
  void initState() {
    super.initState();
    if (context.tasksBloc.tasks.isEmpty) {
      context.tasksBloc.add(GetAllTasksEvent(
        userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      ));
    }
  }

  Future<void> onRefresh() async {
    context.tasksBloc.add(GetAllTasksEvent(
      userRoles: context.usersBloc.currentUser?.roleNames ?? [],
    ));
    context.usersBloc.add(GetAllUsersEvent());
  }

  @override
  Widget build(BuildContext context) {
    final tasksBloc = context.tasksBloc;

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: BlocConsumer<TasksBloc, TasksState>(
        listener: (context, state) {
          //
          if (state is TasksStateAllTasksError) {
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: state.appFailure.getErrorMessage(),
            );
          }

          if (state is TasksStateAllTasksLoaded) {
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: "Tasks Loaded SuccessFully",
            );
          }
        },
        builder: (context, state) {
          bool isLoading = state is TasksStateAllTasksLoading;
          bool isEmpty =
              state is TasksStateAllTasksLoaded && state.tasks.isEmpty;
          final List<TaskModel> allTasks = tasksBloc.tasks;

          return CustomListGridView(
            items: allTasks,
            isLoading: isLoading,
            isEmpty: isEmpty,
            layoutType: LayoutType.listView,
            onRefresh: onRefresh,
            itemBuilder: (context, TaskModel task) {
              return AllTasksWidget(
                onTap: () {
                  //
                  Navigator.pushNamed(
                    context,
                    ScreenConstants.assignTask,
                    arguments: task,
                  );
                },
                onComment: () {
                  //
                  Navigator.pushNamed(
                    context,
                    ScreenConstants.comments,
                    arguments: CommentScreenArgumentParams(
                      refrenceType: "Task",
                      taskId: task.taskId,
                    ),
                  );
                },
                task: task,
              );
            },
          );
        },
      ),
    );
  }
}
