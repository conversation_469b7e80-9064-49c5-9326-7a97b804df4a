import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/shape_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/display%20task/screen/all_task_screen.dart';

import 'assigned_tasks_screen.dart';

class DisplayTaskScreen extends StatefulWidget {
  final bool isFloatingNeeded;
  const DisplayTaskScreen({super.key, required this.isFloatingNeeded});

  @override
  _DisplayTaskScreenState createState() => _DisplayTaskScreenState();
}

class _DisplayTaskScreenState extends State<DisplayTaskScreen>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  bool canViewAllTasks = false;

  @override
  void initState() {
    super.initState();
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    canViewAllTasks = UserRolesHelper().canViewAndCreateTasks(userRoles);
    print("canViewAllTasks: $canViewAllTasks");
    print("userRoles: $userRoles");

    if (canViewAllTasks) {
      _tabController = TabController(length: 2, vsync: this);
    }
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  // void _showFilterDialog() {
  //   showDialog(
  //     context: context,
  //     builder: (context) {
  //       return FilterDialog(
  //         isAssignedTask: _tabController?.index == 1,
  //         onFilterApply:
  //             (FilterField field, FilterOperator operator, String value) {
  //           if (_tabController?.index == 0) {
  //             context.tasksBloc.add(FilterAllTasks(
  //               field: field,
  //               operator: operator,
  //               value: value,
  //             ));
  //           } else {
  //             context.tasksBloc.add(FilterAssignedTasks(
  //               field: field,
  //               operator: operator,
  //               value: value,
  //             ));
  //           }
  //         },
  //       );
  //     },
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    return Scaffold(
      floatingActionButton: widget.isFloatingNeeded && canViewAllTasks
          ? CustomFloatingActionButton(
              shapeType: ShapeType.roundedRectangle,
              tooltip: "Create Task",
              foregroundColor: appColors.primaryColor,
              onPressed: () {
                Navigator.pushNamed(context, ScreenConstants.createTask);
              },
            )
          : null,
      appBar: AnimatedAppBar(
        title: canViewAllTasks && _tabController != null
            ? "ALL Tasks"
            : "My Tasks",
        titleStyle: textTheme.titleLarge?.copyWith(
          fontSize: 20.sp,
        ),
        appBarHeight: canViewAllTasks ? 120 : 60,
        // actions: !canViewAllTasks
        //     ? []
        //     : [
        //         Row(
        //           children: [
        //             InkWell(
        //               onTap: _showFilterDialog,
        //               child: Padding(
        //                 padding: EdgeInsets.only(right: 20.w),
        //                 child: Icon(
        //                   Icons.filter_alt_outlined,
        //                   color: appColors.subtextColor,
        //                   size: 30.w,
        //                 ),
        //               ),
        //             ),
        //             InkWell(
        //               onTap: () {
        //                 final userEmail =
        //                     context.usersBloc.currentUser?.email ?? '';
        //                 if (canViewAllTasks && _tabController?.index == 0) {
        //                   context.tasksBloc.add(GetAllTasksEvent());
        //                 } else {
        //                   context.tasksBloc.add(GetAllAssignedTasksEvent(
        //                     currentUserEmail: userEmail,
        //                   ));
        //                 }
        //               },
        //               child: Padding(
        //                 padding: EdgeInsets.only(right: 20.w),
        //                 child: Icon(
        //                   Icons.filter_alt_off_outlined,
        //                   color: appColors.subtextColor,
        //                   size: 30.w,
        //                 ),
        //               ),
        //             ),
        //           ],
        //         ),
        //       ],
        bottom: canViewAllTasks
            ? TabBar(
                controller: _tabController,
                indicatorColor: context.appColors.buttonColor,
                labelColor: context.appColors.buttonColor,
                tabs: [
                  Tab(child: Text('Tasks')),
                  Tab(child: Text('Assigned Tasks')),
                ],
              )
            : null,
      ),
      body: canViewAllTasks
          ? TabBarView(
              controller: _tabController,
              children: [
                AllTasksScreen(),
                AssignedTasksScreen(),
              ],
            )
          : const AssignedTasksScreen(), // Directly show Assigned Tasks if no permission
    );
  }
}
