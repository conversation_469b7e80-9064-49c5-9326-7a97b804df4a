import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/models/task_model.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/date_validation_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class AssignTasksScreen extends StatefulWidget {
  final TaskModel? task;
  const AssignTasksScreen({super.key, this.task});

  @override
  State<AssignTasksScreen> createState() => _AssignTasksScreenState();
}

class _AssignTasksScreenState extends State<AssignTasksScreen> {
  final GlobalKey<FormState> _assignTaskFormKey = GlobalKey<FormState>();
  final TextEditingController _projectController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  String? token;

  @override
  void initState() {
    super.initState();
    _descriptionController.text = _parseHtmlString(widget.task?.desc ?? 'N/A');
    _projectController.text = widget.task?.projectId ?? '';

    context.usersBloc.add(GetAllUsersEvent());
    _getFcmToken();
  }

  String _parseHtmlString(String htmlString) {
    final parsedString = htmlString.parseHtmlString();
    return parsedString.trim();
  }

  String? selectedDate;
  List<AllUsersModel> selectedUsers = [];

  @override
  void dispose() {
    _projectController.dispose();
    _descriptionController.dispose();
    selectedUsers.clear();
    selectedDate = null;
    super.dispose();
  }

  _getFcmToken() async {
    final usertoken = await FirebaseMessaging.instance.getToken() ?? '';
    setState(() {
      token = usertoken;
    });
  }

  List<String> getSelectedUserEmail() {
    return selectedUsers.map((user) => user.prefredEmail).toList();
  }

  List<String> getSelectedUserToken() {
    return selectedUsers
        .map((user) => user.userToken.isNotEmpty ? user.userToken : "N/A")
        .toList();
  }

  List<String> getNotificationTitle() {
    return selectedUsers.map((user) => "Hello ${user.employeeName}").toList();
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    return BlocListener<TasksBloc, TasksState>(
      listener: (context, state) {
        //---------- error --------------------------------

        if (state is TasksStateError) {
          final errorMessage = state.appFailure.getErrorMessage();

          dialogCubit.showErrorDialog(
            message: errorMessage,
          );
        }

        if (state is TasksStateNotificationError) {
          final errorMessage = state.appFailure.getErrorMessage();
          dialogCubit.showErrorDialog(
            message: errorMessage,
            barrierDismissible: false,
            onConfirm: () {
              _descriptionController.clear();
              selectedDate = null;
              _projectController.clear();
              selectedUsers.clear();
              Navigator.pop(context);
            },
          );
        }

        //-------------------- Loading  --------------------------------------------------------------
        if (state is TasksStateLoading ||
            state is TasksStateNotitifcationLoading) {
          dialogCubit.showLoadingDialog();
        }

        //------------------------------ success  ------------------------
        if (state is TasksStateSuccess) {
          context.tasksBloc.add(SendNotificationEvent(
            title: getNotificationTitle(),
            message: "New Task has been assigned to you",
            fcmToken: getSelectedUserToken(),
            userId: context.usersBloc.currentUser?.employeeName ?? '',
          ));
        }

        if (state is TasksStateNotificationSuccess) {
          //
          dialogCubit.showSuccessDialog(
            message: state.message,
            barrierDismissible: false,
            onConfirm: () {
              _descriptionController.clear();
              selectedDate = null;
              _projectController.clear();
              selectedUsers.clear();
              Navigator.pop(context);
            },
          );

          final email = context.usersBloc.currentUser?.email ?? '';
          context.tasksBloc
              .add(GetAllAssignedTasksEvent(currentUserEmail: email));
        }
      },
      child: Scaffold(
        appBar: AnimatedAppBar(
          title: 'Assign Tasks',
        ),
        body: Container(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Form(
            key: _assignTaskFormKey,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 30.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 300),
                    child: CustomTextField(
                      fillColor: Theme.of(context).scaffoldBackgroundColor,
                      isReadOnly: true,
                      controller: _projectController,
                      labelText: "Project",
                      isObsecureText: false,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 20.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 700),
                    child: BlocBuilder<UsersBloc, UsersState>(
                      buildWhen: (previous, current) =>
                          current is UsersStateAllUserLoaded ||
                          current is UsersStateAllUserError ||
                          current is UsersStateAllUserLoading,
                      builder: (context, state) {
                        return CustomSelectFieldDisplayer<AllUsersModel>(
                          displayItem: (users) => users.employeeName,
                          displaySubTitle: (users) => users.prefredEmail,
                          displayImage: (users) => users.image,
                          labelText: "Users",
                          selectionType: SelectionType.MultiSelection,
                          selectedItems: selectedUsers,
                          onSelectionChanged: (items) {
                            setState(() {
                              selectedUsers = items.isNotEmpty ? items : [];
                            });
                          },
                          options: context.usersBloc.allUsers,
                          bottomSheetTitle: "Select Users",
                          validator: (value) {
                            if (selectedUsers.isEmpty) {
                              return 'required!';
                            }
                            return null;
                          },
                        );
                      },
                    ),
                  ),
                  SizedBox(height: 20.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 800),
                    child: CustomDatePickerField(
                      dateValidationType: DateValidationType.exact,
                      onDateSelected: (date) {
                        setState(() {
                          selectedDate = date;
                        });
                      },
                      validator: (value) {
                        if (selectedDate == null || selectedDate!.isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 20.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 900),
                    child: CustomTextField(
                      controller: _descriptionController,
                      labelText: "Description",
                      isObsecureText: false,
                      maxLine: 5,
                      keyboardType: TextInputType.multiline,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 30.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 1200),
                    child: BlocBuilder<TasksBloc, TasksState>(
                      builder: (context, state) {
                        final isLoading = state is TasksStateLoading;
                        final buttonState = isLoading
                            ? ButtonState.loading
                            : ButtonState.normal;
                        return CustomButton(
                          buttonState: buttonState,
                          buttonText: 'Assign',
                          onTap: () {
                            if (_assignTaskFormKey.currentState!.validate()) {
                              context.tasksBloc.add(
                                AssignTaskEvent(
                                  taskId: widget.task?.taskId ?? '',
                                  usersEmail: getSelectedUserEmail(),
                                  description: _descriptionController.text,
                                  assignedDate: selectedDate ?? '',
                                  assignedBy:
                                      context.usersBloc.currentUser?.email ??
                                          '',
                                ),
                              );
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
