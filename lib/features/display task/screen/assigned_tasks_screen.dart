import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/params/comment_screen_argument_params.dart';

import '../widgets/assigned_task_widget.dart';

class AssignedTasksScreen extends StatefulWidget {
  const AssignedTasksScreen({super.key});

  @override
  State<AssignedTasksScreen> createState() => _AssignedTasksScreenState();
}

class _AssignedTasksScreenState extends State<AssignedTasksScreen> {
  @override
  void initState() {
    super.initState();
    final currentUserEmail = context.usersBloc.currentUser?.email;
    if (context.tasksBloc.assignedTasks.isEmpty) {
      context.tasksBloc.add(GetAllAssignedTasksEvent(
        currentUserEmail: currentUserEmail ?? '',
      ));
    }
  }

  Future<void> onRefresh() async {
    context.tasksBloc.add(GetAllAssignedTasksEvent(
      currentUserEmail: context.usersBloc.currentUser?.email ?? '',
    ));
  }

  @override
  Widget build(BuildContext context) {
    final tasksBloc = context.tasksBloc;
    return RefreshIndicator(
      onRefresh: onRefresh,
      child: BlocConsumer<TasksBloc, TasksState>(
        listener: (context, state) {
          //
          if (state is TasksStateAssignedTasksError) {
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: state.appFailure.getErrorMessage(),
            );
          }

          if (state is TasksStateAssignedTasksLoaded) {
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: "Assigned Tasks Loaded SuccessFully",
            );
          }
        },
        builder: (context, state) {
          bool isLoading = state is TasksStateAssignedTasksLoading;
          bool isEmpty = state is TasksStateAssignedTasksLoaded &&
              state.assignedTasks.isEmpty;
          final List<AssignedTasksModel> allAssignedTasks =
              tasksBloc.assignedTasks;

          return CustomListGridView(
            items: allAssignedTasks,
            isLoading: isLoading,
            isEmpty: isEmpty,
            layoutType: LayoutType.listView,
            onRefresh: onRefresh,
            itemBuilder: (context, AssignedTasksModel assignedTask) {
              return AssignedTaskWidget(
                onTap: () {
                  //
                  Navigator.pushNamed(
                    context,
                    ScreenConstants.changeTaskStatus,
                    arguments: assignedTask,
                  );
                },
                onComment: () {
                  Navigator.pushNamed(
                    context,
                    ScreenConstants.comments,
                    arguments: CommentScreenArgumentParams(
                      refrenceType: "ToDo",
                      taskId: assignedTask.name,
                    ),
                  );
                },
                assignedModel: assignedTask,
              );
            },
          );

        },
      ),
    );
  }
}
