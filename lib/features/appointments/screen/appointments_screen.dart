import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/appointment_bloc/appointment_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/appointment_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';

import '../../../core/constants/screen_constants.dart';
import '../../../core/enums/button_state.dart';
import '../../common/widgets/animations/animated_app_bar.dart';
import '../../common/widgets/custom_button.dart';
import '../../common/widgets/custom_container.dart';
import '../../common/widgets/custom_list_grid_view.dart';

class AppointmentsScreen extends StatefulWidget {
  const AppointmentsScreen({Key? key}) : super(key: key);

  @override
  State<AppointmentsScreen> createState() => _AppointmentsScreenState();
}

class _AppointmentsScreenState extends State<AppointmentsScreen> {
  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  void _loadAppointments() {
    final currentUser = context.usersBloc.currentUser;
    final doctorIdentifier =
        currentUser?.email ?? currentUser?.employeeName ?? '';

    context.appointmentBloc.add(LoadAppointmentsEvent(
      doctorIdentifier: doctorIdentifier,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: "Today's Appointments",
      ),
      body: BlocBuilder<AppointmentBloc, AppointmentState>(
        builder: (context, state) {
          final appointments = context.appointmentBloc.appointments;
          final isLoading = state is AppointmentLoading;
          final isEmpty = state is AppointmentLoaded && appointments.isEmpty;

          return Column(
            children: [
              if (state is AppointmentLoaded && appointments.isNotEmpty)
                _buildHeader(appointments),
              Expanded(
                child: CustomListGridView<AppointmentModel>(
                  items: appointments,
                  isLoading: isLoading,
                  isEmpty: isEmpty,
                  layoutType: LayoutType.listView,
                  emtypWidgetMessage:
                      "No appointments today\nYou have no open appointments scheduled for today.",
                  onRefresh: _loadAppointments,
                  itemBuilder: (context, appointment) {
                    return _buildAppointmentCard(appointment);
                  },
                  emptyDataBuilder: () => _buildCustomEmptyState(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHeader(List<AppointmentModel> appointments) {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            "Today's Schedule",
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
            decoration: BoxDecoration(
              color: context.appColors.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Text(
              "${appointments.length} appointments",
              style: context.textTheme.bodySmall?.copyWith(
                color: context.appColors.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today_outlined,
              size: 64.w,
              color: context.appColors.dividerColor,
            ),
            SizedBox(height: 16.h),
            Text(
              "No appointments today",
              style: context.textTheme.titleMedium,
            ),
            SizedBox(height: 8.h),
            Text(
              "You have no open appointments scheduled for today.",
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.appColors.dividerColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppointmentCard(AppointmentModel appointment) {
    return CustomContainer(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 4.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: context.appColors.primaryColor,
                  borderRadius: BorderRadius.circular(2.r),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.patientName,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      "Patient ID: ${appointment.patient}",
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.appColors.dividerColor,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  "Open",
                  style: context.textTheme.bodySmall?.copyWith(
                    color: Colors.green,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              _buildInfoItem(
                icon: Icons.access_time,
                label: "Time",
                value: appointment.formattedTime,
              ),
              SizedBox(width: 24.w),
              _buildInfoItem(
                icon: Icons.attach_money,
                label: "Amount",
                value: appointment.formattedAmount,
              ),
            ],
          ),
          if (appointment.appointmentSource.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildInfoItem(
              icon: Icons.source,
              label: "Source",
              value: appointment.appointmentSource,
            ),
          ],
          SizedBox(height: 16.h),
          // Add Open Encounter Button
          CustomButton(
            buttonState: ButtonState.normal,
            buttonText: 'Open Encounter',
            backgroundColor: context.appColors.primaryColor,
            leadingIcon: Icon(
              Icons.medical_services,
              color: context.appColors.backgroundColor,
            ),
            onTap: () {
              Navigator.pushNamed(
                context,
                ScreenConstants.openEncounter,
                arguments: appointment,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.w,
          color: context.appColors.dividerColor,
        ),
        SizedBox(width: 6.w),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: context.textTheme.bodySmall?.copyWith(
                color: context.appColors.dividerColor,
              ),
            ),
            Text(
              value,
              style: context.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
