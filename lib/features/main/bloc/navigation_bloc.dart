import 'package:flutter_bloc/flutter_bloc.dart';

// Navigation Events
abstract class NavigationEvent {}

class TabChanged extends NavigationEvent {
  final int index;
  TabChanged(this.index);
}

// Navigation States
abstract class NavigationState {
  final int currentIndex;
  NavigationState(this.currentIndex);
}

class NavigationInitial extends NavigationState {
  NavigationInitial(super.currentIndex);
}

// Navigation Bloc
class NavigationBloc extends Bloc<NavigationEvent, NavigationState> {
  NavigationBloc() : super(NavigationInitial(0)) {
    on<TabChanged>((event, emit) {
      emit(NavigationInitial(event.index));
    });
  }
}
