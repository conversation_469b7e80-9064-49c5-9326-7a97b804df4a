import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:popover/popover.dart';
import 'package:rasiin_tasks_app/core/bloc/dialog%20cubit/dialog_cubit.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/shape_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/dialog_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/display%20task/screen/display_task_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/pop_over_menu_item_model.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_screen.dart';
import 'package:rasiin_tasks_app/features/posts/screen/posts_screen.dart';
import 'package:rasiin_tasks_app/features/profile/screen/profile_screen.dart';
import 'package:rasiin_tasks_app/features/main/bloc/navigation_bloc.dart';

class MainScreen extends StatefulWidget {
  final int initialTabIndex;
  const MainScreen({super.key, this.initialTabIndex = 0});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  bool _isPopoverVisible = false;

  final List<Widget> _pages = [
    const HomeScreen(),
    const DisplayTaskScreen(isFloatingNeeded: false),
    const PostsScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;

    return BlocProvider(
      create: (context) =>
          NavigationBloc()..add(TabChanged(widget.initialTabIndex)),
      child: BlocListener<DialogCubit, DialogState>(
        listener: listenDialogCubit,
        child: BlocBuilder<NavigationBloc, NavigationState>(
          builder: (context, state) {
            return Scaffold(
              floatingActionButton: Builder(
                builder: (context) {
                  return BlocBuilder<UsersBloc, UsersState>(
                    buildWhen: (previous, current) {
                      return current is UsersStateDataLoaded ||
                          current is UsersStateDataLoading ||
                          current is UsersStateDataError ||
                          current is UsersStateProfileImageFetched;
                    },
                    builder: (context, state) {
                      return CustomFloatingActionButton(
                        foregroundColor: appColors.backgroundColor,
                        shapeType: ShapeType.circular,
                        onPressed: () {
                          setState(() {
                            _isPopoverVisible = !_isPopoverVisible;
                          });

                          if (_isPopoverVisible) {
                            final currentUser = context.usersBloc.currentUser;
                            final userRoles = currentUser?.roleNames ?? [];

                            // ✅ Get only visible menu items
                            final menuItems = PopOverMenuModel.getMenuItems()
                                .where((item) => item.isVisible(userRoles))
                                .toList();

                            // ✅ Dynamic popover height calculation
                            double popoverHeight = menuItems.length * 63.h;

                            showPopover(
                              context: context,
                              bodyBuilder: (context) =>
                                  PopOverMenuItems(menuItems: menuItems),
                              direction: PopoverDirection.top,
                              width: 220,
                              height: popoverHeight,
                              arrowHeight: 10,
                              arrowWidth: 20,
                              backgroundColor: appColors.transparent,
                            ).then((_) {
                              setState(() {
                                _isPopoverVisible = false;
                              });
                            });
                          }
                        },
                      );
                    },
                  );
                },
              ),
              floatingActionButtonLocation:
                  FloatingActionButtonLocation.centerDocked,
              bottomNavigationBar: BottomAppBar(
                height: 60.h,
                color: appColors.surfaceColor,
                shape: const CircularNotchedRectangle(),
                notchMargin: 6,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _navBarItem(
                        context,
                        icon: ImageConstants.home_svg,
                        index: 0,
                        currentIndex: state.currentIndex,
                      ),
                      _navBarItem(
                        context,
                        icon: ImageConstants.task_png,
                        index: 1,
                        currentIndex: state.currentIndex,
                      ),
                      SizedBox(width: 40.w),
                      _navBarItem(
                        context,
                        icon: ImageConstants.post_png,
                        index: 2,
                        currentIndex: state.currentIndex,
                      ),
                      _navBarItem(
                        context,
                        icon: ImageConstants.user_png,
                        index: 3,
                        currentIndex: state.currentIndex,
                      ),
                    ],
                  ),
                ),
              ),
              body: _pages[state.currentIndex],
            );
          },
        ),
      ),
    );
  }

  Widget _navBarItem(BuildContext context,
      {required String icon, required int index, required int currentIndex}) {
    final appColors = context.appColors;

    return IconButton(
      icon: icon.endsWith('svg')
          ? SvgPicture.asset(
              icon,
              height: 25.h,
              width: 25.w,
              colorFilter: ColorFilter.mode(
                currentIndex == index
                    ? appColors.buttonColor
                    : appColors.subtextColor,
                BlendMode.srcIn,
              ),
            )
          : Image.asset(
              icon,
              height: 25.h,
              width: 25.w,
              color: currentIndex == index
                  ? appColors.buttonColor
                  : appColors.subtextColor,
            ),
      onPressed: () => context.read<NavigationBloc>().add(TabChanged(index)),
    );
  }
}

class PopOverMenuItems extends StatelessWidget {
  final List<PopOverMenuModel> menuItems;

  const PopOverMenuItems({super.key, required this.menuItems});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      spacing: 10.h,
      children: menuItems
          .map((item) => GestureDetector(
                onTap: () {
                  // Close popover before navigating
                  Navigator.of(context).pop();
                  item.onTap(context);
                },
                child: CustomContainer(
                  height: 40.h,
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    children: [
                      Image.asset(item.assetPath, height: 30.h, width: 30.w),
                      SizedBox(width: 10.w),
                      Text(item.title, style: context.textTheme.bodyMedium),
                    ],
                  ),
                ),
              ))
          .toList(),
    );
  }
}
