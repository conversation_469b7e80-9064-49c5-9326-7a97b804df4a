import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/tabs/issue_comments_tab.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/tabs/issue_details_tab.dart';

class IssueDetailScreen extends StatefulWidget {
  final IssueModel? issue;
  const IssueDetailScreen({super.key, required this.issue});

  @override
  State<IssueDetailScreen> createState() => _IssueDetailScreenState();
}

class _IssueDetailScreenState extends State<IssueDetailScreen> {
  @override
  void initState() {
    super.initState();
    context.issuesBloc.add(GetIssueCommentsEvent(
      issueId: widget.issue?.issueId ?? '',
    ));
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AnimatedAppBar(
          appBarHeight: 120,
          title: widget.issue?.issueId ?? '',
          titleStyle: context.theme.textTheme.titleLarge?.copyWith(
            fontSize: 16.sp,
          ),
          actions: [
            BlocBuilder<UsersBloc, UsersState>(
              buildWhen: (previous, current) {
                return current is UsersStateDataLoaded ||
                    current is UsersStateDataLoading ||
                    current is UsersStateDataError ||
                    current is UsersStateProfileImageFetched;
              },
              builder: (context, state) {
                final currentUser = context.usersBloc.currentUser;
                final userRoles = currentUser?.roleNames ?? [];
                final canAssign = UserRolesHelper().canAssignIssues(userRoles);
                if (canAssign) {
                  // return IconButton(
                  //   onPressed: () {
                  //     Navigator.pushNamed(
                  //       context,
                  //       ScreenConstants.assignIssue,
                  //       arguments: issue,
                  //     );
                  //   },
                  //   icon: Icon(Icons.assignment),
                  // );
                  return Padding(
                    padding: EdgeInsets.only(right: 20.w),
                    child: CustomButton(
                      width: 80,
                      height: 20,
                      buttonText: 'Assign',
                      buttonState: ButtonState.normal,
                      buttonStyleType: ButtonStyleType.filled,
                      backgroundColor:
                          context.appColors.buttonColor.withValues(alpha: 0.08),
                      customBorderSide: BorderSide(
                        color: context.appColors.buttonColor,
                        width: 2,
                      ),
                      isBorderActive: true,
                      leadingIcon: Icon(
                        Icons.assignment,
                        color: context.appColors.buttonColor,
                      ),
                      textStyle: context.theme.textTheme.titleMedium,
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          ScreenConstants.assignIssue,
                          arguments: widget.issue,
                        );
                      },
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ],
          bottom: TabBar(
            indicatorColor: context.appColors.buttonColor,
            labelColor: context.appColors.buttonColor,
            tabs: const [
              Tab(text: 'Issue Details'),
              Tab(text: 'Comments'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Details tab content
            IssueDetailTab(issue: widget.issue),
            // Comments tab content
            IssueCommentTab(issue: widget.issue),
          ],
        ),
      ),
    );
  }
}
