import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_tile.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_gridview_item_model.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_gridview_builder_widget.dart';

// ignore: must_be_immutable
class HomeGridview extends StatelessWidget {
  final bool isHeaderNeeded;
  final List<String> userRole;

  HomeGridview({
    super.key,
    this.isHeaderNeeded = true,
    this.userRole = const [],
  });

  /// Filters grid items based on user roles
  List<HomeGridviewItemModel> getFilteredItems() {
    return HomeGridviewItemModel.items
        .where((item) => item.hasAccess(userRole))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final filteredItems = getFilteredItems();

    return Column(
      children: [
        if (isHeaderNeeded)
          CustomListTile(
            title: "What you would like to do?",
            // widget: Icon(
            //   Icons.settings_outlined,
            // ),
            widget: Text(''),
            onTap: () {
              // _showHomeBottomSheet(context);
            },
          ),
        SizedBox(
          height: 20.h,
        ),
        GridView.builder(
          shrinkWrap: true,
          itemCount: filteredItems.length,
          physics: NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            mainAxisSpacing: 10.h,
            crossAxisSpacing: 10.w,
            childAspectRatio: 1,
          ),
          itemBuilder: (context, index) {
            final item = filteredItems[index];
            return HomeGridviewBuilderWidget(
              imgPath: item.imgPath,
              title: item.title,
              onTap: () {
                item.onTap(context);
              },
            );
          },
        ),

        /*

       if (filteredItems.isNotEmpty)
          SizedBox(
            height: 280.h,
            child: GridView.builder(
              shrinkWrap: true,
              itemCount: filteredItems.length,
              scrollDirection: Axis.horizontal,
              physics: AlwaysScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                mainAxisSpacing: 10.h,
                crossAxisSpacing: 10.w,
                // childAspectRatio: 1,
                mainAxisExtent: 140.w,
              ),
              itemBuilder: (context, index) {
                final item = filteredItems[index];
                return HomeGridviewBuilderWidget(
                  imgPath: item.imgPath,
                  title: item.title,
                  onTap: () {
                    item.onTap(context);
                  },
                );
              },
            ),
          ),

        */
      ],
    );
  }

  // _showHomeBottomSheet(BuildContext context) {
  //   final appColors = context.appColors;
  //   showModalBottomSheet(
  //     backgroundColor: appColors.surfaceColor,
  //     context: context,
  //     builder: (context) => Container(
  //       padding: EdgeInsets.symmetric(horizontal: 20.w),
  //       width: double.infinity,
  //       child: SingleChildScrollView(
  //         child: Column(
  //           children: [
  //             //! Bottom sheet header
  //             SizedBox(
  //               height: 10.h,
  //             ),
  //             Container(
  //               height: 5.h,
  //               width: 80.w,
  //               decoration: BoxDecoration(
  //                 color: appColors.subtextColor,
  //                 borderRadius: BorderRadius.circular(5.r),
  //               ),
  //             ),
  //             SizedBox(
  //               height: 10.h,
  //             ),
  //             Text(
  //               "Select Top 6 Shortcuts",
  //               style: Theme.of(context).textTheme.titleSmall,
  //             ),
  //             SizedBox(
  //               height: 0.h,
  //             ),
  //             HomeGridview(
  //               isHeaderNeeded: false,
  //             ),
  //             SizedBox(
  //               height: 40.h,
  //             ),
  //           ],
  //         ),
  //       ),
  //     ),
  //   );
  // }
}
