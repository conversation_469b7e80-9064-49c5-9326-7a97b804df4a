import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/comment_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class IssueCommentTab extends StatefulWidget {
  final IssueModel? issue;
  const IssueCommentTab({
    super.key,
    required this.issue,
  });

  @override
  State<IssueCommentTab> createState() => _IssueCommentTabState();
}

class _IssueCommentTabState extends State<IssueCommentTab> {
  final GlobalKey<FormState> _commentFormKey = GlobalKey<FormState>();
  final TextEditingController _commentController = TextEditingController();

  bool isEnabled = false;

  @override
  void initState() {
    super.initState();
    _commentController.text = '';
    isEnabled = false;
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<IssueBloc, IssueState>(
      buildWhen: (previous, current) {
        return current is IssueCommentsStateLoaded ||
            current is IssueCommentsStateLoading ||
            current is IssueCommentsStateError ||
            current is IssueCommentCreationStateLoading ||
            current is IssueCommentCreationStateSuccess ||
            current is IssueCommentCreationStateError;
      },
      listener: (context, state) {
        if (state is IssueCommentCreationStateSuccess) {
          _commentController.clear();
          SnackBarHelper.showSuccessSnackBar(
            context: context,
            message: state.message,
          );

          context.issuesBloc.add(GetIssueCommentsEvent(
            issueId: widget.issue?.issueId ?? '',
          ));
        }

        if (state is IssueCommentCreationStateError) {
          SnackBarHelper.showErrorSnackBar(
            context: context,
            message: state.appFailure.getErrorMessage(),
          );
        }
      },
      builder: (context, state) {
        final comments = context.issuesBloc.issueComments;
        final isLoading = state is IssueCommentsStateLoading;
        final isEmpty =
            state is IssueCommentsStateLoaded && state.comments.isEmpty;
        return Form(
          key: _commentFormKey,
          child: Column(
            children: [
              Expanded(
                child: RefreshIndicator.adaptive(
                  onRefresh: () async {
                    context.issuesBloc.add(GetIssueCommentsEvent(
                      issueId: widget.issue?.issueId ?? '',
                    ));
                  },
                  child: CustomListGridView(
                    items: comments,
                    isLoading: isLoading,
                    isEmpty: isEmpty,
                    layoutType: LayoutType.listView,
                    onRefresh: () {
                      context.issuesBloc.add(GetIssueCommentsEvent(
                        issueId: widget.issue?.issueId ?? '',
                      ));
                    },
                    itemBuilder: (context, comment) {
                      return CommentCard(comment: comment);
                    },
                    emtypWidgetMessage:
                        "No Comments Yet! Be the first to comment.",
                  ),
                ),
              ),
              _buildCommentInput(
                context: context,
                isLoading: state is IssueCommentCreationStateLoading,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildCommentInput({
    required BuildContext context,
    required bool isLoading,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: context.appColors.cardColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomTextField(
              controller: _commentController,
              hintText: 'Add a comment...',
              borderRadius: 24.r,
              textInputAction: TextInputAction.done,
              onChanged: (value) {
                setState(() {
                  isEnabled = value.trim().isNotEmpty;
                });
              },
            ),
          ),
          SizedBox(width: 12.w),
          if (isLoading)
            SizedBox(
              width: 25.r,
              height: 25.r,
              child: CircularProgressIndicator.adaptive(
                valueColor: AlwaysStoppedAnimation<Color>(
                  context.appColors.buttonColor,
                ),
              ),
            )
          else
            IconButton(
              tooltip: 'Send Comment',
              onPressed: isEnabled
                  ? () {
                      context.issuesBloc.add(AddIssueCommentEvent(
                        issueId: widget.issue?.issueId ?? '',
                        comment: _commentController.text.trim(),
                        userEmail: context.usersBloc.currentUser?.email ?? '',
                      ));
                    }
                  : null,
              icon: Icon(
                semanticLabel: 'Send Comment',
                Icons.send_rounded,
                color: isEnabled
                    ? context.appColors.buttonColor
                    : context.appColors.subtextColor,
                size: 30.r,
              ),
            ),
        ],
      ),
    );
  }
}

class CommentCard extends StatelessWidget {
  final CommentModel comment;
  const CommentCard({
    super.key,
    required this.comment,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final theme = context.theme;
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: appColors.cardColor,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: appColors.blackColor.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomImagePickerCard(
                imageUrl: comment.profileImage,
                radius: 20.r,
                isProfile: true,
                userName: comment.email,
                isLoading: false,
                showIcon: false,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      comment.email,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      comment.getTimeDifference,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: appColors.subtextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Padding(
            padding: EdgeInsets.only(right: 12.w, left: 12.w),
            child: Text(
              comment.formattedComment,
              style: theme.textTheme.bodyLarge,
              // maxLines: 4,
              // overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
