import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/task_priority_enums.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class IssueDetailTab extends StatelessWidget {
  final IssueModel? issue;
  const IssueDetailTab({
    super.key,
    required this.issue,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section with ID and Subject
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        issue?.issueId ?? '',
                        style: context.theme.textTheme.titleLarge?.copyWith(
                          fontSize: 16.sp,
                          color: context.appColors.buttonColor,
                        ),
                      ),
                      Text(
                        issue?.subject ?? '',
                        style: context.theme.textTheme.titleLarge?.copyWith(
                          fontSize: 16.sp,
                        ),
                      ),
                    ],
                  ),
                ),
                if (issue?.priority.isNotEmpty ?? false)
                  CustomSmallContainer(
                    text: issue?.priority ?? 'Low',
                    backgroundColor:
                        _getPriorityColor(context, issue?.priority ?? ''),
                  ),
              ],
            ),
            SizedBox(height: 16.h),

            // Issue Type
            if (issue?.issueType.isNotEmpty ?? false)
              Row(
                children: [
                  Icon(
                    Icons.category_outlined,
                    color: context.appColors.subtextColor,
                    size: 20.r,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'Issue Type:',
                    style: context.theme.textTheme.labelLarge?.copyWith(
                      color: context.appColors.subtextColor,
                    ),
                  ),
                  SizedBox(width: 8.w),
                  CustomSmallContainer(
                    text: issue?.issueType ?? '',
                    backgroundColor:
                        context.appColors.buttonColor.withValues(alpha: 0.1),
                    textColor: context.appColors.buttonColor,
                  ),
                ],
              ),
            SizedBox(height: 16.h),

            // Description
            if (issue?.formattedDescription.isNotEmpty ?? false) ...[
              Text(
                'Description',
                style: context.theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                issue?.formattedDescription ?? '',
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  color: context.appColors.subtextColor,
                ),
              ),
              SizedBox(height: 16.h),
            ],

            Divider(),
            SizedBox(height: 16.h),

            // Raised By
            if (issue?.raisedBy.isNotEmpty ?? false)
              _buildDetailRow(
                context,
                'Raised By',
                issue?.raisedBy ?? 'N/A',
                Icons.person_outline,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          color: context.appColors.subtextColor,
          size: 20.r,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label:',
          style: context.theme.textTheme.labelLarge?.copyWith(
            color: context.appColors.subtextColor,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: context.theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Color _getPriorityColor(BuildContext context, String priority) {
    return context.appColors.getPriorityColor(
      taskPriority: priority.toTaskPriority(),
    );
  }
}
