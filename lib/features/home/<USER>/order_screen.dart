import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/sales_order_model.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

import '../../../core/bloc/sales order bloc/sales_order_bloc.dart';

class OrderScreen extends StatefulWidget {
  const OrderScreen({super.key});

  @override
  State<OrderScreen> createState() => _OrderScreenState();
}

class _OrderScreenState extends State<OrderScreen> {
  @override
  void initState() {
    super.initState();
    _loadSalesOrders();
  }

  void _loadSalesOrders({
    bool forceFetch = false,
  }) {
    final salesOrders = context.salesOrderBloc.salesOrders;
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    if (salesOrders.isEmpty || forceFetch) {
      context.salesOrderBloc.add(GetAllSalesOrders(userRoles: userRoles));
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
      appBar: AnimatedAppBar(title: "Orders"),
      // floatingActionButton: CustomFloatingActionButton(
      //   foregroundColor: appColors.backgroundColor,
      //   onPressed: () {
      // Navigator.pushNamed(context, ScreenConstants.createOrder);
      //   },
      // ),
      body: BlocConsumer<SalesOrderBloc, SalesOrderState>(
        listener: (context, state) {
          if (state is SalesOrderError) {
            final errorMessage = state.appFailure.message;

            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: errorMessage,
            );
          }
        },
        builder: (context, state) {
          final orders = context.salesOrderBloc.salesOrders;
          final isLoading = state is SalesOrderLoading;
          final isEmpty = state is SalesOrderEmpty;

          return RefreshIndicator.adaptive(
            onRefresh: () async {
              _loadSalesOrders(forceFetch: true);
            },
            child: CustomListGridView(
              items: orders,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              onRefresh: () async {
                _loadSalesOrders(forceFetch: true);
              },
              itemBuilder: (context, order) {
                return OrderBuilderWidget(
                  order: order,
                  onTap: () {
                    //
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class OrderBuilderWidget extends StatelessWidget {
  final void Function()? onTap;
  final SalesOrderModel order;

  const OrderBuilderWidget({
    super.key,
    this.onTap,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    String orderDate = order.formattedTransactionDate;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.orderId,
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.primaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        orderDate,
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.subtextColor.withValues(alpha: 0.8),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                CustomSmallContainer(
                  text: order.status,
                ),
              ],
            ),
            SizedBox(height: 15.h),
            _buildOrderRow(context: context),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderRow({
    required BuildContext context,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        _buildOrderFooter(
          title: "Customer name",
          subTitle: order.customerName,
          context: context,
        ),
        _buildOrderFooter(
          title: "Items",
          subTitle: order.totalQty.toString(),
          context: context,
        ),
        _buildOrderFooter(
          title: "Amount",
          subTitle: order.formattedTotal,
          context: context,
        ),
      ],
    );
  }

  Widget _buildOrderFooter({
    required String title,
    required String subTitle,
    required BuildContext context,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return Flexible(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: textTheme.bodySmall?.copyWith(
              color: appColors.subtextColor.withValues(alpha: 0.4),
            ),
            overflow: TextOverflow.ellipsis,
          ),
          Text(
            subTitle,
            style: textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
