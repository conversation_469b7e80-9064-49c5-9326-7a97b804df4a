import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';

class HomeGridviewItemModel {
  final String imgPath;
  final String title;
  final Function(BuildContext) onTap;
  final bool Function(List<String> userRoles) hasAccess;

  HomeGridviewItemModel({
    required this.imgPath,
    required this.title,
    required this.onTap,
    required this.hasAccess,
  });

  static List<HomeGridviewItemModel> items = [
    HomeGridviewItemModel(
      imgPath: ImageConstants.money_png,
      title: "Expense",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.expense),
      // hasAccess: (userRoles) => UserRolesHelper().canViewAllExpenses(userRoles),
      hasAccess: (userRoles) => true,
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.order_png,
      title: "Leave",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.leave),
      // hasAccess: (userRoles) => UserRolesHelper().canViewAllLeaves(userRoles),
      hasAccess: (userRoles) => true,
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.payroll_png,
      title: "Payroll",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.payroll),
      // hasAccess: (userRoles) => UserRolesHelper().canViewAllPayrolls(userRoles),
      hasAccess: (userRoles) => true,
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.attendance_png,
      title: "Attendance",
      onTap: (context) =>
          Navigator.pushNamed(context, ScreenConstants.attendence),
      hasAccess: (userRoles) => false,
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.order_png,
      title: "Orders",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.order),
      hasAccess: (userRoles) =>
          UserRolesHelper().canViewOrdersAndPayments(userRoles),
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.card_png,
      title: "Payment",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.payment),
      hasAccess: (userRoles) =>
          UserRolesHelper().canViewOrdersAndPayments(userRoles),
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.beach_png,
      title: "Visit",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.visit),
      hasAccess: (userRoles) => UserRolesHelper().canViewAllVisits(userRoles),
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.attendance_png,
      title: "Issue",
      onTap: (context) => Navigator.pushNamed(context, ScreenConstants.issue),
      hasAccess: (userRoles) => true,
    ),
    HomeGridviewItemModel(
      imgPath: ImageConstants.appointment_png,
      title: "Appointments",
      onTap: (context) =>
          Navigator.pushNamed(context, ScreenConstants.appointments),
      // hasAccess: (userRoles) => userRoles.contains("Doctor"),
      hasAccess: (userRoles) =>
          UserRolesHelper().canViewAppointments(userRoles),
    ),
  ];
}
