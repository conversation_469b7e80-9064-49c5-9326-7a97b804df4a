import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/visit%20bloc/visit_bloc.dart';
import 'package:rasiin_tasks_app/core/models/visit_type_model.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';

import '../../common/widgets/custom_textfield.dart';

class CreateVisitScreen extends StatefulWidget {
  const CreateVisitScreen({super.key});

  @override
  State<CreateVisitScreen> createState() => _CreateVisitScreenState();
}

class _CreateVisitScreenState extends State<CreateVisitScreen> {
  final GlobalKey<FormState> _createVisitFormKey = GlobalKey<FormState>();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    context.visitBloc.add(GetAllVisitTypesEvent());
  }

  VisitTypeModel? selectedVisitType; // For single selection

  @override
  void dispose() {
    super.dispose();
    _descriptionController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final dialogCubit = context.dialogCubit;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Create Visit',
          style: textTheme.titleSmall,
        ),
      ),
      body: BlocListener<VisitBloc, VisitState>(
        listenWhen: (previousState, currentState) {
          return currentState is VisitErrorState ||
              // currentState is VisitLoadingState ||
              currentState is VisitSuccessState;
        },
        listener: (context, state) async {
          if (state is VisitErrorState) {
            //
            String errorMessage = state.appFailure.getErrorMessage();

            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }
          // if (state is VisitLoadingState) {
          //   DialogManager.showLoadingDialog(
          //     context: context,
          //   );
          // }
          if (state is VisitSuccessState) {
            _descriptionController.clear();
            selectedVisitType = null;
            context.visitBloc.add(GetAllVisitsEvent(
              employee: context.usersBloc.currentUser?.email ?? '',
              userRoles: context.usersBloc.currentUser?.roleNames ?? [],
            ));

            dialogCubit.showSuccessDialog(
              message: state.message,
              barrierDismissible: false,
              onConfirm: () async {
                Navigator.of(context).pop();
                // DialogManager.autoCloseTimer = null;
              },
            );
          }
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
          child: Form(
            key: _createVisitFormKey,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(height: 20.h),
                  BlocBuilder<VisitBloc, VisitState>(
                    builder: (context, state) {
                      return CustomSelectFieldDisplayer<VisitTypeModel>(
                        displayItem: (visitType) => visitType.visitType,
                        labelText: "Visit Type",
                        selectionType: SelectionType.SingleSelection,
                        selectedItems: selectedVisitType != null
                            ? [selectedVisitType!]
                            : [],
                        onSelectionChanged: (items) {
                          setState(() {
                            selectedVisitType =
                                items.isNotEmpty ? items[0] : null;
                          });
                        },
                        options: context.visitBloc.visitTypes,
                        bottomSheetTitle: "Select VisitType",
                        validator: (value) {
                          if (selectedVisitType == null) {
                            return 'required!';
                          }
                          return null;
                        },
                      );
                    },
                  ),
                  SizedBox(
                    height: 20.h,
                  ),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 500),
                    child: CustomTextField(
                      controller: _descriptionController,
                      labelText: "Description",
                      isObsecureText: false,
                      maxLine: 5,
                      keyboardType: TextInputType.multiline,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 30.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 1200),
                    child: BlocBuilder<VisitBloc, VisitState>(
                      builder: (context, state) {
                        return CustomButton(
                          buttonState: state is VisitLoadingState
                              ? ButtonState.loading
                              : ButtonState.normal,
                          buttonText: 'Submit',
                          onTap: () {
                            if (_createVisitFormKey.currentState!.validate()) {
                              //
                              final visitType =
                                  selectedVisitType?.visitType ?? '';
                              final description =
                                  _descriptionController.text.trim();

                              //
                              context.visitBloc.add(
                                CreateVisitEvent(
                                  visitType: visitType,
                                  description: description,
                                ),
                              );

                              dialogCubit.showLoadingDialog();
                            }
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
