import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/date_validation_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class AssignIssueScreen extends StatefulWidget {
  final IssueModel? issue;
  const AssignIssueScreen({super.key, this.issue});

  @override
  State<AssignIssueScreen> createState() => _AssignIssueScreenState();
}

class _AssignIssueScreenState extends State<AssignIssueScreen> {
  final GlobalKey<FormState> _assignIssueFormKey = GlobalKey<FormState>();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _descriptionController.text =
        _parseHtmlString(widget.issue?.description ?? '');
    if (context.usersBloc.allUsers.isEmpty) {
      context.usersBloc.add(GetAllUsersEvent());
    }
    if (context.issuesBloc.issueTypes.isEmpty) {
      context.issuesBloc.add(GetAllIssueTypesEvent());
    }
  }

  //!!!
  String _parseHtmlString(String htmlString) {
    // Parse the HTML content and extract raw text
    final parsedString = htmlString.parseHtmlString();

    // Use a regular expression to detect numbered points (like 1., 2., etc.)
    // and insert new lines before each number.
    final formattedString = parsedString.replaceAllMapped(
      RegExp(r'(\d+\.\s)'),
      (Match match) => '\n${match.group(1)}',
    );

    return formattedString.trim();
  }

  String? selectedDate;
  bool isNotificationLoading = false;
  List<AllUsersModel> selectedUsers = [];

  @override
  void dispose() {
    _descriptionController.dispose();
    super.dispose();
  }

  List<String> getSelectedUserEmail() {
    return selectedUsers.map((user) => user.prefredEmail).toList();
  }

  List<String> getSelectedUserName() {
    return selectedUsers.map((user) => user.employeeName).toList();
  }

  List<String> getSelectedUserToken() {
    return selectedUsers
        .map((user) => user.userToken.isNotEmpty ? user.userToken : "N/A")
        .toList();
  }

  //
  List<String> getNotificationTitle() {
    return selectedUsers.map((user) => "Hello ${user.employeeName}").toList();
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    final email = context.usersBloc.currentUser?.email ?? '';

    return BlocListener<TasksBloc, TasksState>(
      listener: (context, state) {
        //
        if (state is TasksStateNotitifcationLoading) {
          setState(() {
            isNotificationLoading = true;
          });

          dialogCubit.showLoadingDialog();
        }

        if (state is TasksStateNotificationSuccess) {
          setState(() {
            isNotificationLoading = false;
          });

          context.tasksBloc.add(GetAllAssignedTasksEvent(
            currentUserEmail: email,
          ));

          dialogCubit.showSuccessDialog(
            message: state.message,
            barrierDismissible: false,
            onConfirm: () {
              // Clear fields
              _descriptionController.clear();
              selectedUsers.clear();
              selectedDate = null;
              // pop the screen
              Navigator.of(context).pop();
            },
          );
        }
        //
        if (state is TasksStateNotificationError) {
          context.tasksBloc.add(
            GetAllAssignedTasksEvent(
              currentUserEmail: email,
            ),
          );
          setState(() {
            isNotificationLoading = false;
          });
          final errorMessage = state.appFailure.getErrorMessage();
          dialogCubit.showErrorDialog(
            message: errorMessage,
          );
        }
      },
      child: BlocListener<IssueBloc, IssueState>(
        listener: (context, state) {
          // selectedTaskId = context.tasksBloc.assigngTaskId;
          //
          if (state is IssueStateSuccess) {
            dialogCubit.showSuccessDialog(
              message: "Issue has been assigned successfully",
            );
            //!!!!!!!!!
            context.tasksBloc.add(SendNotificationEvent(
              title: getNotificationTitle(),
              message: "New Task has been assigned to you",
              fcmToken: getSelectedUserToken(),
              userId: context.usersBloc.currentUser?.employeeName ?? '',
            ));

            //!!!
          }
          if (state is IssueCreationStateError) {
            final errorMessage = state.appFailure.getErrorMessage();
            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }

          if (state is IssueStateLoading) {
            dialogCubit.showLoadingDialog();
          }

          //!!!!!
        },
        child: Scaffold(
          appBar: AnimatedAppBar(
            title: 'Assign Issue',
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w),
            child: Form(
              key: _assignIssueFormKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20.h),

                    //!
                    BlocBuilder<UsersBloc, UsersState>(
                      buildWhen: (previous, current) =>
                          current is UsersStateAllUserLoaded ||
                          current is UsersStateAllUserError ||
                          current is UsersStateAllUserLoading,
                      builder: (context, state) {
                        return CustomSelectFieldDisplayer<AllUsersModel>(
                          displayItem: (users) => users.employeeName,
                          displaySubTitle: (users) => users.prefredEmail,
                          displayImage: (users) => users.image,
                          labelText: "Users",
                          selectionType: SelectionType.MultiSelection,
                          selectedItems: selectedUsers,
                          onSelectionChanged: (items) {
                            setState(() {
                              selectedUsers = items.isNotEmpty ? items : [];
                            });
                          },
                          options: context.usersBloc.allUsers,
                          bottomSheetTitle: "Select Users",
                          validator: (value) {
                            if (selectedUsers.isEmpty) {
                              return 'required!';
                            }
                            return null;
                          },
                        );
                      },
                    ),

                    SizedBox(height: 20.h),
                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 700),
                      child: CustomDatePickerField(
                        dateValidationType: DateValidationType.exact,
                        onDateSelected: (date) {
                          setState(() {
                            selectedDate = date;
                          });
                        },
                        validator: (value) {
                          if (selectedDate == null || selectedDate!.isEmpty) {
                            return 'required!';
                          }
                          return null;
                        },
                      ),
                    ),
                    SizedBox(height: 20.h),

                    AnimatedItemWrapper(
                      delay: Duration(milliseconds: 900),
                      child: CustomTextField(
                        controller: _descriptionController,
                        labelText: "Description",
                        isObsecureText: false,
                        maxLine: 5,
                        keyboardType: TextInputType.multiline,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'required!';
                          }
                          return null;
                        },
                      ),
                    ),

                    SizedBox(height: 30.h),
                    Center(
                      child: AnimatedItemWrapper(
                        delay: Duration(milliseconds: 1200),
                        child: BlocBuilder<IssueBloc, IssueState>(
                          builder: (context, state) {
                            final isLoading = state is IssueStateLoading ||
                                isNotificationLoading;
                            return CustomButton(
                              buttonText: 'Assign',
                              buttonState: isLoading
                                  ? ButtonState.loading
                                  : ButtonState.normal,
                              onTap: () {
                                if (_assignIssueFormKey.currentState!
                                    .validate()) {
                                  context.issuesBloc.add(
                                    AssignIssueEvent(
                                      issueType: widget.issue?.issueType ?? '',
                                      usersEmail: getSelectedUserEmail(),
                                      description: _descriptionController.text,
                                      assignedDate: selectedDate ?? '',
                                      assignedBy: context
                                              .usersBloc.currentUser?.email ??
                                          '',
                                      issueId: widget.issue?.issueId ?? '',
                                    ),
                                  );
                                }
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
