import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class HomeLeaveBalanceListviewWidegt extends StatelessWidget {
  final String leaveType;
  final int remaining;
  final int used;
  const HomeLeaveBalanceListviewWidegt({
    super.key,
    required this.leaveType,
    required this.remaining,
    required this.used,
  });
  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    // Get color based on leave type
    final color = _getLeaveTypeColor(context: context, type: leaveType);

    return CustomContainer(
      margin: EdgeInsets.all(1.h),
      height: 150.h,
      width: 170.w,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              height: 80.h,
              width: 100.w,
              decoration: BoxDecoration(
                border: Border.all(
                  color: color.withValues(alpha: 0.2),
                  width: 7.w,
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  remaining.toString(),
                  style: textTheme.bodyLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            SizedBox(height: 10.h),
            Text(
              leaveType,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 2.h),
            Text(
              "Used $used",
              style: textTheme.labelLarge?.copyWith(
                color: appColors.subtextColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getLeaveTypeColor({
    required BuildContext context,
    required String type,
  }) {
    final appColors = context.appColors;
    switch (type.toLowerCase()) {
      case 'annual leave':
        return appColors.successColor;
      case 'sick leave':
        return appColors.warningColor;
      case 'casual leave':
        return appColors.infoColor;
      default:
        return appColors.primaryColor;
    }
  }
}
