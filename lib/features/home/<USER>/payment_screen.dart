import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/payment_bloc/payment_bloc.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/payment_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class PaymentScreen extends StatefulWidget {
  const PaymentScreen({super.key});

  @override
  State<PaymentScreen> createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData({
    bool forceFetch = false,
  }) async {
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    final payments = context.paymentBloc.payments;
    if (payments.isEmpty || forceFetch) {
      context.paymentBloc.add(GetAllPaymentsEvent(userRoles: userRoles));
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    // final itemCount = 20;
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Payment Entries',
      ),
      // floatingActionButton: CustomFloatingActionButton(
      //   foregroundColor: appColors.backgroundColor,
      //   onPressed: () {
      //     //
      //     Navigator.pushNamed(context, ScreenConstants.createPayment);
      //   },
      // ),
      body: RefreshIndicator.adaptive(
        onRefresh: () async {
          await _loadData(forceFetch: true);
        },
        child: BlocBuilder<PaymentBloc, PaymentState>(
          buildWhen: (previousState, currentState) {
            return currentState is PaymentsLoaded ||
                currentState is PaymentLoading ||
                currentState is PaymentFailure;
          },
          builder: (context, state) {
            final payments = context.paymentBloc.payments;
            final isLoading = state is PaymentLoading;
            final isEmpty = state is PaymentsLoaded && state.payments.isEmpty;
            return CustomListGridView(
              items: payments,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              itemBuilder: (context, payment) => PaymentBuilderWidget(
                payment: payment,
              ),
              onRefresh: () async {
                await _loadData(forceFetch: true);
              },
            );
          },
        ),
      ),
    );
  }
}

class PaymentBuilderWidget extends StatelessWidget {
  final Function()? onTap;
  final PaymentModel payment;
  const PaymentBuilderWidget({
    super.key,
    this.onTap,
    required this.payment,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(
            horizontal: 15.w, vertical: 15.h), // Padding inside the container

        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        payment.paymentId,
                        style: textTheme.titleLarge?.copyWith(
                          fontSize: 16.sp,
                        ),
                      ),
                      Text(
                        payment.postingDate,
                        style: textTheme.bodyLarge?.copyWith(
                          color: appColors.subtextColor.withValues(alpha: 0.4),
                        ),
                      ),
                    ],
                  ),
                ),
                //
                CustomSmallContainer(
                  text: "Pending",
                ),
              ],
            ),
            SizedBox(
              height: 15.h, // Space between the rows and additional content
            ),
            Row(
              spacing: 5,
              children: [
                Container(
                  width: 10.w,
                  height: 10.h,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: appColors.textColor.withValues(alpha: 0.8),
                  ),
                ),
                Text(
                  // "2024-10-09",
                  payment.type,
                  style: textTheme.bodyLarge?.copyWith(
                    color: appColors.subtextColor,
                    fontWeight: FontWeight.w900,
                  ),
                ),
              ],
            ),
            const Divider(),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Customer name",
                        style: textTheme.titleLarge?.copyWith(
                          fontSize: 16.sp,
                        ),
                      ),
                      Text(
                        payment.customerName,
                        style: textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "Amount",
                      style: textTheme.titleLarge?.copyWith(
                        fontSize: 16.sp,
                      ),
                    ),
                    Text(
                      payment.formattedAmount,
                      style: textTheme.labelLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
