import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/leave%20bloc/leave_bloc.dart';
import 'package:rasiin_tasks_app/core/models/leave_type_model.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/date_range_validation_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_range_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class ApplyLeaveScreen extends StatefulWidget {
  const ApplyLeaveScreen({super.key});

  @override
  State<ApplyLeaveScreen> createState() => _ApplyLeaveScreenState();
}

class _ApplyLeaveScreenState extends State<ApplyLeaveScreen> {
  final _leaveRequestFormKey = GlobalKey<FormState>();
  final _leaveReasonController = TextEditingController();
  String _leaveStartDateController = '';
  String _leaveEndDateController = '';

  LeaveTypeModel? _selectedLeaveType;

  @override
  void initState() {
    super.initState();
    context.read<LeaveBloc>().add(GetAllLeaveTypesEvent());
  }

  @override
  void dispose() {
    _leaveReasonController.dispose();
    super.dispose();
  }

  // Callback to update selected date range
  void _onDateRangeSelected(String startDate, String endDate) {
    // Delay the setState call by a few milliseconds to avoid calling it during the build phase
    Future.delayed(Duration.zero, () {
      setState(() {
        _leaveStartDateController = startDate;
        _leaveEndDateController = endDate;
      });
    });
  }

  void _clearFormFields() {
    _leaveReasonController.clear();
    _leaveStartDateController = '';
    _leaveEndDateController = '';
    _selectedLeaveType = null;
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    return Scaffold(
      appBar: const AnimatedAppBar(
        title: 'New Leave Request',
      ),
      body: BlocListener<LeaveBloc, LeaveState>(
        listener: (context, state) {
          //
          if (state is LeaveCreationErrorState) {
            String errorMessage = state.appFailure.getErrorMessage();

            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }

          if (state is LeaveCreationSuccessState) {
            // clear fields and re-fetch leaves from the server
            _clearFormFields();
            context.leaveBloc.add(GetAllLeavesEvent(
              employee: context.usersBloc.currentUser?.email ?? '',
              userRoles: context.usersBloc.currentUser?.roleNames ?? [],
            ));

            // show success dialog
            dialogCubit.showSuccessDialog(
              message: state.message,
              onConfirm: () => Navigator.pop(context),
            );
          }

          if (state is LeaveCreationLoadingState) {
            dialogCubit.showLoadingDialog();
          }
        },
        child: Form(
          key: _leaveRequestFormKey,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: Column(
              children: [
                SizedBox(height: 20.h),

                // Build the date range picker widget
                _buildDates(),

                SizedBox(height: 20.h),
                // Build the leave type dropdown widget
                BlocBuilder<LeaveBloc, LeaveState>(
                  builder: (context, state) {
                    return _buildLeaveTypeDropdown();
                  },
                ),

                SizedBox(height: 20.h),
                // Build the leave reason text field widget
                _buildLeaveReasonField(),

                SizedBox(height: 20.h),
                // Build the submit button widget
                BlocBuilder<LeaveBloc, LeaveState>(builder: (context, state) {
                  bool isLoading = state is LeaveCreationLoadingState;
                  return _buildSubmitButton(isLoading);
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Method to build the Date Range Picker widget
  Widget _buildDates() {
    return CustomDateRangePickerField(
      dateRangeValidationType: DateRangeValidationType.fromPastMonth,
      
      onDateRangeSelected: _onDateRangeSelected,
    );
  }

  // Method to build the Leave Type dropdown widget
  Widget _buildLeaveTypeDropdown() {
    return CustomDropDown(
      value: _selectedLeaveType,
      items: context.leaveBloc.leaveTypes,
      displayItem: (leaveType) => leaveType?.name,
      labelText: 'Leave type*',
      onChanged: (leaveType) {
        setState(() {
          _selectedLeaveType = leaveType!;
        });
      },
      validator: (value) {
        if (value == null || _selectedLeaveType == null) {
          return 'required*';
        }
        return null;
      },
    );
  }

  // Method to build the Leave Reason text field widget
  Widget _buildLeaveReasonField() {
    return CustomTextField(
      controller: _leaveReasonController,
      labelText: 'Reason for leave*',
      hintText: 'Please enter reason',
      maxLine: 4,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return 'required*';
        }
        return null;
      },
    );
  }

  // Method to build the Submit Button widget
  Widget _buildSubmitButton(bool isLoading) {
    return CustomButton(
      buttonState: isLoading ? ButtonState.loading : ButtonState.normal,
      buttonText: 'Submit',
      onTap: () {
        if (_leaveRequestFormKey.currentState!.validate()) {
          final employeeId = context.usersBloc.currentUser?.employeeId ?? '';
          final leaveType = _selectedLeaveType?.name ?? '';
          final reason = _leaveReasonController.text.trim();
          final fromDate = _leaveStartDateController.toDateTime();
          final toDate = _leaveEndDateController.toDateTime();

          context.leaveBloc.add(CreateLeaveEvent(
            employeeId: employeeId,
            leaveType: leaveType,
            fromDate: fromDate?.toFormattedString() ?? '',
            toDate: toDate?.toFormattedString() ?? '',
            reason: reason,
          ));
        }
      },
    );
  }
}
