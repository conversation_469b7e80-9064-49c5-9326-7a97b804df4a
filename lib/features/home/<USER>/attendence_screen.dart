import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/bloc/attendence%20bloc/attendence_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/all_users_model.dart';
import 'package:rasiin_tasks_app/core/models/attendence_model.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/enums/shape_type_enum.dart';
import 'package:rasiin_tasks_app/core/services/bottom_sheet_services.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/attendence_summary.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_loading_skeleton.dart';

class AttendenceScreen extends StatefulWidget {
  const AttendenceScreen({super.key});

  @override
  State<AttendenceScreen> createState() => _AttendenceScreenState();
}

class _AttendenceScreenState extends State<AttendenceScreen> {
  int selectedYear = DateTime.now().year;
  Month selectedMonth = Month.january;
  List<AllUsersModel> selectedUsers = [];

  List<int> _getYears() {
    //
    DateTime now = DateTime.now();
    final int currentYear = now.year;
    final int pastYear = currentYear - 1;
    return [
      pastYear,
      currentYear,
    ];
  }

  @override
  void initState() {
    super.initState();
    // Get the current date
    DateTime now = DateTime.now();

    // Set the default selected year and month to the current year and month
    selectedYear = now.year;
    selectedMonth = Month.values[now.month - 1]; // Month is 1-based index

    _getYears();

    _loadInitialData();
  }

  Future<void> _loadInitialData() async {
    final attendenceBloc = context.attendenceBloc;

    // if (attendenceBloc.allAttendances.isEmpty) {
    attendenceBloc.add(AttendanceFetchEvent(
      employeeId: context.usersBloc.currentUser?.email ?? '',
      userRoles: context.usersBloc.currentUser?.roleNames ?? [],
    ));
    await attendenceBloc.stream
        .firstWhere((state) => state is AttendanceLoadedState);
    // }

    attendenceBloc.add(AttendanceFilterEvent(
      year: selectedYear,
      month: selectedMonth,
    ));
  }

  Future<void> _refreshAttendance() async {
    final attendenceBloc = context.attendenceBloc;
    attendenceBloc.add(AttendanceFetchEvent(
      employeeId: context.usersBloc.currentUser?.email ?? '',
      userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      forceRefresh: true,
    ));
    await attendenceBloc.stream
        .firstWhere((state) => state is AttendanceLoadedState);

    attendenceBloc.add(AttendanceFilterEvent(
      year: selectedYear,
      month: selectedMonth,
    ));
  }

  @override
  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AttendanceBloc, AttendanceState>(
      listener: (context, state) {},
      builder: (context, state) {
        final attendances = context.attendenceBloc.filteredAttendances;
        bool isLoading = state is AttendanceLoadingState ||
            state is AttendenceFilterLoadingState;
        bool isEmpty =
            state is AttendanceFilteredState && state.attendances.isEmpty;
        final attendenceSummary = AttendanceSummary.fromAttendances(
          attendances,
          year: selectedYear,
          month: selectedMonth.monthNumber,
        );
        return Scaffold(
          appBar: _buildAppBar(),
          body: isLoading
              ? CustomLoadingSkeleton(
                  loadingItems: [
                    SizedBox(height: 20.h),
                    LoadingSkeletonItem(
                      height: 150.h,
                      width: double.infinity,
                      layoutType: LayoutType.custom,
                    ),
                    SizedBox(height: 20.h),
                    LoadingSkeletonItem(
                      height: 60.h,
                      width: double.infinity,
                      layoutType: LayoutType.listView,
                      itemCount: 10,
                    ),
                  ],
                )
              : RefreshIndicator.adaptive(
                  onRefresh: _refreshAttendance,
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 20.h),
                        // Animated Pie Chart Section
                        if (!isEmpty)
                          AnimatedItemWrapper(
                            delay: Duration(milliseconds: 300),
                            animationDirection: AnimationDirection.bottomToTop,
                            child: Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20.w),
                              child: AttendanceSummaryBuilder(
                                attendanceSummary: attendenceSummary,
                              ),
                            ),
                          ),
                        SizedBox(height: 30.h),
                        //!
                        CustomListGridView(
                          items: attendances,
                          isLoading: isLoading,
                          isEmpty: isEmpty,
                          layoutType: LayoutType.listView,
                          onRefresh: _refreshAttendance,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, AttendenceModel attendance) {
                            //
                            return AttendanceItemBuilder(
                                attendence: attendance);
                          },
                          emptyDataBuilder: () {
                            return _buildEmptyAttendance(context);
                          },
                        ),

                        // some space on bottom
                        SizedBox(height: 100.h),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildEmptyAttendance(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return CustomContainer(
      height: 100.h,
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      backgroundDecoration: BoxDecoration(
        color: appColors.cardColor,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: appColors.textColor.withValues(alpha: 0.1),
            blurRadius: 10.r,
            offset: Offset(0, 3.r),
          ),
        ],
      ),
      child: Center(
        child: Text(
          "No Data Available.",
          textAlign: TextAlign.center,
          style: textTheme.bodyLarge?.copyWith(
            color: appColors.subtextColor,
          ),
        ),
      ),
    );
  }

  _buildAppBar() {
    return AnimatedAppBar(
      appBarHeight: 110,
      title: "Attendence",
      // bottom: PreferredSize(
      //   preferredSize: Size.fromHeight(50.h),
      //   child: Padding(
      //     padding: EdgeInsets.symmetric(horizontal: 20.w),
      //     child: _buildDropdownRow(),
      //   ),
      // ),
      actions: [
        IconButton(
          icon: Icon(
            FontAwesomeIcons.filter,
          ),
          onPressed: () async {
            // show filter bottom sheet
            await _buildFilterBottomSheet();
          },
        )
      ],
    );
  }

  Future<void> _buildFilterBottomSheet() async {
    await BottomSheetServices.showFilterBottomSheet(
      context,
      headerTitleText: "Filter Attendance",
      onApply: (year, month, selectedUsers) {
        setState(() {
          selectedYear = year;
          selectedMonth = month;
          this.selectedUsers = selectedUsers;
        });

        //
        context.attendenceBloc.add(AttendanceFilterEvent(
          year: year,
          month: month,
          employeeIds: selectedUsers.map((e) => e.employeeName).toList(),
        ));
      },
      initialYear: selectedYear,
      initialMonth: selectedMonth,
    );
  }
}

class AttendanceItemBuilder extends StatelessWidget {
  final AttendenceModel attendence;
  const AttendanceItemBuilder({
    super.key,
    required this.attendence,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final String? formattedDate = attendence.formattedDate;
    final userRole = context.usersBloc.currentUser?.roleNames ?? [];
    final canViewAllAttendances =
        UserRolesHelper().canViewAllAttendance(userRole);
    return CustomContainer(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                formattedDate ?? '--',
                style: textTheme.bodyMedium,
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: appColors.subtextColor.withValues(alpha: 0.5),
                size: 15.w,
              ),
            ],
          ),
          if (canViewAllAttendances) ...[
            SizedBox(height: 5.h),
            RichText(
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              text: TextSpan(
                children: [
                  TextSpan(text: "Employee: ", style: textTheme.bodySmall),
                  TextSpan(
                    text: attendence.employeeName,
                    style: textTheme.bodyLarge,
                  ),
                ],
              ),
            ),
            SizedBox(height: 5.h),
          ],
          SizedBox(height: 10.h),
          Row(
            children: [
              _buildAttendanceDetailItem(
                context: context,
                label: "Check In",
                value: attendence.inTime,
              ),
              _buildDivider(context: context),
              _buildAttendanceDetailItem(
                context: context,
                label: "Check Out",
                value: attendence.outTime,
              ),
              _buildDivider(context: context),
              _buildAttendanceDetailItem(
                context: context,
                label: "Work Duration",
                value: "0.0",
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDivider({
    required BuildContext context,
  }) {
    final appColors = context.appColors;
    return Container(
      height: 30.h,
      width: 2.w,
      color: appColors.subtextColor.withValues(alpha: 0.1),
    );
  }

  Widget _buildAttendanceDetailItem({
    required BuildContext context,
    required String label,
    required String value,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return Expanded(
      child: Column(
        children: [
          Text(
            label,
            style: textTheme.bodySmall?.copyWith(
              color: appColors.subtextColor.withValues(alpha: 0.9),
            ),
          ),
          SizedBox(height: 5.h),
          Text(
            value,
            style: textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

class AttendanceSummaryBuilder extends StatelessWidget {
  final AttendanceSummary attendanceSummary;
  const AttendanceSummaryBuilder({
    super.key,
    required this.attendanceSummary,
  });

  @override
  Widget build(BuildContext context) {
    return CustomContainer(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildDynamicPieChart(
            context: context,
            attendanceSummary: attendanceSummary,
          ),
          SizedBox(width: 20.w),
          Flexible(
            child: _buildLegend(
              context: context,
              attendanceSummary: attendanceSummary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDynamicPieChart({
    required BuildContext context,
    required AttendanceSummary attendanceSummary,
  }) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    // Percentages for each category
    final presentPercent = attendanceSummary.presentPercentage;
    final absentPercent = attendanceSummary.absentPercentage;
    final onLeavePercent = attendanceSummary.onLeavePercentage;
    final wfhPercent = attendanceSummary.workFromHomePercentage;
    final halfDayPercent = attendanceSummary.halfDayPercentage;

    // Calculate remaining days (if any)
    final remainingPercent = attendanceSummary.totalUnrecordedDays /
        attendanceSummary.totalDays.toDouble();

    return SizedBox(
      height: 150.h,
      width: 150.w,
      child: Stack(
        alignment: Alignment.center,
        children: [
          PieChart(
            PieChartData(
              sectionsSpace: 2,
              centerSpaceRadius: 45.r,
              sections: [
                if (presentPercent > 0)
                  _buildChartSection(
                    percent: presentPercent,
                    color: appColors.successColor,
                    label: "Present",
                  ),
                if (absentPercent > 0)
                  _buildChartSection(
                    percent: absentPercent,
                    color: appColors.errorColor,
                    label: "Absent",
                  ),
                if (onLeavePercent > 0)
                  _buildChartSection(
                    percent: onLeavePercent,
                    color: appColors.warningColor,
                    label: "On Leave",
                  ),
                if (wfhPercent > 0)
                  _buildChartSection(
                    percent: wfhPercent,
                    color: appColors.infoColor,
                    label: "Work From Home",
                  ),
                if (halfDayPercent > 0)
                  _buildChartSection(
                    percent: halfDayPercent,
                    color: appColors.primaryColor,
                    label: "Half Day",
                  ),
                if (remainingPercent > 0)
                  _buildChartSection(
                    percent: remainingPercent,
                    color: appColors.subtextColor.withValues(alpha: 0.1),
                    label: "Unrecorded",
                  ),
              ],
              startDegreeOffset: -90, // Start from top
            ),
          ),
          Positioned(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${attendanceSummary.totalActualDays}',
                  style: textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  'Of ${attendanceSummary.totalDays} days',
                  style: textTheme.bodySmall?.copyWith(
                    color: appColors.subtextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  PieChartSectionData _buildChartSection({
    required double percent,
    required Color color,
    required String label,
  }) {
    return PieChartSectionData(
      color: color,
      value: percent * 100,
      radius: 20.r,
      showTitle: false,
    );
  }

  Widget _buildLegend({
    required BuildContext context,
    required AttendanceSummary attendanceSummary,
  }) {
    final appColors = context.appColors;
    final unrecordedDays =
        attendanceSummary.totalDays - attendanceSummary.totalActualDays;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (attendanceSummary.presentDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.successColor,
            label: "Present",
          ),
          SizedBox(height: 12.h),
        ],
        if (attendanceSummary.absentDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.errorColor,
            label: "Absent",
          ),
          SizedBox(height: 12.h),
        ],
        if (attendanceSummary.onLeaveDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.warningColor,
            label: "On Leave",
          ),
          SizedBox(height: 12.h),
        ],
        if (attendanceSummary.workFromHomeDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.infoColor,
            label: "Work From Home",
          ),
          SizedBox(height: 12.h),
        ],
        if (attendanceSummary.halfDayDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.primaryColor,
            label: "Half Day",
          ),
          SizedBox(height: 12.h),
        ],
        if (unrecordedDays > 0) ...[
          _buildLegendItem(
            context: context,
            color: appColors.subtextColor.withValues(alpha: 0.3),
            label: "Unrecorded",
          ),
          SizedBox(height: 12.h),
        ],
      ],
    );
  }

  Widget _buildLegendItem({
    required BuildContext context,
    required Color color,
    required String label,
  }) {
    final textTheme = context.textTheme;

    return Row(
      children: [
        CustomContainer(
          height: 12.h,
          width: 12.w,
          shapeType: ShapeType.circular,
          color: color,
          margin: EdgeInsets.only(right: 8.w),
        ),
        Text(
          label,
          style: textTheme.bodySmall,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }
}
