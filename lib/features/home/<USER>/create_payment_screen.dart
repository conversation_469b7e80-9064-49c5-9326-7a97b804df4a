import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class CreatePaymentScreen extends StatefulWidget {
  const CreatePaymentScreen({super.key});

  @override
  State<CreatePaymentScreen> createState() => _CreatePaymentScreenState();
}

class _CreatePaymentScreenState extends State<CreatePaymentScreen> {
  int currentStepIndex = 0;
  bool isCompleted = false;

  final _formKeyStep1 = GlobalKey<FormState>();
  final _formKeyStep2 = GlobalKey<FormState>();

  // Controller for text fields
  final nameController = TextEditingController();
  final descriptionController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'New Payment Entry',
          style: textTheme.titleSmall,
        ),
      ),
      body: isCompleted
          ? _buildCompletted()
          : Stepper(
              type: StepperType.horizontal,
              currentStep: currentStepIndex,
              onStepCancel: () {
                if (currentStepIndex > 0) {
                  setState(() {
                    currentStepIndex -= 1;
                  });
                }
              },
              onStepContinue: () {
                if (currentStepIndex == 0) {
                  // Validate Step 1 before proceeding
                  if (_formKeyStep1.currentState!.validate()) {
                    setState(() {
                      currentStepIndex += 1;
                    });
                  }
                } else if (currentStepIndex == 1) {
                  // Validate Step 2 if necessary
                  if (_formKeyStep2.currentState!.validate()) {
                    setState(() {
                      isCompleted = true;
                    });
                    // Submit the form or proceed to the next step if any
                  }
                }
              },
              // onStepTapped: (index) {
              //   setState(() {
              //     currentStepIndex = index;
              //   });
              // },
              controlsBuilder: (context, details) {
                return Padding(
                  padding: EdgeInsets.only(top: 20.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (currentStepIndex != 0)
                        Expanded(
                          child: ElevatedButton(
                            onPressed: details.onStepCancel,
                            child: Text('Back'),
                          ),
                        ),
                      SizedBox(
                        width: 25.w,
                      ),
                      Expanded(
                        child: CustomButton(
                          buttonText: currentStepIndex == 1 ? 'Submit' : 'Next',
                          buttonState: ButtonState.normal,
                          onTap: details.onStepContinue,
                        ),
                      ),
                    ],
                  ),
                );
              },
              steps: getSteppers(),
            ),
    );
  }

  List<Step> getSteppers() => [
        Step(
          state: currentStepIndex > 0 ? StepState.complete : StepState.indexed,
          isActive: currentStepIndex == 0,
          title: Text('1'),
          content: Form(
            key: _formKeyStep1,
            child: Column(
              children: [
                CustomTextField(
                  labelText: 'Name',
                  isObsecureText: false,
                  controller: nameController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a name';
                    }
                    return null;
                  },
                ),
                SizedBox(height: 10.h),
                CustomTextField(
                  labelText: 'Description',
                  isObsecureText: false,
                  controller: descriptionController,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a description';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
        ),
        Step(
          isActive: currentStepIndex == 1,
          state: currentStepIndex == 1 ? StepState.editing : StepState.indexed,
          title: Text('2'),
          content: Form(
            key: _formKeyStep2,
            child: Column(
              children: [
                Text('Step 2 content'),
                // Add more fields for this step if needed
              ],
            ),
          ),
        ),
      ];

  Widget _buildCompletted() {
    return Center(
      child: Container(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text("Complted"),
            SizedBox(
              height: 30.h,
            ),
            CustomButton(
              buttonState: ButtonState.normal,
              buttonText: 'Go back',
              onTap: () {
                setState(() {
                  isCompleted = false;
                  currentStepIndex = 0;
                  nameController.clear();
                  descriptionController.clear();
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
