import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/payroll%20bloc/payroll_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/enums/month_enum.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/models/payroll_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/screens/pdf_viewer_screen.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_loading_skeleton.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/payroll_card_widget.dart';

class PayrollScreen extends StatefulWidget {
  const PayrollScreen({super.key});

  @override
  State<PayrollScreen> createState() => _PayrollScreenState();
}

class _PayrollScreenState extends State<PayrollScreen> {
  int selectedYear = DateTime.now().year;
  Month selectedMonth = Month.january;

  List<int> _getYears() {
    //
    DateTime now = DateTime.now();
    final int currentYear = now.year;
    final int pastYear = currentYear - 1;
    return [
      pastYear,
      currentYear,
    ];
  }

  @override
  void initState() {
    super.initState();
    DateTime now = DateTime.now();
    selectedYear = now.year;
    // Set to June (month 6) to match available API data
    selectedMonth = Month.values[now.month - 1]; // Month is 1-based index
    _getYears();

    // _loadInitialData(forceFetch: false);
    _loadInitialData();
  }

  Future<void> _loadInitialData({
    bool forceFetch = true,
  }) async {
    final payrollBloc = context.payrollBloc;
    // if (payrollBloc.payrolls.isEmpty) {
    payrollBloc.add(PayrollFetchEvent(
      employee: context.usersBloc.currentUser?.email ?? '',
      userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      forceFetch: forceFetch,
    ));

    await payrollBloc.stream.firstWhere((state) => state is PayrollLoadedState);
    // }
    payrollBloc.add(PayrollFilterEvent(
      month: selectedMonth,
      year: selectedYear,
    ));
  }

  @override
  Widget build(BuildContext context) {
    final payrollBloc = context.payrollBloc;
    return Scaffold(
      appBar: _buildAppBar(),
      body: BlocConsumer<PayrollBloc, PayrollState>(
        listener: (context, state) {
          if (state is PayrollFetchPdfSuccessState) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PdfViewerPage(
                  pdfBytes: state.pdfBytes,
                  title: "Payroll Pdf",
                  onDownload: () {
                    //
                  },
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          final payrolls = payrollBloc.payrolls;
          // print(payrolls.map((p) => p.toJson()));
          bool isLoading = state is PayrollLoadingState;
          return isLoading
              ? _buildLoadingSkelton()
              : RefreshIndicator.adaptive(
                  onRefresh: _loadInitialData,
                  child: CustomListGridView<PayrollModel>(
                    items: payrolls,
                    isLoading: isLoading,
                    layoutType: LayoutType.listView,
                    isEmpty: payrolls.isEmpty,
                    onRefresh: _loadInitialData,
                    itemBuilder: (context, payroll) {
                      return PayrollCard(
                        payroll: payroll,
                        isDownloading: state is PayrollFetchPdfLoadingState,
                        onDownload: () async {
                          //
                          payrollBloc.add(PayrollFetchPdfEvent(
                            payrollId: payroll.payrollId,
                          ));
                        },
                      );
                    },
                    // emptyDataBuilder: () {
                    //   return Placeholder();
                    // },
                  ),
                );
        },
      ),
    );
  }

  Widget _buildLoadingSkelton() {
    return CustomLoadingSkeleton(
      loadingItems: [
        LoadingSkeletonItem(
          height: 100.h,
          width: double.infinity,
          layoutType: LayoutType.custom,
        ),
        SizedBox(height: 20.h),
        LoadingSkeletonItem(
          height: 250.h,
          width: double.infinity,
          layoutType: LayoutType.custom,
        ),
        SizedBox(height: 20.h),
        LoadingSkeletonItem(
          height: 100.h,
          width: double.infinity,
          layoutType: LayoutType.custom,
        ),
      ],
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AnimatedAppBar(
      title: "Payroll",
      animationDirection: AnimationDirection.topToBottom,
      appBarHeight: 90.h,
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(60.h),
        child: _buildDropDowns(),
      ),
    );
  }

  Widget _buildDropDowns() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      color: Colors.transparent,
      height: 50.h,
      child: Row(
        children: [
          Expanded(
            child: CustomDropDown(
              value: selectedYear,
              items: _getYears(),
              displayItem: (value) => value?.toString(),
              labelText: 'Year',
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    selectedYear = value;
                  });

                  //
                  context.payrollBloc.add(PayrollFilterEvent(
                    month: selectedMonth,
                    year: selectedYear,
                  ));
                }
              },
            ),
          ),
          SizedBox(width: 20.w),
          Expanded(
            child: CustomSelectFieldDisplayer<Month>(
              bottomSheetTitle: "Select",
              labelText: "Month",
              leadingIcon: null,
              selectedItems: [selectedMonth],
              displayItem: (Month month) => month.name,
              selectionType: SelectionType.SingleSelection,
              options: Month.values,
              onSelectionChanged: (List<Month> month) {
                //
                setState(() {
                  selectedMonth = month.first;
                });
                context.payrollBloc.add(PayrollFilterEvent(
                  year: selectedYear,
                  month: selectedMonth,
                ));
              },
            ),
          ),
        ],
      ),
    );
  }
}
