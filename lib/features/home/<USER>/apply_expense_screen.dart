import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/expense%20bloc/expense_bloc.dart';
import 'package:rasiin_tasks_app/core/models/department_model.dart';
import 'package:rasiin_tasks_app/core/services/bottom_sheet_services.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/enums/date_validation_type_enum.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/core/params/create_expense_params.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animated_button_row_wideget.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_reach_text.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class ApplyExpenseScreen extends StatefulWidget {
  @override
  _ApplyExpenseScreenState createState() => _ApplyExpenseScreenState();
}

class _ApplyExpenseScreenState extends State<ApplyExpenseScreen> {
  final _expenseFormKey = GlobalKey<FormState>();

  void _addExpense(CreateExpenseParams expense) {
    context.read<ExpenseBloc>().add(AddExpense(expense: expense));
  }

  void _updateExpense(CreateExpenseParams expense) {
    context.read<ExpenseBloc>().add(UpdateExpense(expense: expense));
  }

  void _deleteExpense(CreateExpenseParams expense) {
    context.read<ExpenseBloc>().add(DeleteExpense(expense: expense));
  }

  void _clearFields() {
    _expenseFormKey.currentState!.reset();
    context.expenseBloc.createdExpenses.clear();
  }

  Future<void> _showBottomSheet(BuildContext context,
      {CreateExpenseParams? expense}) async {
    await BottomSheetServices.showBottomSheet(
      context,
      headerTitleText:
          expense == null ? 'New Expense Item' : 'Edit Expense Item',
      formFields: ExpenseBottomSheet(
        expense: expense,
        onAdd: _addExpense,
        onUpdate: _updateExpense,
        onDelete: _deleteExpense,
      ),
    );
  }

  late ExpenseBloc _expenseBloc;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _expenseBloc = context.read<ExpenseBloc>();
    print(ModalRoute.of(context)?.isActive);
  }

  @override
  void dispose() {
    _expenseBloc.createdExpenses.clear();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    context.expenseBloc.add(GetAllExpenseDepartments());
  }

  @override
  Widget build(BuildContext context) {
    final user = context.usersBloc.currentUser;
    final userRoles = user?.roleNames ?? [];
    final userEmail = user?.email ?? '';
    final dialogCubit = context.dialogCubit;
    return Scaffold(
      appBar: AnimatedAppBar(title: 'Apply Expense'),
      body: BlocListener<ExpenseBloc, ExpenseState>(
        listener: (context, state) {
          //
          if (state is ExpenseCreationLoadingState) {
            dialogCubit.showLoadingDialog();
          }

          if (state is ExpenseCreationErrorState) {
            String errorMessage = state.appFailure.getErrorMessage();

            //
            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }

          if (state is ExpenseCreationSuccessState) {
            context.read<ExpenseBloc>().createdExpenses.clear();
            _clearFields();
            context.read<ExpenseBloc>().add(GetAllExpense(
                  userRoles: userRoles,
                  userEmail: userEmail,
                ));
            dialogCubit.showSuccessDialog(
              message: state.message,
              onConfirm: () => Navigator.pop(context),
            );
          }
        },
        child: LayoutBuilder(
          builder: (BuildContext context, BoxConstraints constraints) {
            return BlocBuilder<ExpenseBloc, ExpenseState>(
              builder: (context, state) {
                final _expenses = context.expenseBloc.createdExpenses;
                final isLoading = state is ExpenseCreationLoadingState;
                // final isLoading = true;
                return Form(
                  key: _expenseFormKey,
                  child: Column(
                    children: [
                      Expanded(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(height: 25.h),
                              Padding(
                                // padding: EdgeInsets.symmetric(horizontal: 20.w),
                                padding: EdgeInsets.only(
                                  left: 20.w,
                                  right: 20.w,
                                  top: 20.h,
                                ),
                                child: _buildExpenseHeader(),
                              ),
                              SizedBox(height: 25.h),
                              _buildExpenseList(_expenses),
                              SizedBox(height: 25.h),
                              if (_expenses.isNotEmpty)
                                Padding(
                                  padding: EdgeInsets.only(right: 20.w),
                                  child: _buildTotalExpense(_expenses),
                                ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                          padding: const EdgeInsets.all(16),
                          child: _buildSubmitButton(
                            _expenses,
                            isLoading,
                          )),
                    ],
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildExpenseHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        AnimatedItemWrapper(
          delay: const Duration(milliseconds: 500),
          child: Text(
            'Expenses',
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        AnimatedItemWrapper(
          delay: const Duration(milliseconds: 500),
          child: ElevatedButton(
            onPressed: () {
              _showBottomSheet(context);
            },
            child: Text('ADD'),
          ),
        ),
      ],
    );
  }

  Widget _buildExpenseList(List<CreateExpenseParams> expenses) {
    if (expenses.isEmpty) {
      return Container(
        height: 80.h,
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Center(
          child: AnimatedItemWrapper(
            delay: const Duration(milliseconds: 600),
            child: Text(
              'No expenses added',
              style: TextStyle(
                fontSize: 16.sp,
                color: Colors.grey,
              ),
            ),
          ),
        ),
      );
    } else {
      return CustomListGridView<CreateExpenseParams>(
        items: expenses,
        isLoading: false,
        isEmpty: expenses.isEmpty,
        layoutType: LayoutType.listView,
        onRefresh: () {},
        showFooter: false,
        itemBuilder: (context, CreateExpenseParams expense) {
          return _buildExpenseItem(
            expense,
            onTap: () async {
              await _showBottomSheet(context, expense: expense);
            },
          );
        },
      );
    }
  }

  Widget _buildExpenseItem(
    CreateExpenseParams expense, {
    Function()? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.all(20),
        child: Row(
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomRichText(
                    title: 'Department : ',
                    subTitle: expense.departmentName,
                    subTitleFontSize: 14,
                    titleFontSize: 14,
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  CustomRichText(
                    title: 'Date : ',
                    subTitle: expense.date?.toFormattedString() ?? 'N/A',
                    subTitleFontSize: 14,
                    titleFontSize: 14,
                  ),
                ],
              ),
            ),

            SizedBox(
              width: 30.w,
            ),

            //
            Text(
              expense.amount.toMoneyString(),
              style: Theme.of(context).textTheme.bodyLarge,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTotalExpense(List<CreateExpenseParams> expenses) {
    double _totalExpensesPrice() {
      return expenses.fold<double>(0, (sum, item) => sum + item.amount);
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          "Total :",
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        SizedBox(
          width: 10.w,
        ),
        Flexible(
          child: Text(
            _totalExpensesPrice().toMoneyString(
              showSymbol: true,
            ),
            style: Theme.of(context).textTheme.labelLarge,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
          ),
        ),
      ],
    );
  }

  Widget _buildSubmitButton(
      List<CreateExpenseParams> expenses, bool isLoading) {
    return !isLoading
        ? AnimatedButtonRow(
            submitButtonState:
                isLoading ? ButtonState.loading : ButtonState.normal,
            horizentalMarging: 10,
            onCancelTap: () {
              Navigator.pop(context);
            },
            onSubmitTap: () {
              if (_expenseFormKey.currentState!.validate()) {
                if (expenses.isNotEmpty) {
                  context.read<ExpenseBloc>().add(CreateExpense(
                        createExpenseParams: expenses,
                      ));
                } else {
                  SnackBarHelper.showErrorSnackBar(
                    context: context,
                    message: "Please add at least one expense",
                  );
                }
              }
            },
          )
        : const SizedBox.shrink();
  }
}

class ExpenseBottomSheet extends StatefulWidget {
  final CreateExpenseParams? expense;
  final Function(CreateExpenseParams expense)? onAdd;
  final Function(CreateExpenseParams expense)? onUpdate;
  final Function(CreateExpenseParams expense)? onDelete;

  const ExpenseBottomSheet({
    super.key,
    this.expense,
    this.onAdd,
    this.onUpdate,
    this.onDelete,
  });

  @override
  _ExpenseBottomSheetState createState() => _ExpenseBottomSheetState();
}

class _ExpenseBottomSheetState extends State<ExpenseBottomSheet> {
  final _expenseFormKey = GlobalKey<FormState>();

  DepartmentModel? _department;
  String? _selectedDate;
  String? remark;
  String? amount;

  @override
  void initState() {
    super.initState();

    // Set initial values if provided
    if (widget.expense != null) {
      _department = context.expenseBloc.departments
          .where(
              (department) => department.name == widget.expense?.departmentName)
          .firstOrNull;

      // Set controllers' initial values
      remark = widget.expense?.remark ?? '';
      amount = widget.expense?.amount.toString() ?? '';
      _selectedDate = widget.expense?.date?.toIso8601String();
    } else {
      // Set to today's date if no expense is provided
      // _selectedDate = DateFormat('yyyy-MM-dd').format(DateTime.now());
      _selectedDate = DateTime.now().toFormattedString();
    }
  }

  void _clearFields() {
    _department = null;
    _selectedDate = null;
    remark = null;
    amount = null;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _expenseFormKey,
      child: BlocBuilder<ExpenseBloc, ExpenseState>(
        builder: (context, state) {
          return Column(
            children: [
              CustomDatePickerField(
                initialValue: _selectedDate,
                initialDate: widget.expense?.date,
                dateValidationType: DateValidationType.before,
                showDefaultDate: widget.expense == null,
                onDateSelected: (date) {
                  setState(() {
                    _selectedDate = date;
                    _selectedDate = _selectedDate!;
                  });
                },
                validator: (value) {
                  if (_selectedDate == null || _selectedDate!.isEmpty) {
                    return 'Please select a date';
                  }
                  return null;
                },
              ),
              SizedBox(height: 15.h),
              CustomSelectFieldDisplayer<DepartmentModel>(
                labelText: 'Select Department*',
                selectedItems: _department != null ? [_department!] : [],
                displayItem: (value) => value.name,
                displaySubTitle: (value) => value.id,
                onSelectionChanged: (value) {
                  setState(() {
                    _department = value.isNotEmpty ? value[0] : null;
                  });
                },
                validator: (value) => _department == null
                    ? 'Please select an expense type'
                    : null,
                selectionType: SelectionType.SingleSelection,
                options: context.expenseBloc.departments,
                bottomSheetTitle: "Select Department",
              ),
              SizedBox(height: 15.h),
              CustomTextField(
                initialValue: amount,
                labelText: 'Amount*',
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  amount = value;
                  setState(() {});
                },
                validator: (value) {
                  double? amount = double.tryParse(value ?? '');
                  if (amount == null || amount <= 0) {
                    return 'Please enter a valid amount';
                  }
                  return null;
                },
              ),
              SizedBox(height: 15.h),
              CustomTextField(
                initialValue: remark,
                labelText: 'Remark*',
                onChanged: (value) {
                  remark = value;
                  setState(() {});
                },
                validator: (value) => value == null || value.isEmpty
                    ? 'Please enter a remark'
                    : null,
              ),
              SizedBox(height: 15.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (widget.expense != null)
                    CustomButton(
                      width: 45,
                      padding: EdgeInsets.all(3),
                      height: 35,
                      borderRadius: 60,
                      leadingIcon: Align(
                        child: Icon(
                          Icons.delete,
                          color: Colors.red,
                        ),
                      ),
                      buttonState: ButtonState.normal,
                      onTap: () {
                        widget.onDelete?.call(widget.expense!);
                        Navigator.pop(context);
                      },
                      buttonText: null,
                    ),
                  CustomButton(
                    width: 80,
                    height: 30,
                    textStyle: Theme.of(context).textTheme.bodyLarge,
                    buttonState: ButtonState.normal,
                    buttonStyleType: ButtonStyleType.outline,
                    onTap: () {
                      Navigator.pop(context);
                    },
                    buttonText: 'Cancel',
                  ),
                  CustomButton(
                    width: 70,
                    buttonState: ButtonState.normal,
                    buttonText: widget.expense == null ? 'Add' : 'Update',
                    onTap: () {
                      //
                      if (_expenseFormKey.currentState!.validate()) {
                        final uniqueId = widget.expense != null
                            ? widget.expense?.uniqueId
                            : CreateExpenseParams.generateUniqueId();
                        CreateExpenseParams newExpense = CreateExpenseParams(
                          departmentName: _department?.name ?? '',
                          uniqueId: uniqueId ?? '',
                          remark: remark ?? '',
                          amount: double.parse(amount ?? ''),
                          date: _selectedDate?.toDateTime(),
                          userEmail: context.usersBloc.currentUser?.email ?? '',
                        );
                        if (widget.expense == null) {
                          print('Add expense is called');
                          widget.onAdd?.call(newExpense);
                        } else {
                          print('Update expense is called');
                          widget.onUpdate?.call(newExpense);
                        }

                        //
                        _clearFields();
                        Navigator.pop(context);
                      } else {
                        print('Form is invalid');
                      }
                    },
                  ),
                ],
              )
            ],
          );
        },
      ),
    );
  }
}
