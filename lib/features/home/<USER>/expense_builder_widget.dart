import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/expense_status.dart';
import 'package:rasiin_tasks_app/core/models/expense_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

import '../../../app/theme/colors/app_colors.dart';

class ExpenseBuilderWidget extends StatelessWidget {
  final void Function()? onTap;
  final ExpenseModel expense;

  const ExpenseBuilderWidget({
    super.key,
    this.onTap,
    required this.expense,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    final Color expenseStatusColor = appColors.getExpenseColor(
      expenseStatus: expense.status.toExpenseStatus(),
    );

    String expenseDate = expense.date?.toFormattedString() ?? 'N/A';

    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        expense.id,
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.buttonColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        expenseDate,
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.subtextColor.withValues(alpha: 0.5),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                CustomSmallContainer(
                  text: expense.status,
                  textColor: expenseStatusColor,
                  backgroundColor: expenseStatusColor,
                  fontSize: 14,
                ),
              ],
            ),
            SizedBox(height: 15.h),
            if (expense.department?.name.trim().isNotEmpty ?? false) ...[
              Text(
                "Department",
                style: textTheme.bodyMedium?.copyWith(
                  color: appColors.subtextColor.withValues(alpha: 0.5),
                ),
              ),
              Text(
                expense.department?.name ?? '',
                style: textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
