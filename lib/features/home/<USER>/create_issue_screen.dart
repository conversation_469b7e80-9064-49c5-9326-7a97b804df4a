import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/task_priority_enums.dart';
import 'package:rasiin_tasks_app/core/models/issue_type_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class CreateIssueScreen extends StatefulWidget {
  const CreateIssueScreen({super.key});

  @override
  State<CreateIssueScreen> createState() => _CreateIssueScreenState();
}

class _CreateIssueScreenState extends State<CreateIssueScreen> {
  final GlobalKey<FormState> _createIssueFormKey = GlobalKey<FormState>();
  final TextEditingController _subjectController = TextEditingController();
  final TextEditingController _decriptionController = TextEditingController();

  //
  TaskPriority? selectedPriority;
  IssueTypeModel? selectedIssueType;
  // List<String> priorityItems = ['Low', 'Medium', 'High', 'Urgent'];
  List<TaskPriority> priorityItems = TaskPriority.values;

  @override
  void initState() {
    super.initState();
    final issueBloc = context.issuesBloc;
    if (issueBloc.issueTypes.isEmpty) {
      issueBloc.add(GetAllIssueTypesEvent());
    }
  }

  @override
  void dispose() {
    super.dispose();
    _subjectController.dispose();
    _decriptionController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;
    return Scaffold(
      appBar: AnimatedAppBar(
        title: "Create Issue",
      ),
      body: BlocListener<IssueBloc, IssueState>(
        listener: (context, state) {
          if (state is IssueCreationStateError) {
            String errorMessage = state.appFailure.getErrorMessage();
            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }
          if (state is IssueCreationStateLoading) {
            dialogCubit.showLoadingDialog();
          }
          if (state is IssueCreationStateSuccess) {
            context.issuesBloc.add(GetAllIssueEvent());

            dialogCubit.showSuccessDialog(
              message: state.message,
              onConfirm: () {
                _subjectController.clear();
                _decriptionController.clear();
                selectedPriority = null;
                Navigator.pop(context);
                Navigator.pop(context);
              },
              barrierDismissible: false,
            );
          }
        },
        child: BlocBuilder<IssueBloc, IssueState>(
          builder: (context, state) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
              child: Form(
                key: _createIssueFormKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),
                      AnimatedItemWrapper(
                        delay: Duration(milliseconds: 500),
                        child: CustomTextField(
                          controller: _subjectController,
                          labelText: "subject",
                          isObsecureText: false,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'required';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(
                        height: 25.h,
                      ),
                      AnimatedItemWrapper(
                        delay: Duration(milliseconds: 700),
                        child: CustomDropDown<IssueTypeModel>(
                          items: context.issuesBloc.issueTypes,
                          displayItem: (issueType) => issueType?.name ?? '',
                          labelText: "Issue Types",
                          onChanged: (issueType) {
                            //
                            selectedIssueType = issueType;
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'required!';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(
                        height: 25.h,
                      ),
                      AnimatedItemWrapper(
                        delay: Duration(milliseconds: 800),
                        child: CustomDropDown<TaskPriority>(
                          leadingImgPath: ImageConstants.power_svg,
                          items: priorityItems,
                          displayItem: (value) => value?.displayName,
                          labelText: "Priority*",
                          onChanged: (value) {
                            setState(() {
                              selectedPriority = value;
                            });
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'required!';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(height: 25.h),
                      AnimatedItemWrapper(
                        delay: Duration(milliseconds: 900),
                        child: CustomTextField(
                          controller: _decriptionController,
                          labelText: "Description",
                          isObsecureText: false,
                          maxLine: 5,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'required';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(
                        height: 30.h,
                      ),
                      AnimatedItemWrapper(
                        delay: Duration(milliseconds: 1100),
                        child: CustomButton(
                          buttonState: state is IssueStateLoading
                              ? ButtonState.loading
                              : ButtonState.normal,
                          buttonText: 'Submit',
                          onTap: () {
                            //

                            if (_createIssueFormKey.currentState!.validate()) {
                              context.issuesBloc.add(
                                CreateIssueEvent(
                                  subject: _subjectController.text,
                                  raisedBy:
                                      context.usersBloc.currentUser?.email ??
                                          '',
                                  priority: selectedPriority?.displayName ?? '',
                                  issueType: selectedIssueType?.name ?? '',
                                  decription: _decriptionController.text,
                                ),
                              );
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
