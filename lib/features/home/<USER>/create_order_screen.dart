import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animated_button_row_wideget.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_date_picker_field.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';

class CreateOrderScreen extends StatefulWidget {
  const CreateOrderScreen({super.key});

  @override
  State<CreateOrderScreen> createState() => _CreateOrderScreenState();
}

class _CreateOrderScreenState extends State<CreateOrderScreen> {
  final _orderFormKey = GlobalKey<FormState>();

  String? selectedCompany;
  String? selectedCostCenter;
  String? selectedDate;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(title: "Create Order"),
      body: Form(
        key: _orderFormKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 20.h),
                      AnimatedItemWrapper(
                        delay: const Duration(milliseconds: 400),
                        child: CustomSelectFieldDisplayer<String?>(
                          leadingIcon: Icons.account_balance_wallet,
                          labelText: "Cost Center*",
                          selectedItems: [selectedCostCenter],
                          displayItem: (value) => value ?? '',
                          onSelectionChanged: (value) {
                            setState(() {
                              selectedCostCenter =
                                  value.isNotEmpty ? value[0] : null;
                            });
                          },
                          selectionType: SelectionType.SingleSelection,
                          options: ['Mele', 'Main -RT', 'Rasiin'],
                          bottomSheetTitle: "Select Cost Center",
                          validator: (value) {
                            if (selectedCostCenter == null ||
                                selectedCostCenter!.isEmpty) {
                              return 'required*';
                            }
                            return null;
                          },
                        ),
                      ),
                      SizedBox(height: 20.h),
                      AnimatedItemWrapper(
                        delay: const Duration(milliseconds: 600),
                        child: CustomDatePickerField(
                          validator: (value) {
                            if (selectedDate == null || selectedDate!.isEmpty) {
                              return 'required*';
                            }
                            return null;
                          },
                          onDateSelected: (value) {
                            setState(() {
                              selectedDate = value;
                            });
                          },
                        ),
                      ),
                      SizedBox(height: 30.h),
                    ],
                  ),
                ),
              ),
            ),
            AnimatedButtonRow(
              submitButtonState: ButtonState.normal,
              onCancelTap: () {
                Navigator.of(context).pop();
              },
              onSubmitTap: () {
                if (_orderFormKey.currentState!.validate()) {
                  ///
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
