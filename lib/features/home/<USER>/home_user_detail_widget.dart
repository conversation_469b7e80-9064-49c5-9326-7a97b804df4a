import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_image_picker_card.dart';
import 'package:rasiin_tasks_app/features/common/widgets/typer_text_animation_effect.dart';

class HomeUserDetailWidget extends StatefulWidget {
  const HomeUserDetailWidget({
    super.key,
  });

  @override
  State<HomeUserDetailWidget> createState() => _HomeUserDetailWidgetState();
}

class _HomeUserDetailWidgetState extends State<HomeUserDetailWidget> {
  Timer? _timer;
  String _currentGreeting = '';

  @override
  void initState() {
    super.initState();
    _updateGreeting();
    // Update greeting every minute to catch time changes
    _timer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _updateGreeting();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void _updateGreeting() {
    final newGreeting = getGreetingMessage();
    if (_currentGreeting != newGreeting) {
      setState(() {
        _currentGreeting = newGreeting;
      });
      print('🕐 Greeting updated to: $newGreeting');
    }
  }

  String getWeekdaySpecialGreeting() {
    final now = DateTime.now();
    final weekday = now.weekday;

    switch (weekday) {
      case DateTime.monday:
        return 'Happy Monday!';
      case DateTime.friday:
        return 'Happy Friday!';
      case DateTime.saturday:
      case DateTime.sunday:
        return 'Enjoy your weekend!';
      default:
        return '';
    }
  }

  String getGreetingMessage() {
    final now = DateTime.now();
    final hour = now.hour;

    // More refined time-based greetings with emojis
    if (hour >= 5 && hour < 12) {
      return '🌅 Good Morning,';
    } else if (hour >= 12 && hour < 17) {
      return '☀️ Good Afternoon,';
    } else if (hour >= 17 && hour < 21) {
      return '🌆 Good Evening,';
    } else if (hour >= 21 || hour < 2) {
      return '🌙 Good Night,';
    } else {
      // Late night/early morning (2-5 AM)
      return '🌃 Working Late?';
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UsersBloc, UsersState>(
      buildWhen: (previous, currentState) {
        return currentState is UsersStateDataLoaded ||
            currentState is UsersStateDataLoading ||
            currentState is UsersStateDataError ||
            currentState is UsersStateError ||
            currentState is UsersStateLoading ||
            currentState is UsersStateProfileImageFetched;
      },
      builder: (context, state) {
        final appColors = context.appColors;
        UserModel? currentUser = context.usersBloc.currentUser;

        return Container(
          color: appColors.transparent,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TypewriterTextAnimationEffect(
                          text: _currentGreeting,
                          textStyle: Theme.of(context).textTheme.titleMedium,
                          duration: Duration(seconds: 2),
                        ),
                        // Show special weekday greeting if available
                        if (getWeekdaySpecialGreeting().isNotEmpty) ...[
                          SizedBox(height: 2.h),
                          Text(
                            getWeekdaySpecialGreeting(),
                            style:
                                Theme.of(context).textTheme.bodySmall?.copyWith(
                                      color: Colors.grey[600],
                                      fontStyle: FontStyle.italic,
                                    ),
                          ),
                        ],
                        SizedBox(
                          height: 2.h,
                        ),
                        Text(
                          (currentUser?.employeeName ?? 'No Name').safeText,
                          style: GoogleFonts.roboto(
                            fontSize: 20.sp,
                            color: Colors.blueAccent,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                          softWrap: true,
                        ),
                      ],
                    ),
                  ),
                  AnimatedItemWrapper(
                    delay: const Duration(seconds: 2),
                    child: CustomImagePickerCard(
                      isLoading: true,
                      imageUrl: currentUser?.profileImage,
                      imageFile: context.usersBloc.profileImage,
                      showIcon: false,
                      radius: 30,
                      isProfile: true,
                      userName: currentUser?.employeeName,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}
