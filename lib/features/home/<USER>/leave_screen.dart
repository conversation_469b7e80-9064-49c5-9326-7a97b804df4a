import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/core/bloc/leave%20bloc/leave_bloc.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/leave_model.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_reach_text.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class LeaveScreen extends StatefulWidget {
  const LeaveScreen({super.key});

  @override
  State<LeaveScreen> createState() => LeaveScreenState();
}

class LeaveScreenState extends State<LeaveScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData({
    bool forceFetch = false,
  }) async {
    final leaves = context.leaveBloc.leaves;
    if (leaves.isEmpty || forceFetch) {
      context.leaveBloc.add(GetAllLeavesEvent(
        employee: context.usersBloc.currentUser?.email ?? '',
        userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      ));
    }
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
      appBar: const AnimatedAppBar(
        title: 'My Leaves',
      ),
      floatingActionButton: CustomFloatingActionButton(
        foregroundColor: appColors.backgroundColor,
        onPressed: () {
          //
          Navigator.pushNamed(context, ScreenConstants.applyLeave);
        },
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: () async {
          await _loadData(forceFetch: true);
        },
        child: BlocConsumer<LeaveBloc, LeaveState>(
          buildWhen: (previousState, currentState) =>
              currentState is LeaveLoadingState ||
              currentState is LeaveErrorState ||
              currentState is LeaveLoadedState ||
              currentState is LeaveApprovalLoadingState ||
              currentState is LeaveApprovalSuccessState ||
              currentState is LeaveApprovalErrorState,
          listener: (context, state) {
            if (state is LeaveApprovalLoadingState) {
              //
              context.dialogCubit.showLoadingDialog();
            }

            if (state is LeaveApprovalErrorState) {
              context.dialogCubit.closeDialog();
              Navigator.pop(context);

              final message = state.appFailure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: message,
              );
            }

            if (state is LeaveApprovalSuccessState) {
              context.dialogCubit.closeDialog();
              Navigator.pop(context);
              _loadData();

              final message = state.message;
              SnackBarHelper.showSuccessSnackBar(
                context: context,
                message: message,
              );
            }
          },
          builder: (context, state) {
            final leaves = context.leaveBloc.leaves;
            bool isLoading = state is LeaveLoadingState;
            bool isEmpty = state is LeaveLoadedState && state.leaves.isEmpty;
            return CustomListGridView(
              onRefresh: () async {
                await _loadData();
              },
              items: leaves,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              itemBuilder: (context, leave) {
                return LeaveCardWidget(
                  leave: leave,
                  onTap: () {
                    //
                    showLeaveBottomSheet(
                      context: context,
                      leave: leave,
                      onApproval: () {
                        //
                        final userRoles =
                            context.usersBloc.currentUser?.roleNames ?? [];
                        context.leaveBloc.add(
                          ApproveLeaveEvent(
                            leaveId: leave.id,
                            userRoles: userRoles,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  void showLeaveBottomSheet({
    required BuildContext context,
    required LeaveModel leave,
    required VoidCallback onApproval,
  }) {
    showModalBottomSheet(
      context: context,
      builder: (context) => LeaveBottomSheet(
        leave: leave,
        onApproval: onApproval,
      ),
    );
  }
}

class LeaveBottomSheet extends StatelessWidget {
  final LeaveModel leave;
  final VoidCallback onApproval;

  const LeaveBottomSheet({
    super.key,
    required this.leave,
    required this.onApproval,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final isDraft = leave.docStatus == 0;
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    final canApprove = UserRolesHelper().canViewAllLeaves(userRoles);
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Center(
                    child: Container(
                      height: 7.h,
                      width: 100.w,
                      margin: EdgeInsets.only(bottom: 10.h),
                      decoration: BoxDecoration(
                        color: appColors.dividerColor,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                    ),
                  ),
                  //
                  Center(
                    child: Text(
                      'Leave Details',
                      style: textTheme.titleLarge,
                    ),
                  ),
                  SizedBox(height: 10.h),
                  _buildLeaveDetails(
                    context: context,
                    title: 'Leave ID:',
                    value: leave.id,
                    icon: FontAwesomeIcons.hashtag,
                  ),
                  _buildLeaveDetails(
                    context: context,
                    title: 'Employee:',
                    value: leave.employee,
                    icon: FontAwesomeIcons.userTie,
                  ),
                  _buildLeaveDetails(
                    context: context,
                    title: 'Applied On:',
                    value: leave.formattedCreation,
                    icon: FontAwesomeIcons.calendarDay,
                  ),
                  _buildLeaveDetails(
                    context: context,
                    title: 'Status:',
                    value: leave.status,
                    // ignore: deprecated_member_use
                    icon: FontAwesomeIcons.infoCircle,
                    trailing: CustomSmallContainer(
                      text: leave.status,
                      backgroundColor: leave.getLeaveColor(context: context),
                      textColor: leave.getLeaveColor(context: context),
                      showBorder: false,
                    ),
                  ),
                  _buildLeaveDetails(
                    context: context,
                    title: 'Leave Type:',
                    value: leave.leaveType,
                    icon: FontAwesomeIcons.suitcaseRolling,
                  ),

                  Divider(
                    color: appColors.dividerColor,
                    indent: 10.w,
                    endIndent: 10.w,
                  ),
                  // reason
                  if (leave.reason.trim().isNotEmpty) ...[
                    SizedBox(height: 10.h),
                    Center(
                      child: Text(
                        'Reason',
                        style: textTheme.titleLarge,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Text(
                        leave.reason,
                        style: textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),

          // approval button
          if (isDraft && canApprove)
            CustomButton(
              buttonState: ButtonState.normal,
              buttonText: 'Approve',
              backgroundColor: appColors.buttonColor,
              onTap: onApproval,
            )
          else
            CustomButton.disabled(
              buttonState: ButtonState.disabled,
              buttonText: leave.status,
            ),
        ],
      ),
    );
  }

  Widget _buildLeaveDetails({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    Widget? trailing,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(icon),
              SizedBox(width: 5.w),
              Text(title,
                  style: textTheme.bodyMedium?.copyWith(
                    color: appColors.subtextColor,
                  )),
            ],
          ),
          trailing ??
              Text(
                value,
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w900,
                ),
              ),
        ],
      ),
    );
  }
}

class LeaveCardWidget extends StatelessWidget {
  final LeaveModel leave;
  final Function()? onTap;

  const LeaveCardWidget({
    super.key,
    required this.leave,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;

    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Employee Name and Leave Type
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      leave.id,
                      style: textTheme.titleLarge?.copyWith(
                        fontSize: 18.sp,
                      ),
                    ),
                    Text(
                      leave.formattedCreation,
                      style: textTheme.bodyMedium?.copyWith(
                        fontSize: 14.sp,
                        color: appColors.subtextColor,
                        fontWeight: FontWeight.w900,
                      ),
                    ),
                  ],
                ),
                CustomSmallContainer(
                  text: leave.status,
                  backgroundColor: leave.getLeaveColor(context: context),
                  textColor: leave.getLeaveColor(context: context),
                  showBorder: false,
                ),
              ],
            ),

            SizedBox(height: 10.h),

            // Leave Type
            Text(
              leave.leaveType,
              style: textTheme.bodyMedium?.copyWith(
                fontSize: 14.sp,
              ),
            ),

            Padding(
              padding: EdgeInsets.symmetric(vertical: 5.h),
              child: Divider(),
            ),

            // Leave Dates
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgPicture.asset(
                  ImageConstants.calendar_svg,
                  width: 15.w,
                  height: 15.h,
                ),

                SizedBox(width: 10.w),

                Flexible(
                  child: CustomRichText(
                    title: 'From: ',
                    subTitle: leave.formattedFromDate,
                  ),
                ),

                //
                SizedBox(width: 10.w),

                Flexible(
                  child: CustomRichText(
                    title: 'To: ',
                    subTitle: leave.formattedToDate,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
