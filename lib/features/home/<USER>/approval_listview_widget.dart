import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/approval_decission.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/models/sales_invoice_model.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class ApprovalListviewWidget extends StatefulWidget {
  final SalesInvoiceModel? salesInvoiceModel;
  final VoidCallback? onTapPdf;
  final void Function(ApprovalDecision? decision) onTapAction;
  final bool isPdfLoading;

  const ApprovalListviewWidget({
    super.key,
    required this.salesInvoiceModel,
    required this.onTapPdf,
    required this.onTapAction,
    required this.isPdfLoading,
  });

  @override
  State<ApprovalListviewWidget> createState() => _ApprovalListviewWidgetState();
}

class _ApprovalListviewWidgetState extends State<ApprovalListviewWidget> {
  @override
  Widget build(BuildContext context) {
    final salesInvoice = widget.salesInvoiceModel;
    if (salesInvoice == null) return const SizedBox.shrink();

    return CustomContainer(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row with Invoice ID and Status
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      salesInvoice.id,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: context.appColors.primaryColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      salesInvoice.customer,
                      style: context.textTheme.bodyMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 8.w),
              Flexible(
                flex: 1,
                child: CustomSmallContainer(
                  text: salesInvoice.status,
                  textColor: _getStatusColor(salesInvoice.status),
                  backgroundColor: _getStatusColor(salesInvoice.status),
                  fontSize: 12,
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          // Details Row
          IntrinsicHeight(
            child: Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    label: "Posting Date",
                    value: salesInvoice.formattedPostingDate,
                    icon: Icons.calendar_today,
                  ),
                ),
                VerticalDivider(
                  width: 20.w,
                  thickness: 1,
                  color: context.appColors.dividerColor,
                ),
                Expanded(
                  child: _buildDetailItem(
                    label: "Amount",
                    value: salesInvoice.formattedTotalAmount,
                    icon: Icons.attach_money,
                  ),
                ),
                VerticalDivider(
                  width: 20.w,
                  thickness: 1,
                  color: context.appColors.dividerColor,
                ),
                Expanded(
                  child: _buildDetailItem(
                    label: "Due Date",
                    value: salesInvoice.formattedDueDate,
                    icon: Icons.schedule,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 16.h),

          // Action Row
          Row(
            children: [
              Expanded(
                flex: 2,
                child: CustomDropDown<ApprovalDecision>(
                  items: ApprovalDecision.values,
                  // hintText: "Select Action",
                  displayItem: (decision) =>
                      decision?.salesInvoiceApprovalDecision,
                  onChanged: widget.onTapAction,
                  labelText: "Action",
                ),
              ),
              SizedBox(width: 12.w),
              Flexible(
                flex: 1,
                child: GestureDetector(
                  onTap: widget.isPdfLoading ? null : widget.onTapPdf,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(vertical: 12.h, horizontal: 8.w),
                    decoration: BoxDecoration(
                      color:
                          context.appColors.primaryColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Center(
                      child: widget.isPdfLoading
                          ? SizedBox(
                              height: 16.h,
                              width: 16.w,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: context.appColors.primaryColor,
                              ),
                            )
                          : Icon(
                              FontAwesomeIcons.filePdf,
                              color: context.appColors.primaryColor,
                              size: 16.w,
                            ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDetailItem({
    required String label,
    required String value,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16.w,
          color: context.appColors.dividerColor,
        ),
        SizedBox(height: 4.h),
        Text(
          label,
          style: context.textTheme.bodySmall?.copyWith(
            color: context.appColors.dividerColor,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 2.h),
        Text(
          value,
          style: context.textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'draft':
        return Colors.orange;
      case 'submitted':
        return Colors.blue;
      case 'paid':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return context.appColors.dividerColor;
    }
  }
}
