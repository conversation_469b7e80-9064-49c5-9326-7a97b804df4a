import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/core/bloc/notification%20bloc/notification_bloc.dart';
import 'package:rasiin_tasks_app/core/models/notification_model.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  @override
  void initState() {
    super.initState();
    final notifications = context.notificationBloc.notifications;
    if (notifications.isEmpty) {
      context.notificationBloc.add(GetAllNotifications());
    }
  }

  Future<void> onRefresh() async {
    context.notificationBloc.add(GetAllNotifications());
  }

  @override
  Widget build(BuildContext context) {
    final notificationBloc = context.notificationBloc;
    return Scaffold(
      floatingActionButton: CustomFloatingActionButton(
        onPressed: () {
          //
          Navigator.pushNamed(context, ScreenConstants.createNotification);
        },
      ),
      appBar: AnimatedAppBar(
        title: 'Notifications',
      ),
      body: RefreshIndicator(
        onRefresh: onRefresh,
        child: BlocConsumer<NotificationBloc, NotificationState>(
          listener: (context, state) {
            if (state is NotificationStateError) {
              final errorMessage = state.appFailure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }
            if (state is NotificationStateSuccess) {
              SnackBarHelper.showSuccessSnackBar(
                context: context,
                message: state.message,
              );
            }
          },
          builder: (context, state) {
            final List<NotificationModel> notifications =
                notificationBloc.notifications;

            bool isLoading = state is NotificationStateLoading;
            bool isEmpty =
                state is NotificationStateLoaded && notifications.isEmpty;

            return CustomListGridView(
              onRefresh: onRefresh,
              items: notifications,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              itemBuilder: (context, NotificationModel notification) {
                return NotificationCardWidget(
                  notificationModel: notification,
                  onDelete: () {
                    final notificationId = notification.notificationId ?? '';
                    context.notificationBloc.add(
                      DeleteNotifications(notificationId: notificationId),
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }
}

class NotificationCardWidget extends StatelessWidget {
  final NotificationModel notificationModel;
  final Function()? onDelete;
  const NotificationCardWidget({
    super.key,
    required this.notificationModel,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return CustomContainer(
      padding: EdgeInsets.zero,
      child: ListTile(
        leading: Icon(Icons.notifications),
        title: Text(
          notificationModel.title ?? 'no title',
          style: textTheme.bodyLarge,
        ),
        subtitle: Text(
          notificationModel.message ?? 'no message',
          style: textTheme.bodyMedium,
        ),
        trailing: IconButton(
          onPressed: onDelete,
          icon: const Icon(
            Icons.delete,
            color: Colors.red,
          ),
        ),
      ),
    );
  }
}
