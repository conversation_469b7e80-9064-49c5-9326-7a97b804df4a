import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class HomeGridviewBuilderWidget extends StatelessWidget {
  const HomeGridviewBuilderWidget({
    super.key,
    required this.imgPath,
    required this.title,
    required this.onTap,
  });

  final String imgPath;
  final String title;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 20.h),
              Image.asset(
                imgPath,
                height: 30.h,
                width: 30.w,
                cacheWidth:
                    (30.w * MediaQuery.of(context).devicePixelRatio).round(),
                cacheHeight:
                    (30.h * MediaQuery.of(context).devicePixelRatio).round(),
                fit: BoxFit.cover,
              ),
              SizedBox(height: 5.h),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontSize: 14,
                        // fontWeight: FontWeight.bold,
                      ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
