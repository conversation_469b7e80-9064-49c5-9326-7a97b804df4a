import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_string_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';
import 'package:rasiin_tasks_app/core/bloc/issue%20bloc/issue_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/models/issue_model.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';

class IssueScreen extends StatefulWidget {
  const IssueScreen({super.key});

  @override
  State<IssueScreen> createState() => _IssueScreenState();
}

class _IssueScreenState extends State<IssueScreen> {
  @override
  void initState() {
    super.initState();
    final issueBloc = context.issuesBloc;
    // final usersBloc = context.usersBloc;
    if (issueBloc.issues.isEmpty) {
      issueBloc.add(GetAllIssueEvent());
    }
    if (issueBloc.issueTypes.isEmpty) {
      issueBloc.add(GetAllIssueTypesEvent());
    }
    // if (usersBloc.allUsers.isEmpty) {
    //   usersBloc.add(GetAllUsersEvent());
    // }
  }

  Future<void> _loadData({
    bool forceFetch = false,
  }) async {
    context.issuesBloc.add(GetAllIssueEvent());
    context.issuesBloc.add(GetAllIssueTypesEvent());
    context.usersBloc.add(GetAllUsersEvent());
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
        floatingActionButton: CustomFloatingActionButton(
          foregroundColor: appColors.backgroundColor,
          onPressed: () {
            //
            Navigator.pushNamed(context, ScreenConstants.createIssue);
          },
        ),
        appBar: AnimatedAppBar(
          title: 'Issue',
        ),
        body: RefreshIndicator(
          onRefresh: () async {
            await _loadData(forceFetch: true);
          },
          child: BlocConsumer<IssueBloc, IssueState>(
            listener: (context, state) {
              //
              if (state is IssueStateIssuesLoaded) {
                SnackBarHelper.showSuccessSnackBar(
                  context: context,
                  message: "Issues Loaded SuccessFully",
                );
              }
              if (state is IssueStateError) {
                final errorMessage = state.appFailure.getErrorMessage();
                SnackBarHelper.showErrorSnackBar(
                  context: context,
                  message: errorMessage,
                );
              }
            },
            builder: (context, state) {
              bool isLoading = state is IssueStateIssuesLoading ||
                  state is IssueStateIssueTypesLoading;
              bool isEmpty =
                  state is IssueStateIssuesLoaded && state.issues.isEmpty;
              final List<IssueModel> issues = context.issuesBloc.issues;

              return CustomListGridView(
                onRefresh: () async {
                  await _loadData(forceFetch: true);
                },
                items: issues,
                isLoading: isLoading,
                isEmpty: isEmpty,
                layoutType: LayoutType.listView,
                itemBuilder: (context, IssueModel issue) {
                  return IssueCard(
                    issue: issue,
                    onTap: () {
                      Navigator.pushNamed(
                        context,
                        ScreenConstants.issueDetails,
                        arguments: issue,
                      );
                    },
                  );
                },
              );
            },
          ),
        ));
  }
}

//!!
class IssueCard extends StatelessWidget {
  final IssueModel issue;
  // onTap
  final Function()? onTap;
  const IssueCard({
    super.key,
    required this.issue,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 15.h),
        margin: EdgeInsets.all(2.w),
        boxShadow: [
          BoxShadow(
            color: appColors.subtextColor.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(2, 2),
          ),
        ],
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        issue.subject.safeText,
                        style: textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 5.w),
                //!
                if (issue.priority.isNotEmpty)
                  CustomSmallContainer(
                    text: issue.priority,
                  ),
              ],
            ),
            //!!!!!!!
            SizedBox(
              height: 15.h,
            ),

            Text(
              "raised by : ${issue.raisedBy.safeText}",
              style: textTheme.bodyMedium,
            ),
            SizedBox(
              height: 5.h,
            ),
            Text(
              // issue.description ?? 'N/A',
              issue.description.parseHtmlString(),
              style: textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }
}
