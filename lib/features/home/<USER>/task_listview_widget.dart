import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/custom%20theme/custom_text_theme.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class TaskListviewWidget extends StatelessWidget {
  const TaskListviewWidget({
    super.key,
    required this.assignedTask,
    required this.onTap,
    required this.onComment,
  });

  final AssignedTasksModel assignedTask;
  final Function()? onTap;
  final Function()? onComment;

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return CustomContainer(
      margin: EdgeInsets.all(0.w),
      width: MediaQuery.of(context).size.width - 60.w,
      padding: EdgeInsets.all(0),
      clipBehavior: Clip.hardEdge,
      child: Column(
        children: [
          GestureDetector(
            onTap: onTap,
            child: Stack(
              children: [
                //!!!!!!!!1
                Container(
                  height: 100.h,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    // color: AppColors.secondaryColor,
                    image: DecorationImage(
                      image: ResizeImage(
                        AssetImage(
                          ImageConstants.backGround_jpg,
                        ),
                        width:
                            (MediaQuery.of(context).devicePixelRatio).round(),
                        height:
                            (100.h * MediaQuery.of(context).devicePixelRatio)
                                .round(),
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                //!
                Positioned(
                  top: 10.h,
                  left: 15.w,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 5.h,
                    ),
                    decoration: BoxDecoration(
                      color: appColors.transparent,
                      border: Border.all(
                          color: appColors.subtextColor.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: appColors.whiteColor.withValues(alpha: 0.1),
                          spreadRadius: 0.8,
                          blurRadius: 1,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        assignedTask.priority,
                        style: CustomTextTheme.getLightStyle(context: context),
                      ),
                    ),
                  ),
                ),

                //!1
                Positioned(
                  top: 10.h,
                  right: 15.w,
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 15.w,
                      vertical: 5.h,
                    ),
                    decoration: BoxDecoration(
                      color: appColors.transparent,
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: appColors.whiteColor.withValues(alpha: 0.1),
                          spreadRadius: 0.8,
                          blurRadius: 1,
                        ),
                      ],
                    ),
                    child: Center(
                      child: Text(
                        assignedTask.status,
                        style: CustomTextTheme.getLightStyle(context: context),
                      ),
                    ),
                  ),
                ),

                //!!!!!!!!!!!1
                Positioned(
                    top: 50.h,
                    left: 20.w,
                    child: Text(
                      assignedTask.referenceType,
                      style: CustomTextTheme.getLightStyle(context: context),
                    )),

                //!!!!!!!!!
                Positioned(
                    top: 75.h,
                    left: 20.w,
                    child: Row(
                      children: [
                        Icon(
                          Icons.watch_later_outlined,
                          color: appColors.whiteColor,
                          size: 20.w,
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Text(
                          assignedTask.date,
                          style:
                              CustomTextTheme.getLightStyle(context: context),
                        ),
                      ],
                    )),
              ],
            ),
          ),

          //!!!!!!!!!!!!!!11
          SizedBox(
            height: 5.h,
          ),
          GestureDetector(
            onTap: onComment,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: Row(
                children: [
                  FaIcon(
                    FontAwesomeIcons.facebookMessenger,
                    size: 15.w,
                    // color: Theme.of(context).brightness == Brightness.dark ? :,
                    color: appColors.primaryColor,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    // commentsLenght.toString(),
                    // "1",
                    assignedTask.commentCount.toString(),
                    style: textTheme.bodyLarge,
                  ),
                  SizedBox(width: 30.w),
                  Expanded(
                    child: Text(
                      "Comments",
                      style: textTheme.bodyLarge,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      softWrap: true,
                    ),
                  ),
                  // SizedBox(
                  //   width: 70.w,
                  // ),
                  CircleAvatar(
                    radius: 15.w,
                    backgroundImage: ResizeImage(
                      AssetImage(
                        ImageConstants.user_png,
                      ),
                      width: (15.r * 2)
                          .toInt(), // Width equal to the diameter of the circle
                      height: (15.r * 2).toInt(),
                    ),
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }
}
