import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class HomeAttendenceDetailsWidget extends StatelessWidget {
  const HomeAttendenceDetailsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final attendance = context.usersBloc.dashboardData?.attendanceSummary;

    // If no attendance data, return empty container or a message
    if (attendance == null) {
      return const SizedBox.shrink();
      // OR return a message:
      // return Text("No attendance data available", style: textTheme.bodyMedium);
    }

    // Calculate percentages for progress bars
    final presentPercentage = attendance.totalDays > 0
        ? attendance.present / attendance.totalDays
        : 0;
    final absentPercentage =
        attendance.totalDays > 0 ? attendance.absent / attendance.totalDays : 0;
    final onLeavePercentage = attendance.totalDays > 0
        ? attendance.onLeave / attendance.totalDays
        : 0;

    // Get current month name
    final monthName = DateTime.now().monthName;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Attendance Details",
          style: textTheme.titleSmall,
        ),
        SizedBox(height: 10.h),
        CustomContainer(
          margin: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomSmallContainer(
                text: monthName,
                fontSize: 14.sp,
                borderRadius: 20.r,
                textColor: appColors.primaryColor,
                backgroundColor: appColors.primaryColor.withValues(alpha: 0.1),
                verticalPadding: 4,
                horizontalPadding: 12,
              ),
              SizedBox(height: 12.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Text(
                        "${attendance.present} Days",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.successColor,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        "|",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.subtextColor,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        "${attendance.absent} Days",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.errorColor,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        "|",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.subtextColor,
                        ),
                      ),
                      SizedBox(width: 8.w),
                      Text(
                        "${attendance.onLeave} Days",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.warningColor,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    "${attendance.totalDays} Days",
                    style: textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 12.h),
              // Progress bars
              Container(
                height: 10.h,
                width: MediaQuery.of(context).size.width,
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  border: Border.all(
                      color: appColors.subtextColor.withValues(alpha: 0.3)),
                  color: appColors.subtextColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10.r),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: (presentPercentage * 100).round(),
                      child: Container(
                        decoration: BoxDecoration(
                          color: appColors.successColor,
                          borderRadius: BorderRadius.horizontal(
                            left: Radius.circular(10.r),
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      flex: (absentPercentage * 100).round(),
                      child: Container(
                        color: appColors.errorColor,
                      ),
                    ),
                    Expanded(
                      flex: (onLeavePercentage * 100).round(),
                      child: Container(
                        decoration: BoxDecoration(
                          color: appColors.warningColor,
                          borderRadius: BorderRadius.horizontal(
                            right: Radius.circular(10.r),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 12.h),
              // Legend
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildLegendItem(
                    context,
                    color: appColors.successColor,
                    label: "Present",
                  ),
                  SizedBox(width: 16.w),
                  _buildLegendItem(
                    context,
                    color: appColors.errorColor,
                    label: "Absent",
                  ),
                  SizedBox(width: 16.w),
                  _buildLegendItem(
                    context,
                    color: appColors.warningColor,
                    label: "On Leave",
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(BuildContext context,
      {required Color color, required String label}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 12.h,
          width: 12.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: color,
          ),
        ),
        SizedBox(width: 6.w),
        Text(
          label,
          style: context.textTheme.bodySmall,
        ),
      ],
    );
  }
}
