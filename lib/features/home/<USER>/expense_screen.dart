import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/expense_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/edit_expense_details_screen.dart'
    show EditExpenseDetailsScreen;
import 'package:rasiin_tasks_app/features/home/<USER>/expense_builder_widget.dart';

import '../../../core/bloc/expense bloc/expense_bloc.dart';

class ExpenseScreen extends StatefulWidget {
  const ExpenseScreen({super.key});

  @override
  State<ExpenseScreen> createState() => _ExpenseScreenState();
}

class _ExpenseScreenState extends State<ExpenseScreen> {
  @override
  initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData({
    bool forceFetch = false,
  }) async {
    final user = context.usersBloc.currentUser;
    final userRoles = user?.roleNames ?? [];
    final userEmail = user?.email ?? '';
    final expenses = context.expenseBloc.expenses;
    final expenseDepartments = context.expenseBloc.departments;
    await Future.wait([
      if (expenseDepartments.isEmpty || forceFetch)
        Future(() => context.expenseBloc.add(GetAllExpenseDepartments())),
      if (expenses.isEmpty || forceFetch)
        Future(() => context.expenseBloc.add(GetAllExpense(
              userRoles: userRoles,
              userEmail: userEmail,
            ))),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;

    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Expense Screen',
      ),
      floatingActionButton: CustomFloatingActionButton(
        foregroundColor: appColors.backgroundColor,
        onPressed: () {
          //
          Navigator.pushNamed(context, ScreenConstants.createExpense);
        },
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: () => _loadData(forceFetch: true),
        child: BlocConsumer<ExpenseBloc, ExpenseState>(
          listener: (context, state) {
            if (state is ExpenseApprovalLoadingState) {
              context.dialogCubit.showLoadingDialog();
            }

            if (state is ExpenseApprovalSuccessState) {
              context.dialogCubit.closeDialog();
              Navigator.pop(context);
              _loadData();
              SnackBarHelper.showSuccessSnackBar(
                context: context,
                message: state.message,
              );
            }

            if (state is ExpenseApprovalErrorState) {
              final errorMessage = state.appFailure.getErrorMessage();
              context.dialogCubit.closeDialog();
              Navigator.pop(context);
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }

            if (state is ExpenseErrorState) {
              //
              final errorMessage = state.appFailure.getErrorMessage();

              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }
          },
          builder: (context, state) {
            final expenses = context.read<ExpenseBloc>().expenses;
            bool isLoading = state is ExpenseLoadingState;
            bool isEmpty =
                state is ExpenseLoadedState && state.expenses.isEmpty;
            return CustomListGridView(
              onRefresh: () => _loadData(forceFetch: true),
              items: expenses,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              itemBuilder: (context, expense) {
                return ExpenseBuilderWidget(
                  expense: expense,
                  onTap: () {
                    //
                    showExpenseBottomSheet(
                      context: context,
                      expense: expense,
                      onEdit: () {
                        //
                        if (context.mounted) {
                          Navigator.pop(context);

                          // push the edit expense details screen
                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            // push the edit expense details screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    EditExpenseDetailsScreen(expense: expense),
                              ),
                            );
                          });
                        }
                      },
                      onApproval: () {
                        //
                        final userRoles =
                            context.usersBloc.currentUser?.roleNames ?? [];

                        context.expenseBloc.add(
                          ApproveExpense(
                            expenseId: expense.id,
                            userRoles: userRoles,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        ),
      ),
    );
  }

  void showExpenseBottomSheet({
    required BuildContext context,
    required ExpenseModel expense,
    required VoidCallback onApproval,
    required VoidCallback onEdit,
  }) {
    showModalBottomSheet(
      context: context,
      builder: (context) => ExpenseBottomSheet(
        expense: expense,
        onApproval: onApproval,
        onEdit: onEdit,
      ),
    );
  }
}

class ExpenseBottomSheet extends StatelessWidget {
  final ExpenseModel expense;
  final VoidCallback onApproval;
  final VoidCallback onEdit;

  const ExpenseBottomSheet({
    super.key,
    required this.expense,
    required this.onApproval,
    required this.onEdit,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final isDraft = expense.docStatus == 0;
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    final canApprove = UserRolesHelper().canViewAllExpenses(userRoles);
    return Container(
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Center(
                    child: Container(
                      height: 7.h,
                      width: 100.w,
                      margin: EdgeInsets.only(bottom: 10.h),
                      decoration: BoxDecoration(
                        color: appColors.dividerColor,
                        borderRadius: BorderRadius.circular(16.r),
                      ),
                    ),
                  ),
                  Center(
                    child: Text(
                      'Expense Details',
                      style: textTheme.titleLarge,
                    ),
                  ),
                  SizedBox(height: 10.h),
                  _buildExpenseDetails(
                    context: context,
                    title: 'Expense ID:',
                    value: expense.id,
                    icon: FontAwesomeIcons.receipt,
                  ),
                  _buildExpenseDetails(
                    context: context,
                    title: 'Department:',
                    value: expense.department?.name ?? '',
                    icon: FontAwesomeIcons.building,
                  ),
                  _buildExpenseDetails(
                    context: context,
                    title: 'Date:',
                    value: expense.formattedDate,
                    icon: FontAwesomeIcons.calendarDay,
                  ),
                  _buildExpenseDetails(
                    context: context,
                    title: 'Status:',
                    value: expense.status,
                    // ignore: deprecated_member_use
                    icon: FontAwesomeIcons.infoCircle,
                  ),
                  _buildExpenseDetails(
                    context: context,
                    title: 'Amount:',
                    value: expense.formattedAmount,
                    icon: FontAwesomeIcons.dollarSign,
                  ),
                  if (canApprove)
                    _buildExpenseDetails(
                      context: context,
                      title: 'Cost Center:',
                      value: expense.costCenter?.name ?? '',
                      icon: FontAwesomeIcons.chartPie,
                    ),
                  Divider(
                    color: appColors.dividerColor,
                    indent: 10.w,
                    endIndent: 10.w,
                  ),
                  if (expense.remark.trim().isNotEmpty) ...[
                    SizedBox(height: 10.h),
                    Center(
                      child: Text(
                        'Remarks',
                        style: textTheme.titleLarge,
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20.w),
                      child: Text(
                        expense.remark,
                        style: textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
          // Approve button or Edit and Approve based on the draft status
          if (canApprove && isDraft)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CustomButton(
                    buttonState: ButtonState.normal,
                    buttonText: 'Edit',
                    backgroundColor:
                        appColors.warningColor.withValues(alpha: 0.5),
                    leadingIcon: Icon(
                      FontAwesomeIcons.penToSquare,
                      color: appColors.backgroundColor,
                    ),
                    onTap: onEdit, // Edit functionality
                  ),
                ),
                SizedBox(width: 20.w),
                Expanded(
                  child: CustomButton(
                    buttonState: ButtonState.normal,
                    buttonText: 'Approve',
                    // onTap: onApproval,
                    onTap: () {
                      //
                      final isMissingRequiredFields =
                          expense.account == null || expense.paidFrom == null;
                      if (isMissingRequiredFields) {
                        // show a snack bar
                        SnackBarHelper.showErrorSnackBar(
                          context: context,
                          message: 'Please fill in all the required fields',
                          duration: const Duration(seconds: 10),
                          actionLabel: 'Edit',
                          onActionPressed: () {
                            onEdit();
                          },
                        );
                      }
                      //
                      else {
                        // call the onApproval callback
                        onApproval();
                      }
                    },
                  ),
                ),
              ],
            )
          else
            CustomButton.disabled(
              buttonState: ButtonState.disabled,
              buttonText: expense.status,
            ),
        ],
      ),
    );
  }

  Widget _buildExpenseDetails({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
  }) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(icon, color: appColors.iconColor),
              SizedBox(width: 5.w),
              Text(title,
                  style: textTheme.bodyMedium?.copyWith(
                    color: appColors.subtextColor,
                  )),
            ],
          ),
          Text(
            value,
            style: textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w900,
            ),
          ),
        ],
      ),
    );
  }
}
