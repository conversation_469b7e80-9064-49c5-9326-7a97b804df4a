import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/approval_decission.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/models/sales_invoice_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/approval_listview_widget.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart';

import '../../../core/bloc/sales invoice/sales_invoice_bloc.dart';

class ApprovalRequestScreen extends StatefulWidget {
  const ApprovalRequestScreen({super.key});

  @override
  State<ApprovalRequestScreen> createState() => _ApprovalRequestScreenState();
}

class _ApprovalRequestScreenState extends State<ApprovalRequestScreen> {
  @override
  void initState() {
    super.initState();
    _onGetSalesInvoices();
  }

  _onGetSalesInvoices() {
    final userRoles = context.usersBloc.currentUser?.roleNames ?? [];
    context.salesInvoiceBloc.add(GetSalesInvoices(userRoles: userRoles));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'Approval Request',
      ),
      body: BlocConsumer<SalesInvoiceBloc, SalesInvoiceState>(
        listener: (context, state) {
          if (state is SalesInvoiceError) {
            final appFailure = state.appFailure.getErrorMessage();
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: appFailure,
            );
          }

          if (state is SalesInvoiceUpdateLoading) {
            context.dialogCubit.showLoadingDialog();
          }

          if (state is SalesInvoiceUpdatedSuccessfully) {
            context.dialogCubit.closeDialog();
            _onGetSalesInvoices();
            final message = state.message;
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: message,
            );
          }

          if (state is SalesInvoiceUpdateError) {
            context.dialogCubit.closeDialog();
            final appFailure = state.appFailure.getErrorMessage();
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: appFailure,
            );
          }

          if (state is SalesInvoicePdfLoaded) {
            _onOpenPdf(state.pdfBytes);
          }

          if (state is SalesInvoicePdfError) {
            final appFailure = state.appFailure.getErrorMessage();
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: appFailure,
            );
          }
        },
        builder: (context, state) {
          final isLoading = state is SalesInvoiceLoading;
          final isPdfLoading = state is SalesInvoicePdfLoading;
          final isEmpty =
              state is SalesInvoiceLoaded && state.salesInvoices.isEmpty;
          final salesInvoices = context.salesInvoiceBloc.salesInvoices;
          return CustomListGridView<SalesInvoiceModel>(
            items: salesInvoices,
            isLoading: isLoading,
            isEmpty: isEmpty,
            layoutType: LayoutType.listView,
            onRefresh: () async {
              _onGetSalesInvoices();
            },
            itemBuilder: (context, salesInvoice) => ApprovalListviewWidget(
              salesInvoiceModel: salesInvoice,
              // isPdfLoading: isPdfLoading,
              isPdfLoading: state is SalesInvoicePdfLoading &&
                  state.salesInvoiceId == salesInvoice.id,
              onTapPdf: () {
                // _onOpenPdf(salesInvoice);
                context.salesInvoiceBloc.add(GetSalesInvoicePdf(
                  salesInvoiceId: salesInvoice.id,
                ));
              },
              onTapAction: (decision) {
                if (decision != null) {
                  context.dialogCubit.showConfirmDialog(
                    title: "Confirm",
                    message:
                        "Are you sure you want to ${decision.salesInvoiceApprovalDecision.toUpperCase()} this invoice?",
                    onConfirm: () {
                      _onUpdateSalesInvoiceWorkState(decision, salesInvoice);
                    },
                  );
                }
              },
            ),
          );
        },
      ),
    );
  }

  Future<void> _onUpdateSalesInvoiceWorkState(
      ApprovalDecision decision, SalesInvoiceModel salesInvoice) async {
    context.salesInvoiceBloc.add(UpdateSalesInvoiceWorkState(
      salesInvoiceId: salesInvoice.id,
      approvalDecision: decision,
      userRoles: context.usersBloc.currentUser?.roleNames ?? [],
    ));
  }

  Future<void> _onOpenPdf(Uint8List pdfBytes) async {
    try {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) =>
              ApprovalRequestPdfViewerScreen(pdfBytes: pdfBytes),
        ),
      );
    } catch (e, s) {
      print("Error opening pdf $e");
      print("Stack trace $s");
      SnackBarHelper.showErrorSnackBar(
        context: context,
        message: "Error opening PDF: ${e.toString()}",
      );
    }
  }
}

class ApprovalRequestPdfViewerScreen extends StatelessWidget {
  final Uint8List pdfBytes;
  const ApprovalRequestPdfViewerScreen({super.key, required this.pdfBytes});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(title: "Document View"),
      body: SfPdfViewer.memory(
        pdfBytes,
        onDocumentLoadFailed: (details) {
          print("Error loading pdf ${details.error}");
        },
        onDocumentLoaded: (details) {
          print("Pdf loaded ${details.document.pages}");
        },
      ),
    );
  }
}
