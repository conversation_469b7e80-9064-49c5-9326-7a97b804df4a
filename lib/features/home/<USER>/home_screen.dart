import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/task%20bloc/tasks_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/core/models/assigned_tasks_model.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/params/comment_screen_argument_params.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_tile.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_loading_skeleton.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_attendence_detail_widget.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_gridview.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_pending_requests_widget.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/home_user_detail_widget.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/task_listview_widget.dart';

import '../../../core/models/dashboard_model.dart';
import '../widgets/home_leave_balance_listview_widget.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
  }

  _loadData() async {
    await _loadUserData();
    await Future.wait(
      [
        _loadAssignedTasks(),
        _loadAllTasks(),
      ],
    );
  }

  Future<void> _loadUserData() async {
    // final profileImage = context.usersBloc.currentUser?.profileImage;
    // final employeeId = context.usersBloc.currentUser?.employeeId ?? '';
    if (context.usersBloc.currentUser == null) {
      if (!mounted) return;
      context.usersBloc.add(GetAuthUserEvent());
      await context.usersBloc.stream
          .firstWhere((state) => state is UsersStateDataLoaded);
    }
    // if (profileImage == null ||
    // profileImage.isEmpty ||
    // !profileImage.startsWith("http")) {
    // if (!mounted) return;
    // context.usersBloc.add(GetProfileImageEvent(employeeId: employeeId));
    // }
  }

  Future<void> _loadAssignedTasks() async {
    if (!mounted) return;
    if (context.tasksBloc.assignedTasks.isNotEmpty) return;
    final currentUserEmail = context.usersBloc.currentUser?.email ?? '';
    context.tasksBloc.add(GetAllAssignedTasksEvent(
      currentUserEmail: currentUserEmail,
    ));
  }

  Future<void> _loadAllTasks() async {
    if (!mounted) return;
    if (context.tasksBloc.tasks.isEmpty) {
      context.tasksBloc.add(GetAllTasksEvent(
        userRoles: context.usersBloc.currentUser?.roleNames ?? [],
      ));
    }
  }

  Future<void> onRefresh() async {
    final currentUser = context.usersBloc.currentUser;
    if (!mounted) return;
    context.tasksBloc.add(GetAllAssignedTasksEvent(
      currentUserEmail: currentUser?.email ?? '',
    ));
    if (!mounted) return;
    context.usersBloc.add(GetAuthUserEvent(forceFetch: true));
    final employeeId = context.usersBloc.currentUser?.employeeId ?? '';
    if (!mounted) return;
    context.read<UsersBloc>().add(GetProfileImageEvent(employeeId: employeeId));

    if (!mounted) return;
    context.read<UsersBloc>().add(GetDashboardEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  // Modularized the AppBar widget
  PreferredSizeWidget _buildAppBar() {
    return AnimatedAppBar(
      title: "RASIIN",
      actions: [
        BlocBuilder<UsersBloc, UsersState>(
          buildWhen: (previous, current) {
            return current is UsersStateDataLoaded ||
                current is UsersStateDataLoading ||
                current is UsersStateDataError ||
                current is UsersStateProfileImageFetched;
          },
          builder: (context, state) {
            final currentUser = context.usersBloc.currentUser;
            final userRole = currentUser?.roleNames ?? [];

            return Row(
              children: [
                // _buildNotificationButton(),
                if (UserRolesHelper().canShowApprovalButton(userRole)) ...[
                  // SizedBox(width: 15.w),
                  _buildApprovalButton(),
                ],
                SizedBox(width: 5.w),
                _buildHelpButton(),
              ],
            );
          },
        ),
      ],
    );
  }

  // Notification button
  Widget _buildNotificationButton() {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, ScreenConstants.notifcation);
      },
      child: _buildIconButton(ImageConstants.notification_png),
    );
  }

  // Approval button
  Widget _buildApprovalButton() {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, ScreenConstants.approval);
        // _loadUserData();
      },
      child: _buildIconButton(ImageConstants.approval_png),
    );
  }

  // Reusable method for building icon buttons
  Widget _buildIconButton(String assetPath) {
    final appColors = context.appColors;
    return SizedBox(
      height: 22.h,
      width: 22.w,
      child: Image.asset(
        assetPath,
        fit: BoxFit.cover,
        cacheWidth: (22.w * MediaQuery.of(context).devicePixelRatio).round(),
        cacheHeight: (22.h * MediaQuery.of(context).devicePixelRatio).round(),
        color: appColors.iconColor,
      ),
    );
  }

  // Help button
  Widget _buildHelpButton() {
    final appColors = context.appColors;
    return IconButton(
      onPressed: () {
        //
      },
      icon: Icon(
        Icons.help_outline_outlined,
        color: appColors.buttonColor,
        size: 30.w,
      ),
    );
  }

  // Body content
  Widget _buildBody() {
    return RefreshIndicator.adaptive(
      onRefresh: onRefresh,
      child: BlocConsumer<TasksBloc, TasksState>(
        listener: (context, state) {
          //
        },
        builder: (context, state) {
          final tasksBloc = context.tasksBloc;
          final List<AssignedTasksModel> assignedTasks =
              tasksBloc.assignedTasks;
          bool isEmpty = state is TasksStateAssignedTasksLoaded &&
              state.assignedTasks.isEmpty;

          bool isLoading = state is TasksStateAssignedTasksLoading;

          return isLoading
              ? HomeLoadingSkeleton(
                  loadingItems: [
                    SizedBox(
                      height: 15.h,
                    ),
                    LoadingSkeletonItem(layoutType: LayoutType.custom),
                    SizedBox(
                      height: 10.h,
                    ),
                    //
                    LoadingSkeletonItem(
                      layoutType: LayoutType.custom,
                      height: 200,
                    ),

                    //
                    LoadingSkeletonItem(layoutType: LayoutType.gridView),
                    SizedBox(height: 10.h),
                    //
                    LoadingSkeletonItem(
                      layoutType: LayoutType.custom,
                      height: 200,
                    ),
                  ],
                )
              : Padding(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 15.h),
                        _buildUserDetailSection(),
                        SizedBox(height: 25.h),
                        if (assignedTasks.isNotEmpty)
                          _buildTaskSection(assignedTasks, isEmpty),
                        _buildHomeGridView(),
                        SizedBox(
                          height: 25.h,
                        ),
                        _buildApprovalButtonSection(),
                        _buildPendingRequestSection(),
                        // _buildAttendanceSection(),
                        _buildLeaveBalanceSection(),
                        _buildSalaryDetailSection(),

                        // some space on bottom
                        if (!isLoading) SizedBox(height: 50.h),
                      ],
                    ),
                  ),
                );
        },
      ),
    );
  }

  // User Detail Section
  Widget _buildUserDetailSection() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 300),
      child: HomeUserDetailWidget(),
    );
  }

  // Task Section
  Widget _buildTaskSection(
      List<AssignedTasksModel> assignedTasks, bool isEmpty) {
    return Column(
      children: [
        _buildTasksHeader(),
        SizedBox(height: 20.h),
        _buildTaskListView(assignedTasks, isEmpty),
      ],
    );
  }

  // Task Header
  Widget _buildTasksHeader() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 600),
      child: CustomListTile(
        title: "Your Tasks",
        textStyle: context.textTheme.bodyLarge?.copyWith(
          color: context.appColors.buttonColor,
        ),
        onTap: () {
          Navigator.pushNamed(
            context,
            ScreenConstants.displayTask,
            arguments: true,
          );
        },
      ),
    );
  }

  // Task List View
  Widget _buildTaskListView(
      List<AssignedTasksModel> assignedTasks, bool isEmpty) {
    return isEmpty
        ? SizedBox()
        : SizedBox(
            height: 180.h,
            child: CustomListGridView(
              items: assignedTasks,
              isLoading: false,
              isEmpty: isEmpty,
              loadingItemHeight: 70,
              loadingItemCount: 10,
              showFooter: false,
              layoutType: LayoutType.listView,
              scrollDirection: Axis.horizontal,
              seperatedWidget: SizedBox(width: 15.w),
              padding: EdgeInsets.only(right: 20.w, top: 5.h, bottom: 15.h),
              itemBuilder: (context, task) {
                return TaskListviewWidget(
                  assignedTask: task,
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      ScreenConstants.changeTaskStatus,
                      arguments: task,
                    );
                  },
                  onComment: () {
                    Navigator.pushNamed(
                      context,
                      ScreenConstants.comments,
                      arguments: CommentScreenArgumentParams(
                        refrenceType: "ToDo",
                        taskId: task.name,
                      ),
                    );
                  },
                );
              },
              onRefresh: onRefresh,
            ),
          );
  }

  // Home GridView
  Widget _buildHomeGridView() {
    return AnimatedItemWrapper(
      delay: Duration(milliseconds: 900),
      child: BlocBuilder<UsersBloc, UsersState>(
        buildWhen: (previous, current) {
          return current is UsersStateDataLoaded ||
              current is UsersStateDataLoading ||
              current is UsersStateDataError ||
              current is UsersStateProfileImageFetched;
        },
        builder: (context, state) {
          final currentUser = context.usersBloc.currentUser;
          final userRole = currentUser?.roleNames ?? [];
          print("User has ${userRole.length} roles: $userRole");
          print("Real role is : ${currentUser?.roles}");

          //
          return HomeGridview(
            userRole: userRole,
          );
        },
      ),
    );
  }

  // Approval Button Section
  Widget _buildApprovalButtonSection() {
    return BlocBuilder<UsersBloc, UsersState>(
      buildWhen: (previous, current) {
        return current is UsersStateDataLoaded ||
            current is UsersStateDataLoading ||
            current is UsersStateDataError ||
            current is UsersStateProfileImageFetched;
      },
      builder: (context, state) {
        final currentUser = context.usersBloc.currentUser;
        final userRoles = currentUser?.roleNames ?? [];
        // if (!showApprovalButton(userRole)) {
        if (!UserRolesHelper().canShowApprovalButton(userRoles)) {
          return SizedBox.shrink();
        }
        return AnimatedItemWrapper(
          delay: Duration(milliseconds: 1200),
          child: CustomButton(
            buttonState: ButtonState.normal,
            width: double.infinity,
            buttonStyleType: ButtonStyleType.outline,
            isBorderActive: true,
            rippleColor: context.appColors.transparent,
            textStyle: Theme.of(context).textTheme.bodyLarge,
            buttonText: 'Approval Request',
            onTap: () {
              Navigator.pushNamed(context, ScreenConstants.approval);
            },
          ),
        );
      },
    );
  }

  // Pending Request Section
  Widget _buildPendingRequestSection() {
    return Column(
      children: [
        SizedBox(height: 25.h),
        HomePendingRequestWidget(),
      ],
    );
  }

  // Attendance Section
  Widget _buildAttendanceSection() {
    final dashboard = context.usersBloc.dashboardData;
    final attendenceSummary = dashboard?.attendanceSummary;
    final isEmpty =
        attendenceSummary == null || attendenceSummary.totalDays == 0;

    if (isEmpty) {
      return const SizedBox.shrink();
    }

    print(attendenceSummary.toJson());

    return Column(
      children: [
        SizedBox(height: 25.h),
        HomeAttendenceDetailsWidget(),
      ],
    );
  }

  // Leave Balance Section
  Widget _buildLeaveBalanceSection() {
    final leaveBalances = context.usersBloc.dashboardData?.leaveBalances ?? [];

    if (leaveBalances.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 25.h),
        Text(
          "Leave Balance",
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        _buildLeaveBalanceListView(),
      ],
    );
  }

  // Leave Balance List View
  Widget _buildLeaveBalanceListView() {
    final leaveBalances = context.usersBloc.dashboardData?.leaveBalances ?? [];

    return SizedBox(
      height: 200.h,
      child: leaveBalances.isEmpty
          ? SizedBox.shrink()
          : CustomListGridView(
              items: leaveBalances,
              itemCount: leaveBalances.length,
              isLoading: false,
              isEmpty: leaveBalances.isEmpty,
              layoutType: LayoutType.listView,
              scrollDirection: Axis.horizontal,
              showFooter: false,
              itemBuilder: (context, balance) {
                return HomeLeaveBalanceListviewWidegt(
                  leaveType: balance.leaveType,
                  remaining: balance.remaining,
                  used: balance.used,
                );
              },
              seperatedWidget: SizedBox(width: 20.w),
              onRefresh: () async {
                // Refresh leave balance data
                context.usersBloc.add(GetDashboardEvent());
              },
            ),
    );
  }

// Salary Detail Section
  Widget _buildSalaryDetailSection() {
    final salaryDetails = context.usersBloc.dashboardData?.salaryDetails;

    // If no salary data, return empty container or a message
    if (salaryDetails == null) {
      return const SizedBox.shrink();
      // OR return a message:
      // return Text("No salary data available", style: textTheme.bodyMedium);
    }

    if (salaryDetails.workingDays == 0 &&
        salaryDetails.netPay == 0 &&
        salaryDetails.grossPay == 0 &&
        salaryDetails.totalDeduction == 0 &&
        salaryDetails.processedDate.trim().isEmpty &&
        salaryDetails.salarySlipId.trim().isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        SizedBox(height: 25.h),
        CustomListTile(
          title: "Salary Details",
          onTap: () {
            Navigator.pushNamed(context, ScreenConstants.payroll);
          },
        ),
        SizedBox(height: 10.h),
        _buildSalaryDetails(salaryDetails),
      ],
    );
  }

// Salary Detail Widget - now takes SalaryDetails as parameter
  Widget _buildSalaryDetails(SalaryDetails salaryDetails) {
    return CustomContainer(
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      margin: EdgeInsets.symmetric(horizontal: 2.w, vertical: 2.h),
      width: MediaQuery.of(context).size.width,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSalaryMonthHeader(salaryDetails.monthYear),
          SizedBox(height: 10.h),
          Text(
            "\$${salaryDetails.netPay.toStringAsFixed(2)}",
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: context.appColors.primaryColor,
                  fontWeight: FontWeight.bold,
                ),
          ),
          SizedBox(height: 10.h),
          _buildSalaryCreatedRow(),
          SizedBox(height: 10.h),
          _buildSalaryDateRow(
            processedDate: salaryDetails.processedDate,
            workingDays: salaryDetails.workingDays,
          ),
          SizedBox(height: 30.h),
          _buildSalarySlipButton(),
        ],
      ),
    );
  }

// Salary Month Header - now takes monthYear as parameter
  Widget _buildSalaryMonthHeader(String monthYear) {
    final appColors = context.appColors;
    return CustomContainer(
      width: 100.w,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
      color: appColors.successColor.withValues(alpha: 0.1),
      border: Border.all(color: appColors.successColor),
      child: Center(
        child: Text(
          monthYear, // Use the monthYear from salaryDetails
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: appColors.successColor,
              ),
        ),
      ),
    );
  }

// Salary Created Row (unchanged)
  Widget _buildSalaryCreatedRow() {
    return Row(
      children: [
        Text("Created on", style: Theme.of(context).textTheme.labelMedium),
        SizedBox(width: 50.w),
        Text("Work day", style: Theme.of(context).textTheme.labelMedium),
      ],
    );
  }

// Salary Date Row - now takes parameters
  Widget _buildSalaryDateRow({
    required String processedDate,
    required int workingDays,
  }) {
    // Format the processed date (assuming it's in "YYYY-MM-DD" format)
    final formattedDate =
        processedDate.split('-').reversed.join('-'); // Converts to DD-MM-YYYY

    return Row(
      children: [
        Text(formattedDate, style: Theme.of(context).textTheme.bodyMedium),
        SizedBox(width: 50.w),
        Text("$workingDays", style: Theme.of(context).textTheme.bodyMedium),
      ],
    );
  }

  // View Salary Slip Button
  Widget _buildSalarySlipButton() {
    return Center(
      child: CustomButton(
        width: double.infinity,
        onTap: () {
          Navigator.pushNamed(context, ScreenConstants.payroll);
        },
        buttonText: "View Salary Slip",
        buttonState: ButtonState.normal,
      ),
    );
  }
}
