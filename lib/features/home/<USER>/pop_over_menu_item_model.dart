import 'package:flutter/material.dart';
import 'package:rasiin_tasks_app/core/constants/image_constants.dart';
import 'package:rasiin_tasks_app/core/enums/user_roles_enum.dart';
import 'package:rasiin_tasks_app/features/add%20task/screen/create_task_screen.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/navigation_animations_helper.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/apply_expense_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/apply_leave_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_issue_screen.dart';
import 'package:rasiin_tasks_app/features/home/<USER>/create_visit_screen.dart';

class PopOverMenuModel {
  final String title;
  final String assetPath;
  final Function(BuildContext context) onTap;
  final bool Function(List<String> userRoles) isVisible;

  PopOverMenuModel({
    required this.title,
    required this.assetPath,
    required this.onTap,
    required this.isVisible,
  });

  static List<PopOverMenuModel> getMenuItems() {
    return [
      PopOverMenuModel(
        title: "Apply Expense",
        assetPath: ImageConstants.money_png,
        onTap: (context) {
          Navigator.push(
            context,
            slideTransition(page: ApplyExpenseScreen()),
          );
        },
        isVisible: (userRoles) => true,
      ),
      PopOverMenuModel(
        title: "Apply Leave",
        assetPath: ImageConstants.order_png,
        onTap: (context) {
          Navigator.push(
            context,
            slideTransition(page: const ApplyLeaveScreen()),
          );
        },
        isVisible: (userRoles) => true,
      ),
      PopOverMenuModel(
        title: "Create Issue",
        assetPath: ImageConstants.attendance_png,
        onTap: (context) {
          Navigator.push(
            context,
            slideTransition(page: const CreateIssueScreen()),
          );
        },
        isVisible: (userRoles) => true,
      ),
      PopOverMenuModel(
        title: "Create Visit",
        assetPath: ImageConstants.beach_png,
        onTap: (context) {
          Navigator.push(
            context,
            slideTransition(page: const CreateVisitScreen()),
          );
        },
        isVisible: (userRoles) => UserRolesHelper().canViewAllVisits(userRoles),
      ),
      PopOverMenuModel(
        title: "Create Task",
        assetPath: ImageConstants.task_png,
        onTap: (context) {
          Navigator.push(
            context,
            slideTransition(page: const CreateTaskScreen()),
          );
        },
        isVisible: (userRoles) =>
            UserRolesHelper().canViewAndCreateTasks(userRoles),
      ),
    ];
  }
}
