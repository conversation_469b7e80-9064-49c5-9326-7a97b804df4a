import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/shape_type_enum.dart';
import 'package:rasiin_tasks_app/core/models/payroll_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_number_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class PayrollCard extends StatelessWidget {
  final PayrollModel payroll;
  final VoidCallback onDownload;
  final bool isDownloading;

  const PayrollCard({
    Key? key,
    required this.payroll,
    required this.onDownload,
    required this.isDownloading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final total = payroll.netPay > 0 ? payroll.netPay : 0.0001;
    // final earnings = payroll.earnings.fold(0.0, (sum, e) => sum + e.amount);
    // final deductions = payroll.deductions.fold(0.0, (sum, d) => sum + d.amount);
    final earnings = payroll.grossPay > 0 ? payroll.grossPay : 0.001;
    final deductions =
        payroll.totalDeduction > 0 ? payroll.totalDeduction : 0.001;
    // print("earning : $earnings, deductions:$deductions, total: $total");

    return CustomContainer(
      // margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart + Earnings / Deductions
          Row(
            children: [
              SizedBox(
                width: 120.w,
                height: 120.w,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    PieChart(
                      PieChartData(
                        sections: [
                          PieChartSectionData(
                            color: Colors.green,
                            value: earnings,
                            showTitle: false,
                            radius: 12.r,
                          ),
                          PieChartSectionData(
                            color: Colors.red,
                            value: deductions,
                            showTitle: false,
                            radius: 12.r,
                          ),
                        ],
                        centerSpaceRadius: 35.r,
                        sectionsSpace: 2,
                      ),
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          total.toMoneyString(),
                          style: textTheme.bodyLarge?.copyWith(
                            fontSize: 14,
                          ),
                        ),
                        Text('Net Pay',
                            style: textTheme.bodyLarge?.copyWith(
                              fontSize: 12,
                              fontWeight: FontWeight.normal,
                            )),
                      ],
                    ),
                  ],
                ),
              ),
              SizedBox(width: 16.w),
              // Summary rows
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _summaryRow(
                        context: context,
                        color: Colors.green,
                        label: 'Earnings',
                        amount: earnings.toMoneyString(symbol: 'USD ')),
                    SizedBox(height: 8.h),
                    _summaryRow(
                        context: context,
                        color: Colors.red,
                        label: 'Deductions',
                        amount: deductions.toMoneyString(symbol: 'USD ')),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Basic Info Section
          _sectionTitle('Basic'),
          _infoRow('Employee ID', payroll.employeeId),
          _infoRow('Employee Name', payroll.employeeName),
          _infoRow('Posting Date', payroll.postingDate.toFormattedString()),
          _infoRow('Start Date', payroll.startDate.toFormattedString()),
          _infoRow('End Date', payroll.endDate.toFormattedString()),

          // Earnings Section
          if (payroll.earnings.isNotEmpty) ...[
            _sectionTitle('Earnings'),
            ...payroll.earnings.map((e) => _infoRow(
                e.salaryComponent, e.amount.toMoneyString(symbol: 'USD '))),
          ],

          // Deductions Section
          if (payroll.deductions.isNotEmpty) ...[
            _sectionTitle('Deductions'),
            ...payroll.deductions.map((d) => _infoRow(
                d.salaryComponent, d.amount.toMoneyString(symbol: 'USD '))),
          ],

          SizedBox(height: 25.h),

          // Download button
          CustomButton(
            buttonText: "Download",
            buttonState:
                isDownloading ? ButtonState.loading : ButtonState.normal,
            onTap: onDownload,
          ),
        ],
      ),
    );
  }

  Widget _summaryRow({
    required BuildContext context,
    required Color color,
    required String label,
    required String amount,
  }) {
    final textTheme = context.textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            // Icon(Icons.circle, size: 10.r, color: color),
            CustomContainer(
              shapeType: ShapeType.circular,
              color: Colors.transparent,
              padding: EdgeInsets.all(10),
              width: 15,
              height: 10,
              border: Border.all(
                color: color,
                width: 4,
              ),
            ),
            SizedBox(width: 4.w),
            Text(
              amount,
              style: textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        SizedBox(height: 4.w),
        Padding(
          padding: const EdgeInsets.only(left: 15),
          child: Text(
            label,
            style: textTheme.bodySmall,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: Text(title,
          style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: Colors.blue)),
    );
  }

  Widget _infoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(child: Text(label, style: TextStyle(fontSize: 12.sp))),
          Text(value,
              style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }
}
