import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/core/bloc/expense bloc/expense_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/enums/selection_type_enum.dart';
import 'package:rasiin_tasks_app/core/models/account_model.dart';
import 'package:rasiin_tasks_app/core/models/cost_center_model.dart';
import 'package:rasiin_tasks_app/core/models/expense_model.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_selected_filed_displayer.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class EditExpenseDetailsScreen extends StatefulWidget {
  final ExpenseModel expense;
  const EditExpenseDetailsScreen({super.key, required this.expense});

  @override
  State<EditExpenseDetailsScreen> createState() =>
      _EditExpenseDetailsScreenState();
}

class _EditExpenseDetailsScreenState extends State<EditExpenseDetailsScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _remarkController =
      TextEditingController(text: widget.expense.remark);

  CostCenterModel? _selectedCostCenter;
  AccountModel? _selectedAccount;
  AccountModel? _selectedPaidFrom;

  Future<void> _loadData() async {
    final expenseBloc = context.expenseBloc;
    //
    expenseBloc.add(GetAllExpenseCostCenters());
    await expenseBloc.stream
        .firstWhere((state) => state is ExpenseCostCentersLoadedState);
    _selectedCostCenter = expenseBloc.costCenters
        .where((costCenter) => costCenter.id == widget.expense.costCenter?.id)
        .firstOrNull;
    expenseBloc.add(GetAllExpenseAccounts());
    await expenseBloc.stream
        .firstWhere((state) => state is ExpenseAccountsLoadedState);
    _selectedAccount = expenseBloc.accounts
        .where((account) => account.id == widget.expense.account?.id)
        .firstOrNull;

    _selectedPaidFrom = expenseBloc.accounts
        .where((account) => account.id == widget.expense.paidFrom?.id)
        .firstOrNull;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _loadData();
    });
  }

  @override
  void dispose() {
    // Disposing controllers when done
    _remarkController.dispose();
    _selectedAccount = null;
    _selectedPaidFrom = null;
    _selectedCostCenter = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Edit Expense Details'),
      ),
      body: BlocConsumer<ExpenseBloc, ExpenseState>(
        listener: (context, state) {
          if (state is ExpenseModificationLoadingState) {
            context.dialogCubit.showLoadingDialog();
          }

          if (state is ExpenseModificationSuccessState) {
            context.dialogCubit.closeDialog();
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: state.message,
            );

            // refresh the expense list
            context.expenseBloc.add(GetAllExpense(
              userRoles: context.usersBloc.currentUser?.roleNames ?? [],
              userEmail: context.usersBloc.currentUser?.email ?? '',
            ));

            // pop the screen
            Navigator.pop(context);
          }

          if (state is ExpenseModificationErrorState) {
            context.dialogCubit.closeDialog();
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: state.appFailure.message,
            );
          }
        },
        builder: (context, state) {
          return Form(
            key: _formKey,
            child: Padding(
              padding: EdgeInsets.all(16.r),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 20.h),

                    // Account field using CustomSelectFieldDisplayer
                    CustomSelectFieldDisplayer<AccountModel>(
                      leadingIcon: FontAwesomeIcons.moneyBillWave,
                      bottomSheetTitle: "Select Account",
                      options: context.expenseBloc.accounts,
                      displayItem: (account) => account.name,
                      displaySubTitle: (account) => account.id,
                      labelText: "Account",
                      selectionType: SelectionType.SingleSelection,
                      selectedItems:
                          _selectedAccount != null ? [_selectedAccount!] : [],
                      onSelectionChanged: (items) {
                        _selectedAccount = items.first;
                        setState(() {});
                      },
                      validator: (value) {
                        if (_selectedAccount == null) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 20.h),

                    // Paid From field using CustomSelectFieldDisplayer
                    CustomSelectFieldDisplayer<AccountModel>(
                      leadingIcon: FontAwesomeIcons.creditCard,
                      bottomSheetTitle: "Select Paid From",
                      options: context.expenseBloc.accounts,
                      displayItem: (account) => account.name,
                      displaySubTitle: (account) => account.id,
                      labelText: "Paid From",
                      selectionType: SelectionType.SingleSelection,
                      selectedItems:
                          _selectedPaidFrom != null ? [_selectedPaidFrom!] : [],
                      onSelectionChanged: (items) {
                        _selectedPaidFrom = items.first;
                        setState(() {});
                      },
                      validator: (value) {
                        if (_selectedPaidFrom == null) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 20.h),

                    // Cost Center field using CustomSelectFieldDisplayer
                    CustomDropDown<CostCenterModel>(
                      value: _selectedCostCenter,
                      displayItem: (costCenter) => costCenter?.name ?? '',
                      labelText: "Cost Center",
                      items: context.expenseBloc.costCenters,
                      onChanged: (value) {
                        _selectedCostCenter = value;
                        setState(() {});
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 20.h),

                    // Remark text field
                    CustomTextField(
                      controller: _remarkController,
                      labelText: "Remark",
                      textInputAction: TextInputAction.done,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'required!';
                        }
                        return null;
                      },
                    ),
                    SizedBox(height: 50.h),

                    // Save Changes Button
                    CustomButton(
                      buttonState: ButtonState.normal,
                      buttonText: 'Save Changes',
                      onTap: () {
                        if (_formKey.currentState!.validate()) {
                          context.expenseBloc.add(ModifyExpense(
                            expenseId: widget.expense.id,
                            account: _selectedAccount!.id,
                            paidFrom: _selectedPaidFrom!.id,
                            costCenter: _selectedCostCenter!.id,
                            remark: _remarkController.text.trim(),
                            userRoles:
                                context.usersBloc.currentUser?.roleNames ?? [],
                          ));
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
