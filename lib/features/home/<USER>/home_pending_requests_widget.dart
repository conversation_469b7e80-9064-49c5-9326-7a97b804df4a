import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

class HomePendingRequestWidget extends StatelessWidget {
  const HomePendingRequestWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = context.textTheme;
    final appColors = context.appColors;
    final pendingExpenses = context.usersBloc.dashboardData?.pendingExpenses;
    final latestRequest = pendingExpenses?.latestRequest;

    // If no pending expenses, return empty container or a message
    if (pendingExpenses == null || pendingExpenses.totalPending == 0) {
      return const SizedBox.shrink();
      // OR return a message:
      // return Text("No pending expenses", style: textTheme.bodyMedium);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Pending requests",
              style: textTheme.titleSmall,
            ),
            if (pendingExpenses.totalPending > 1)
              Text(
                "${pendingExpenses.totalPending - 1} more",
                style: textTheme.bodySmall?.copyWith(
                  color: appColors.subtextColor,
                ),
              ),
          ],
        ),
        SizedBox(height: 10.h),
        CustomContainer(
          margin: EdgeInsets.symmetric(horizontal: 2.w),
          width: MediaQuery.of(context).size.width,
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 12.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (latestRequest != null) ...[
                CustomSmallContainer(
                  text: latestRequest.type.isNotEmpty
                      ? latestRequest.type
                      : 'Expense',
                  fontSize: 14.sp,
                  textColor: appColors.primaryColor,
                  backgroundColor:
                      appColors.primaryColor.withValues(alpha: 0.1),
                  verticalPadding: 4,
                  horizontalPadding: 8,
                ),
                SizedBox(height: 10.h),
                Text(
                  "\$${latestRequest.claimedAmount.toStringAsFixed(2)}",
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 6.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      latestRequest.date,
                      style: textTheme.bodySmall?.copyWith(
                        color: appColors.subtextColor,
                      ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                      decoration: BoxDecoration(
                        color: latestRequest.status == "Pending"
                            ? appColors.warningColor.withOpacity(0.1)
                            : appColors.successColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                      child: Text(
                        latestRequest.status,
                        style: textTheme.bodySmall?.copyWith(
                          color: latestRequest.status == "Pending"
                              ? appColors.warningColor
                              : appColors.successColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
