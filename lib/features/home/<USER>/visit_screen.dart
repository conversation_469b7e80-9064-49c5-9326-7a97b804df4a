import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/models/visit_model.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/layout_type.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_floating_action_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_list_grid_view.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_small_container.dart';

import '../../../core/bloc/visit bloc/visit_bloc.dart';

class VisitScreen extends StatefulWidget {
  const VisitScreen({super.key});

  @override
  State<VisitScreen> createState() => _VisitScreenState();
}

class _VisitScreenState extends State<VisitScreen> {
  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    await Future.wait([
      Future(() => context.read<VisitBloc>().add(GetAllVisitsEvent(
            employee: context.usersBloc.currentUser?.email ?? '',
            userRoles: context.usersBloc.currentUser?.roleNames ?? [],
          ))),
      Future(() => context.read<VisitBloc>().add(GetAllVisitTypesEvent())),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Scaffold(
      floatingActionButton: CustomFloatingActionButton(
        foregroundColor: appColors.backgroundColor,
        onPressed: () {
          //
          Navigator.pushNamed(context, ScreenConstants.createVisit);
        },
      ),
      appBar: AnimatedAppBar(
        title: "Visit",
      ),
      body: RefreshIndicator.adaptive(
        onRefresh: () => _loadData(),
        child: BlocConsumer<VisitBloc, VisitState>(
          listener: (context, state) {
            if (state is VisitErrorState) {
              //
              final errorMessage = state.appFailure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }
            if (state is VisitTypeErrorState) {
              //
              final errorMessage = state.appFailure.getErrorMessage();
              SnackBarHelper.showErrorSnackBar(
                context: context,
                message: errorMessage,
              );
            }
            if (state is VisitSuccessState) {
              //
              SnackBarHelper.showSuccessSnackBar(
                context: context,
                message: state.message,
              );
            }
            if (state is VisitTypeSuccessState) {
              //
              SnackBarHelper.showSuccessSnackBar(
                context: context,
                message: state.message,
              );
            }
          },
          builder: (context, state) {
            final visits = context.read<VisitBloc>().visits;
            bool isLoading =
                state is VisitLoadingState || state is VisitTypeLoadingState;
            bool isEmpty = state is VisitLoadedState && state.visits.isEmpty;

            return CustomListGridView(
              items: visits,
              isLoading: isLoading,
              isEmpty: isEmpty,
              layoutType: LayoutType.listView,
              itemBuilder: (context, VisitModel visit) => VisitBuilderWidget(
                visit: visit,
                onTap: () {
                  //
                },
              ),
              onRefresh: () => _loadData(),
            );
            // if (state is VisitLoadingState) {
            //   return IssueLoadingPlaceholder();
            // }
            // if (visits.isEmpty) {
            //   return SingleChildScrollView(
            //     physics: const AlwaysScrollableScrollPhysics(),
            //     child: SizedBox(
            //       height: MediaQuery.of(context).size.height,
            //       child: Center(
            //         child: EmptyTasksWidget(
            //           message: "No visits available.",
            //         ),
            //       ),
            //     ),
            //   );
            // }
            // return ListView.separated(
            //   itemCount: visits.length + 1,
            //   shrinkWrap: true,
            //   physics: const AlwaysScrollableScrollPhysics(),
            //   padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 20.h),
            //   itemBuilder: (context, index) {
            //     if (index == visits.length) {
            //       // footer
            //       return CustomFooter();
            //     }
            //     final visit = visits[index];
            //     return CustomAnimatedItem(
            //       index: index,
            //       itemCount: visits.length,
            //       animationDirection: index % 2 == 0
            //           ? AnimationDirection.bottomToTop
            //           : AnimationDirection.rightToLeft,
            //       child: VisitBuilderWidget(
            //         visit: visit,
            //         onTap: () {
            //           //
            //         },
            //       ),
            //     );
            //   },
            //   separatorBuilder: (context, index) => SizedBox(height: 10.h),
            // );
          },
        ),
      ),
    );
  }
}

class VisitBuilderWidget extends StatelessWidget {
  final void Function()? onTap;
  final VisitModel visit;

  const VisitBuilderWidget({
    super.key,
    this.onTap,
    required this.visit,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    return GestureDetector(
      onTap: onTap,
      child: CustomContainer(
        padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        visit.name,
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.primaryColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        "17-10-2024- 03:56:17",
                        style: textTheme.bodyMedium?.copyWith(
                          color: appColors.subtextColor.withValues(alpha: 0.5),
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                CustomSmallContainer(
                  text: visit.visitType,
                ),
              ],
            ),
            SizedBox(height: 15.h),
            Text(
              "Customer name",
              style: textTheme.bodyMedium?.copyWith(
                color: appColors.subtextColor.withValues(alpha: 0.5),
              ),
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              visit.customerName,
              style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
