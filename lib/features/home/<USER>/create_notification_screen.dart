import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/notification%20bloc/notification_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class CreateNotificationScreen extends StatefulWidget {
  const CreateNotificationScreen({super.key});

  @override
  State<CreateNotificationScreen> createState() =>
      _CreateNotificationScreenState();
}

class _CreateNotificationScreenState extends State<CreateNotificationScreen> {
  final GlobalKey<FormState> _notificationFormKey = GlobalKey<FormState>();
  final TextEditingController _fcmController = TextEditingController();
  final TextEditingController _userIDController = TextEditingController();
  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();

  _getFcmToken() async {
    final token = await FirebaseMessaging.instance.getToken() ?? '';
    setState(() {
      _fcmController.text = token;
    });
  }

  @override
  void initState() {
    super.initState();
    _getFcmToken();
  }

  @override
  void dispose() {
    super.dispose();
    _fcmController.dispose();
    _userIDController.dispose();
    _titleController.dispose();
    _messageController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final notificationBloc = context.notificationBloc;
    return Scaffold(
      appBar: AnimatedAppBar(
        title: "Send Notification",
      ),
      body: BlocConsumer<NotificationBloc, NotificationState>(
        listener: (context, state) {
          if (state is NotificationStateError) {
            final errorMessage = state.appFailure.message;
            SnackBarHelper.showErrorSnackBar(
              context: context,
              message: errorMessage,
            );
          }
          if (state is NotificationStateSuccess) {
            SnackBarHelper.showSuccessSnackBar(
              context: context,
              message: state.message,
            );
            _fcmController.clear();
            _userIDController.clear();
            _titleController.clear();
            _messageController.clear();
            // _notificationFormKey.currentState!.reset();
            Navigator.pop(context);
          }
        },
        builder: (context, state) {
          return Container(
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.h),
            child: Form(
              key: _notificationFormKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 20.h,
                    ),
                    CustomTextField(
                      controller: _userIDController,
                      labelText: "User ID",
                      isObsecureText: false,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    CustomTextField(
                      controller: _fcmController,
                      labelText: "Fcm Token",
                      isObsecureText: false,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    CustomTextField(
                      controller: _titleController,
                      labelText: "Title",
                      isObsecureText: false,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: 20.h,
                    ),
                    CustomTextField(
                      controller: _messageController,
                      labelText: "Message",
                      isObsecureText: false,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'required';
                        }
                        return null;
                      },
                    ),
                    SizedBox(
                      height: 40.h,
                    ),
                    CustomButton(
                      buttonState: state is NotificationStateLoading
                          ? ButtonState.loading
                          : ButtonState.normal,
                      buttonText: 'Submit',
                      onTap: () {
                        //
                        if (_notificationFormKey.currentState!.validate()) {
                          //
                          notificationBloc.add(
                            SendNotificationEvent(
                              title: [
                                _titleController.text.trim(),
                              ],
                              message: _messageController.text,
                              fcmToken: [
                                _fcmController.text.trim(),
                              ],
                              userId: _userIDController.text,
                            ),
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
