// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_date_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';

import 'package:rasiin_tasks_app/features/profile/widgets/custom_user_detail_widget.dart';

class UserInformationWidget extends StatelessWidget {
  final UserModel? user;

  const UserInformationWidget({
    required this.user,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    return Container(
      width: MediaQuery.of(context).size.width.w,
      margin: EdgeInsets.all(2.w),
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15.r),
        color: appColors.transparent,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          //! custom widget
          if (user?.employeeId.isNotEmpty ?? false)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              animationDirection: AnimationDirection.rightToLeft,
              child: CustomUserDetailWidget(
                title: "Employee ID",
                subtitle: user?.employeeId ?? '',
              ),
            ),
          if (user?.dateOfJoining != null)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 600),
              animationDirection: AnimationDirection.leftToRight,
              child: CustomUserDetailWidget(
                title: "Date of joining",
                subtitle: user?.dateOfJoining?.toFormattedString() ?? '',
              ),
            ),
          if (user?.birthDate != null)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              animationDirection: AnimationDirection.rightToLeft,
              child: CustomUserDetailWidget(
                title: "Date of birth",
                subtitle: user?.birthDate?.toFormattedString() ?? '',
              ),
            ),
          if (user?.gender != null)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 600),
              animationDirection: AnimationDirection.leftToRight,
              child: CustomUserDetailWidget(
                title: "Gender",
                subtitle: user?.gender ?? '',
              ),
            ),
          if (user?.email.isNotEmpty ?? false)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              animationDirection: AnimationDirection.rightToLeft,
              child: CustomUserDetailWidget(
                title: "Personal email address",
                subtitle: user?.email ?? 'no email address',
              ),
            ),
          if (user?.employeeNo.isNotEmpty ?? false)
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 600),
              animationDirection: AnimationDirection.leftToRight,
              child: CustomUserDetailWidget(
                title: "Contact Number",
                subtitle: user?.employeeNo ?? 'no contact number',
              ),
            ),
        ],
      ),
    );
  }
}
