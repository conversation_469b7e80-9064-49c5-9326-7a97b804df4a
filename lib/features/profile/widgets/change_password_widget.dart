import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_container.dart';

class ChangePasswordWidget extends StatelessWidget {
  const ChangePasswordWidget({
    super.key,
    this.title = 'ChangePassowrd',
    this.trailingIcon = Icons.arrow_forward_ios,
    // ignore: deprecated_member_use
    this.leadingIcon = FontAwesomeIcons.lock,
    required this.onTap,
    this.trailingWidget,
    this.color,
  });

  final String title;
  final IconData leadingIcon;
  final IconData trailingIcon;
  final Function()? onTap;
  final Widget? trailingWidget;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;

    final iconColor = appColors.iconColor;
    return CustomContainer(
      margin: EdgeInsets.all(1.w),
      color: appColors.transparent,
      child: ListTile(
        onTap: onTap,
        splashColor: appColors.transparent,
        hoverColor: appColors.transparent,
        leading: FaIcon(
          leadingIcon,
          color: color ?? iconColor,
        ),
        title: Text(
          title,
          style: textTheme.bodyLarge?.copyWith(
            color: color,
          ),
        ),
        trailing: trailingWidget != null
            ? trailingWidget
            : Icon(
                trailingIcon,
                color: color ?? iconColor,
              ),
      ),
    );
  }
}
