import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomUserDetailWidget extends StatelessWidget {
  const CustomUserDetailWidget(
      {super.key, required this.title, required this.subtitle});

  final String title;
  final String subtitle;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: textTheme.labelLarge,
        ),
        SizedBox(
          height: 4.h,
        ),
        Text(
          subtitle,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 5.h,
        ),
        const Divider(),
        <PERSON><PERSON><PERSON><PERSON>(
          height: 8.h,
        ),
      ],
    );
  }
}
