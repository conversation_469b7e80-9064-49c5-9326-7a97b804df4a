import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/enums/button_state.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_textfield.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final forgotPasswordFormKey = GlobalKey<FormState>();
  final passwordController = TextEditingController();

  @override
  void dispose() {
    super.dispose();
    passwordController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final dialogCubit = context.dialogCubit;

    return Scaffold(
      appBar: AnimatedAppBar(
        title: "Change password",
      ),
      body: BlocConsumer<UsersBloc, UsersState>(
        listener: (context, state) {
          if (state is UsersStateError) {
            final errorMessage = state.appFailure.getErrorMessage();

            dialogCubit.showErrorDialog(
              message: errorMessage,
            );
          }
          if (state is UsersStateLoading) {
            dialogCubit.showLoadingDialog();
          }
          if (state is UsersStateUsersSuccess) {
            passwordController.clear();
            dialogCubit.showSuccessDialog(
              message: state.message,
              onConfirm: () {
                Navigator.of(context).pop();
              },
            );
          }
        },
        builder: (context, state) {
          final isLoading = state is UsersStateLoading;
          return Form(
            key: forgotPasswordFormKey,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 30.w),
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 100.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 300),
                    animationDirection: AnimationDirection.leftToRight,
                    child: CustomTextField(
                      controller: passwordController,
                      labelText: "New Password",
                      isObsecureText: true,
                      isPasswordField: true,
                      validator: (value) {
                        //
                        if (value == null || value.isEmpty) {
                          return 'Please enter your new password';
                        }
                        if (value.length < 3) {
                          return 'password cannot less than 3 characteristics long.';
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(height: 30.h),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 500),
                    animationDirection: AnimationDirection.rightToLeft,
                    child: CustomButton(
                      buttonState:
                          isLoading ? ButtonState.loading : ButtonState.normal,
                      buttonText: 'Change password',
                      onTap: () {
                        //!!!!!!!!!!!!!!!!!
                        if (forgotPasswordFormKey.currentState!.validate()) {
                          context.usersBloc.add(
                            ChangeUserPasswordEvent(
                              newPassword: passwordController.text.trim(),
                              email: context.usersBloc.currentUser?.email ?? '',
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
