import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image_picker/image_picker.dart';
import 'package:rasiin_tasks_app/core/bloc/authentication%20bloc/authentication_bloc.dart';
import 'package:rasiin_tasks_app/core/bloc/users%20bloc/users_bloc.dart';
import 'package:rasiin_tasks_app/core/models/user_model.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/constants/screen_constants.dart';
import 'package:rasiin_tasks_app/core/enums/animation_direction_enum.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/app_bloc_extenstions.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_item_wrapper.dart';
import 'package:rasiin_tasks_app/core/utils/helpers/snack_bar_helper.dart';
import 'package:rasiin_tasks_app/features/profile/widgets/user_information_widget.dart';

import '../../common/widgets/custom_image_picker_card.dart';
import '../widgets/change_password_widget.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPersistentFrameCallback(
      (_) {
        _loadUserData();
      },
    );
  }

  _loadUserData() async {
    if (!mounted) return;
    final userBloc = context.read<UsersBloc>();
    final employeeId = userBloc.currentUser?.employeeId;
    if (userBloc.currentUser == null) {
      userBloc.add(GetAuthUserEvent());
      await userBloc.stream
          .firstWhere((state) => state is UsersStateDataLoaded);
    }
    // final userProfileImage = userBloc.currentUser?.profileImage;
    // if (userProfileImage == null ||
    //     userProfileImage.isEmpty ||
    //     !userProfileImage.startsWith("http")) {
    //   userBloc.add(GetProfileImageEvent(employeeId: employeeId ?? ''));
    // }
  }

  @override
  Widget build(BuildContext context) {
    final userBloc = context.read<UsersBloc>();
    return Scaffold(
      appBar: AnimatedAppBar(
        title: 'My Profile',
      ),
      body: BlocConsumer<UsersBloc, UsersState>(
        listener: (context, state) {
          // ----
        },
        builder: (context, state) {
          final UserModel? currentUser = context.usersBloc.currentUser;
          return _buildProfileView(context, currentUser, userBloc);
        },
      ),
    );
  }

  Widget _buildProfileView(
      BuildContext context, UserModel? currentUser, UsersBloc userBloc) {
    final appColors = context.appColors;
    final textTheme = context.textTheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    final employeeId = context.usersBloc.currentUser?.employeeId ?? '';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 10.h),
            Container(
              margin: EdgeInsets.all(1.w),
              width: MediaQuery.of(context).size.width.w,
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // const ProfileImageWidget(),
                  BlocListener<UsersBloc, UsersState>(
                    listener: (context, state) {
                      if (state is UsersStateProfileImageUpdated) {
                        SnackBarHelper.showSuccessSnackBar(
                          context: context,
                          message: state.message,
                        );
                        userBloc
                            .add(GetProfileImageEvent(employeeId: employeeId));
                      }

                      if (state is UsersStateError) {
                        final errorMessage = state.appFailure.getErrorMessage();
                        SnackBarHelper.showErrorSnackBar(
                          context: context,
                          message: errorMessage,
                        );
                      }
                      if (state is UsersStateProfileImageError) {
                        SnackBarHelper.showErrorSnackBar(
                          context: context,
                          message: state.message,
                        );
                        Navigator.pop(context);
                      }

                      if (state is UsersStateProfileImagePicked) {
                        Navigator.pop(context);
                        userBloc.add(
                            ChangeProfileImageEvent(employeeId: employeeId));
                      }
                    },
                    child: AnimatedItemWrapper(
                      delay: Duration(milliseconds: 1200),
                      animationDirection: AnimationDirection.leftToRight,
                      child: CustomImagePickerCard(
                        radius: 40,
                        imageUrl: currentUser?.profileImage,
                        isLoading: false,
                        imageFile: userBloc.profileImage,
                        isProfile: true,
                        showIcon: true,
                        userName: currentUser?.employeeName,
                        onCamera: () {
                          userBloc.add(PickProfileImageEvent(
                            imageSource: ImageSource.camera,
                          ));

                          // if (Navigator.canPop(context)) {
                          //   Navigator.pop(context);
                          // }
                        },
                        onGallery: () {
                          userBloc.add(PickProfileImageEvent());
                          // if (Navigator.canPop(context)) {
                          //   Navigator.pop(context);
                          // }
                        },
                      ),
                    ),
                  ),
                  AnimatedItemWrapper(
                    delay: Duration(milliseconds: 300),
                    child: SizedBox(
                      width: 20.h,
                    ),
                  ),
                  Expanded(
                    child: AnimatedItemWrapper(
                      delay: Duration(milliseconds: 700),
                      animationDirection: AnimationDirection.rightToLeft,
                      child: Text(
                        currentUser?.employeeName ?? 'No Name',
                        style: textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        maxLines: 1,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              child: SizedBox(
                height: 10.h,
              ),
            ),
            UserInformationWidget(user: currentUser),
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              child: SizedBox(
                height: 5.h,
              ),
            ),
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 700),
              animationDirection: AnimationDirection.bottomToTop,
              child: ChangePasswordWidget(
                title: "Dark mode",
                leadingIcon: Icons.dark_mode,
                onTap: () {},
                trailingWidget: Switch.adaptive(
                  value: isDarkMode,
                  onChanged: (value) {
                    context.usersBloc.add(ToggleUserThemeEvent());
                  },
                ),
              ),
            ),
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              child: SizedBox(
                height: 15.h,
              ),
            ),
            ChangePasswordWidget(
              title: "Change password",
              // ignore: deprecated_member_use
              leadingIcon: FontAwesomeIcons.userLock,
              onTap: () {
                Navigator.pushNamed(context, ScreenConstants.changePassword);
              },
            ),
            AnimatedItemWrapper(
              delay: Duration(milliseconds: 300),
              child: SizedBox(
                height: 15.h,
              ),
            ),
            BlocListener<AuthenticationBloc, AuthenticationState>(
              listener: (context, state) {
                if (state is AuthenticationStateInitial) {
                  Navigator.pushReplacementNamed(
                    context,
                    ScreenConstants.login,
                  );
                }
              },
              child: ChangePasswordWidget(
                title: "Logout",
                // ignore: deprecated_member_use
                leadingIcon: FontAwesomeIcons.signOut,
                color: appColors.errorColor,
                onTap: () {
                  context.dialogCubit.showWarningDialog(
                    title: "Logout",
                    message: "Do you really want to logout?",
                    barrierDismissible: true,
                    onConfirm: () {
                      context.authenticationBloc
                          .add(AuthenticationLogoutEvent());
                      context.tasksBloc.assignedTasks.clear();
                      context.usersBloc.currentUser = null;
                      // Navigator.pushReplacementNamed(
                      //   context,
                      //   ScreenConstants.login,
                      // );
                    },
                  );
                },
              ),
            ),
            SizedBox(height: 50.h),
          ],
        ),
      ),
    );
  }
}
