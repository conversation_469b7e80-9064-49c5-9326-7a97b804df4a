import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:rasiin_tasks_app/app/theme/colors/app_colors.dart';
import 'package:rasiin_tasks_app/core/enums/button_style_type.dart';
import 'package:rasiin_tasks_app/core/models/appointment_model.dart';
import 'package:rasiin_tasks_app/core/enums/encounter_enums.dart';
import 'package:rasiin_tasks_app/core/utils/extensions/build_context_extensions.dart';
import 'package:rasiin_tasks_app/features/common/widgets/animations/animated_app_bar.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_button.dart';
import 'package:rasiin_tasks_app/features/common/widgets/custom_drop_down.dart';

import '../../../core/enums/button_state.dart';
import '../../common/widgets/custom_textfield.dart';

class OpenEncounterScreen extends StatefulWidget {
  final AppointmentModel? appointment;

  const OpenEncounterScreen({
    super.key,
    required this.appointment,
  });

  @override
  State<OpenEncounterScreen> createState() => _OpenEncounterScreenState();
}

class _OpenEncounterScreenState extends State<OpenEncounterScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final _formKey = GlobalKey<FormState>();
  final _symptomsController = TextEditingController();
  final _diagnosisController = TextEditingController();
  final _instructionsController = TextEditingController();

  EncounterType? _selectedEncounterType;
  List<String> _selectedLabTests = [];
  List<String> _selectedProcedures = [];
  List<Map<String, dynamic>> _prescriptions = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AnimatedAppBar(
        appBarHeight: 120,
        title: 'Patient Encounter',
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Basic Info'),
            Tab(text: 'Lab Tests'),
            Tab(text: 'Procedures'),
            Tab(text: 'Prescriptions'),
          ],
        ),
      ),
      // body: Form(
      //   key: _formKey,
      //   child: TabBarView(
      //     controller: _tabController,
      //     children: [
      //       _buildBasicInfoTab(),
      //       _buildLabTestsTab(),
      //       _buildProceduresTab(),
      //       _buildPrescriptionsTab(),
      //     ],
      //   ),
      // ),
      body: Form(
        key: _formKey,
        child:
            // KeyboardAttached(
            //   child:
            Column(
          children: [
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBasicInfoTab(),
                  _buildLabTestsTab(),
                  _buildProceduresTab(),
                  _buildPrescriptionsTab(),
                ],
              ),
            ),
            _buildBottomActions(),
          ],
        ),
        // ),
      ),
      // bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Patient Info Card
          Card(
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Patient Information',
                    style: context.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text('Name: ${widget.appointment?.patientName}'),
                  Text('ID: ${widget.appointment?.patient}'),
                  Text(
                      'Appointment Time: ${widget.appointment?.formattedTime}'),
                ],
              ),
            ),
          ),
          SizedBox(height: 16.h),

          // Encounter Type
          CustomDropDown<EncounterType>(
            items: EncounterType.values,
            labelText: "Select Encounter Type",
            onChanged: (type) => setState(() => _selectedEncounterType = type),
            displayItem: (type) => type?.displayName,
          ),
          SizedBox(height: 16.h),

          // Symptoms
          CustomTextField(
            controller: _symptomsController,
            labelText: 'Symptoms',
            hintText: 'Enter patient symptoms...',
            maxLine: 3,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter symptoms';
              }
              return null;
            },
          ),
          SizedBox(height: 16.h),

          // Diagnosis
          CustomTextField(
            controller: _diagnosisController,
            labelText: 'Diagnosis',
            hintText: 'Enter diagnosis...',
            maxLine: 3,
            validator: (value) {
              if (value?.isEmpty ?? true) {
                return 'Please enter diagnosis';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  Widget _buildLabTestsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Lab Tests',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // Common Lab Tests
          ...[
            'CBC',
            'Blood Sugar',
            'Lipid Profile',
            'Liver Function',
            'Kidney Function',
            'Thyroid Function',
            'Urine Analysis',
            'ECG',
            'X-Ray Chest'
          ].map(
            (test) => CheckboxListTile(
              title: Text(test),
              value: _selectedLabTests.contains(test),
              onChanged: (bool? value) {
                setState(() {
                  if (value == true) {
                    _selectedLabTests.add(test);
                  } else {
                    _selectedLabTests.remove(test);
                  }
                });
              },
            ),
          ),

          SizedBox(height: 16.h),
          CustomTextField(
            controller: _instructionsController,
            labelText: 'Lab Instructions',
            hintText: 'Special instructions for lab...',
            maxLine: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildProceduresTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Procedures',
            style: context.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16.h),

          // Common Procedures
          ...[
            'Ultrasound',
            'CT Scan',
            'MRI',
            'Endoscopy',
            'Colonoscopy',
            'Biopsy',
            'Minor Surgery'
          ].map(
            (procedure) => CheckboxListTile(
              title: Text(procedure),
              value: _selectedProcedures.contains(procedure),
              onChanged: (bool? value) {
                setState(() {
                  if (value == true) {
                    _selectedProcedures.add(procedure);
                  } else {
                    _selectedProcedures.remove(procedure);
                  }
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrescriptionsTab() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Prescriptions',
                style: context.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              CustomButton(
                buttonState: ButtonState.normal,
                buttonText: 'Add Medicine',
                width: 120,
                height: 35,
                onTap: _addPrescription,
              ),
            ],
          ),
          SizedBox(height: 16.h),

          // Prescription List
          if (_prescriptions.isEmpty)
            Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 40.h),
                child: Text(
                  'No prescriptions added yet.\nTap "Add Medicine" to get started.',
                  textAlign: TextAlign.center,
                  style: context.textTheme.bodyMedium?.copyWith(
                    color:
                        context.textTheme.bodyMedium?.color?.withOpacity(0.6),
                  ),
                ),
              ),
            )
          else
            ..._prescriptions.asMap().entries.map(
                  (entry) => Card(
                    margin: EdgeInsets.only(bottom: 8.h),
                    child: ListTile(
                      title: Text(entry.value['medicine'] ?? ''),
                      subtitle: Text(
                        '${entry.value['dosage']} - ${entry.value['frequency']}\nDuration: ${entry.value['duration']}',
                      ),
                      trailing: IconButton(
                        icon: const Icon(Icons.delete),
                        onPressed: () => _removePrescription(entry.key),
                      ),
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Material(
      elevation: 8.0,
      color: context.appColors.surfaceColor,
      child: Container(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Expanded(
              child: CustomButton(
                buttonState: ButtonState.normal,
                buttonStyleType: ButtonStyleType.disabled,
                buttonText: 'Cancel',
                onTap: () => Navigator.pop(context),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: CustomButton(
                buttonState: ButtonState.normal,
                buttonText: 'Complete Encounter',
                onTap: _completeEncounter,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addPrescription() {
    // Show dialog to add prescription
    showDialog(
      context: context,
      builder: (context) => _PrescriptionDialog(
        onAdd: (prescription) {
          setState(() {
            _prescriptions.add(prescription);
          });
        },
      ),
    );
  }

  void _removePrescription(int index) {
    setState(() {
      _prescriptions.removeAt(index);
    });
  }

  void _completeEncounter() {
    // Complete and submit encounter
    if (_formKey.currentState!.validate()) {
      // Implement complete encounter logic
    }
  }
}

class _PrescriptionDialog extends StatefulWidget {
  final Function(Map<String, dynamic>) onAdd;

  const _PrescriptionDialog({required this.onAdd});

  @override
  State<_PrescriptionDialog> createState() => _PrescriptionDialogState();
}

class _PrescriptionDialogState extends State<_PrescriptionDialog> {
  final _medicineController = TextEditingController();
  final _dosageController = TextEditingController();
  final _frequencyController = TextEditingController();
  final _durationController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Add Prescription'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomTextField(
            controller: _medicineController,
            labelText: 'Medicine Name',
            hintText: 'Enter medicine name',
          ),
          SizedBox(height: 12.h),
          CustomTextField(
            controller: _dosageController,
            labelText: 'Dosage',
            hintText: 'e.g., 500mg',
          ),
          SizedBox(height: 12.h),
          CustomTextField(
            controller: _frequencyController,
            labelText: 'Frequency',
            hintText: 'e.g., Twice daily',
          ),
          SizedBox(height: 12.h),
          CustomTextField(
            controller: _durationController,
            labelText: 'Duration',
            hintText: 'e.g., 7 days',
          ),
        ],
      ),
      actions: [
        SizedBox(width: 8.w),
        CustomButton(
          buttonState: ButtonState.normal,
          buttonText: 'Add',
          width: 80,
          height: 36,
          onTap: () {
            widget.onAdd({
              'medicine': _medicineController.text,
              'dosage': _dosageController.text,
              'frequency': _frequencyController.text,
              'duration': _durationController.text,
            });
            Navigator.pop(context);
          },
        ),
      ],
    );
  }
}
