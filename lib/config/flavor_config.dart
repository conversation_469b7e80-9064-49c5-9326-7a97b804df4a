// enum Flavor { dev, staging, prod }

// class FlavorValues {
//   final String baseUrl;
//   final bool enableCrashlytics;

//   const FlavorValues({required this.baseUrl, required this.enableCrashlytics});
// }

// class FlavorConfig {
//   final Flavor flavor;
//   final String name;
//   // final Color color;
//   final FlavorValues values;

//   static late FlavorConfig _instance;

//   factory FlavorConfig({
//     required Flavor flavor,
//     required String name,
//     // required Color color,
//     required FlavorValues values,
//   }) {
//     _instance = FlavorConfig._internal(
//       flavor,
//       name,
//       // color,
//       values,
//     );
//     return _instance;
//   }

//   FlavorConfig._internal(
//     this.flavor,
//     this.name,
//     // this.color,
//     this.values,
//   );

//   static FlavorConfig get instance => _instance;

//   static bool get isProd => _instance.flavor == Flavor.prod;
//   static bool get isDev => _instance.flavor == Flavor.dev;
//   static bool get isStaging => _instance.flavor == Flavor.staging;
// }
