{"_note1": "KEEP THIS FILE! Check it into a version control system (VCS) like git.", "_note2": "ObjectBox manages crucial IDs for your object model. See docs for details.", "_note3": "If you have VCS merge conflicts, you must resolve them according to ObjectBox docs.", "entities": [{"id": "1:3214317593088249727", "lastPropertyId": "10:6233624287632976140", "name": "PayrollModel", "properties": [{"id": "1:8343269168936656316", "name": "id", "type": 6, "flags": 1}, {"id": "2:6886242308905019192", "name": "payrollId", "type": 9}, {"id": "3:4346966552918546603", "name": "employeeId", "type": 9}, {"id": "4:5263287385290275628", "name": "employeeName", "type": 9}, {"id": "5:2724384260861610674", "name": "postingDate", "type": 10}, {"id": "6:1677922205145721577", "name": "startDate", "type": 10}, {"id": "7:5916725108231859841", "name": "endDate", "type": 10}, {"id": "8:5794225464156390555", "name": "grossPay", "type": 8}, {"id": "9:2945941677514537349", "name": "netPay", "type": 8}, {"id": "10:6233624287632976140", "name": "totalDeduction", "type": 8}], "relations": [{"id": "5:3486817248701094001", "name": "earnings", "targetId": "10:2797639222038046012"}, {"id": "6:4287109309613490185", "name": "deductions", "targetId": "9:8303883214076406899"}]}, {"id": "2:3661973159102556276", "lastPropertyId": "10:2026988272520727263", "name": "AttendenceModel", "properties": [{"id": "1:6113832711796417665", "name": "id", "type": 6, "flags": 1}, {"id": "2:5272230451816943230", "name": "attendanceId", "type": 9}, {"id": "3:2218247504626107837", "name": "employeeName", "type": 9}, {"id": "4:7039224192423468333", "name": "attendanceDate", "type": 10, "flags": 8, "indexId": "1:7037776378521294698"}, {"id": "5:695736443717106242", "name": "status", "type": 9}, {"id": "6:516916311419388023", "name": "shift", "type": 9}, {"id": "7:8388117671154251476", "name": "inTime", "type": 9}, {"id": "8:2032811096723330749", "name": "outTime", "type": 9}, {"id": "9:900863365172717915", "name": "workingHours", "type": 8}, {"id": "10:2026988272520727263", "name": "employeeId", "type": 9}], "relations": []}, {"id": "3:7605319204650408037", "lastPropertyId": "21:5549315618375047052", "name": "PostModel", "properties": [{"id": "1:8822422830393738815", "name": "id", "type": 6, "flags": 1}, {"id": "3:5670992319815410398", "name": "content", "type": 9}, {"id": "10:3775064118615377939", "name": "postId", "type": 9}, {"id": "11:1424666039086257543", "name": "user", "type": 9}, {"id": "12:5634630356369658641", "name": "creation", "type": 10}, {"id": "13:1558474850552837033", "name": "isPoll", "type": 1}, {"id": "14:1956602217242877393", "name": "pollExpiry", "type": 10}, {"id": "15:690300099352848973", "name": "likeCount", "type": 6}, {"id": "16:4317120331042940531", "name": "commentCount", "type": 6}, {"id": "18:4845630068605848810", "name": "totalVotes", "type": 6}, {"id": "19:3441521878043502057", "name": "isExpired", "type": 1}, {"id": "20:7649663344985898600", "name": "postuserInfoId", "type": 11, "flags": 520, "indexId": "4:5934763573459379279", "relationTarget": "PostUserInfo"}, {"id": "21:5549315618375047052", "name": "isLiked", "type": 1}], "relations": [{"id": "1:8551799150360929868", "name": "media", "targetId": "5:3636532233600367712"}, {"id": "2:7427220829509614690", "name": "pollOptions", "targetId": "4:7083823751272081825"}, {"id": "3:4998306575583663522", "name": "likedPostUserInfo", "targetId": "7:2040870452160061065"}, {"id": "4:3165730724867197157", "name": "votedPostUserInfo", "targetId": "8:2808653228003911363"}]}, {"id": "4:7083823751272081825", "lastPropertyId": "6:6347091677344432260", "name": "PollOption", "properties": [{"id": "1:8838568022541513355", "name": "id", "type": 6, "flags": 1}, {"id": "2:2837218805224005225", "name": "pollOptionId", "type": 9}, {"id": "3:111729137565733148", "name": "optionText", "type": 9}, {"id": "4:3689665560340204632", "name": "votes", "type": 6}, {"id": "5:2679313144056821144", "name": "percentage", "type": 8}, {"id": "6:6347091677344432260", "name": "isVoted", "type": 1}], "relations": []}, {"id": "5:3636532233600367712", "lastPropertyId": "3:7130557243543842803", "name": "PostMedia", "properties": [{"id": "1:725257230422777747", "name": "id", "type": 6, "flags": 1}, {"id": "2:1438149750527518903", "name": "fileUrl", "type": 9}, {"id": "3:7130557243543842803", "name": "mediaType", "type": 9}], "relations": []}, {"id": "6:4681017783786778017", "lastPropertyId": "6:6621887159541337734", "name": "PostUserInfo", "properties": [{"id": "1:2781524005428224526", "name": "id", "type": 6, "flags": 1}, {"id": "2:2842952986290530910", "name": "email", "type": 9}, {"id": "3:3558835681473347948", "name": "firstName", "type": 9}, {"id": "4:2539230414452306020", "name": "lastName", "type": 9}, {"id": "5:145286554804436662", "name": "fullName", "type": 9}, {"id": "6:6621887159541337734", "name": "userImage", "type": 9}], "relations": []}, {"id": "7:2040870452160061065", "lastPropertyId": "4:5418615342732425812", "name": "LikedPostUserInfo", "properties": [{"id": "1:6398301644927730969", "name": "id", "type": 6, "flags": 1}, {"id": "2:6057022985885012829", "name": "email", "type": 9}, {"id": "3:983459878975452709", "name": "fullName", "type": 9}, {"id": "4:5418615342732425812", "name": "userImage", "type": 9}], "relations": []}, {"id": "8:2808653228003911363", "lastPropertyId": "5:4639311628966900091", "name": "VotedPostUserInfo", "properties": [{"id": "1:7886645249891837467", "name": "id", "type": 6, "flags": 1}, {"id": "2:1203496322125057826", "name": "email", "type": 9}, {"id": "3:7763353340532401501", "name": "fullName", "type": 9}, {"id": "4:3910305782467468925", "name": "userImage", "type": 9}, {"id": "5:4639311628966900091", "name": "voteDate", "type": 10}], "relations": []}, {"id": "9:8303883214076406899", "lastPropertyId": "4:5049997170487442972", "name": "Deduction", "properties": [{"id": "1:700023453645988998", "name": "id", "type": 6, "flags": 1}, {"id": "2:6230371870289512224", "name": "salaryComponent", "type": 9}, {"id": "3:8634930506773488685", "name": "amount", "type": 8}, {"id": "4:5049997170487442972", "name": "defaultAmount", "type": 8}], "relations": []}, {"id": "10:2797639222038046012", "lastPropertyId": "4:6085853900904442669", "name": "Earning", "properties": [{"id": "1:3919577671806783888", "name": "id", "type": 6, "flags": 1}, {"id": "2:3301622877800068909", "name": "salaryComponent", "type": 9}, {"id": "3:1375234149140430917", "name": "amount", "type": 8}, {"id": "4:6085853900904442669", "name": "defaultAmount", "type": 8}], "relations": []}, {"id": "11:185818736576792292", "lastPropertyId": "3:2874811821607295597", "name": "RoleModel", "properties": [{"id": "1:6618859192659833241", "name": "id", "type": 6, "flags": 1}, {"id": "2:8773492820034579805", "name": "name", "type": 9}, {"id": "3:2874811821607295597", "name": "userId", "type": 11, "flags": 520, "indexId": "5:8874616297877673506", "relationTarget": "UserModel"}], "relations": []}, {"id": "12:4731581537534486812", "lastPropertyId": "11:266519346603780806", "name": "UserModel", "properties": [{"id": "1:7485493958248428555", "name": "id", "type": 6, "flags": 1}, {"id": "2:6165212698565174751", "name": "employeeId", "type": 9}, {"id": "3:2242429188661905821", "name": "employeeName", "type": 9}, {"id": "4:2620300503405494878", "name": "username", "type": 9}, {"id": "5:6106963992713910004", "name": "email", "type": 9}, {"id": "6:3241778503092642399", "name": "gender", "type": 9}, {"id": "7:5484170193500930488", "name": "birthDate", "type": 10}, {"id": "8:1276110321614921405", "name": "dateOfJoining", "type": 10}, {"id": "9:8935446937486966515", "name": "employeeNo", "type": 9}, {"id": "10:6535519114589001951", "name": "fcmToken", "type": 9}, {"id": "11:266519346603780806", "name": "profileImage", "type": 9}], "relations": []}], "lastEntityId": "12:4731581537534486812", "lastIndexId": "5:8874616297877673506", "lastRelationId": "6:4287109309613490185", "lastSequenceId": "0:0", "modelVersion": 5, "modelVersionParserMinimum": 5, "retiredEntityUids": [], "retiredIndexUids": [5046094165871269980, 5220372761325304387], "retiredPropertyUids": [3982143284016684776, 4136161748612723381, 8219302454741045976, 4955585233617740921, 1752456425451931260, 1319564842551062076, 7810667676936469696, 304401107688839998], "retiredRelationUids": [], "version": 1}