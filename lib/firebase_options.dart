// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCnY_nsjPxkieT_FmvnN7bcino05RixYHI',
    appId: '1:552524098621:web:ed745e0b483e9f80fd4e54',
    messagingSenderId: '552524098621',
    projectId: 'rasiin-task-app-36186',
    authDomain: 'rasiin-task-app-36186.firebaseapp.com',
    storageBucket: 'rasiin-task-app-36186.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBCrlsEBQd-wSSBi8RfGnJkTCcYCeB3Pjc',
    appId: '1:552524098621:android:bec691e16e3add99fd4e54',
    messagingSenderId: '552524098621',
    projectId: 'rasiin-task-app-36186',
    storageBucket: 'rasiin-task-app-36186.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDJxZkd0E49hrTNFl0s1bouGzUhL1a1D5E',
    appId: '1:552524098621:ios:61ba8e947e9bb6effd4e54',
    messagingSenderId: '552524098621',
    projectId: 'rasiin-task-app-36186',
    storageBucket: 'rasiin-task-app-36186.appspot.com',
    iosBundleId: 'com.example.rasiinTasksApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDJxZkd0E49hrTNFl0s1bouGzUhL1a1D5E',
    appId: '1:552524098621:ios:61ba8e947e9bb6effd4e54',
    messagingSenderId: '552524098621',
    projectId: 'rasiin-task-app-36186',
    storageBucket: 'rasiin-task-app-36186.appspot.com',
    iosBundleId: 'com.example.rasiinTasksApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCnY_nsjPxkieT_FmvnN7bcino05RixYHI',
    appId: '1:552524098621:web:a806e3febff5fb5ffd4e54',
    messagingSenderId: '552524098621',
    projectId: 'rasiin-task-app-36186',
    authDomain: 'rasiin-task-app-36186.firebaseapp.com',
    storageBucket: 'rasiin-task-app-36186.appspot.com',
  );
}
