// GENERATED CODE - DO NOT MODIFY BY HAND
// This code was generated by ObjectBox. To update it run the generator again
// with `dart run build_runner build`.
// See also https://docs.objectbox.io/getting-started#generate-objectbox-code

// ignore_for_file: camel_case_types, depend_on_referenced_packages
// coverage:ignore-file

import 'dart:typed_data';

import 'package:flat_buffers/flat_buffers.dart' as fb;
import 'package:objectbox/internal.dart'
    as obx_int; // generated code can access "internal" functionality
import 'package:objectbox/objectbox.dart' as obx;
import 'package:objectbox_flutter_libs/objectbox_flutter_libs.dart';

import 'core/models/attendence_model.dart';
import 'core/models/payroll_model.dart';
import 'core/models/post_model.dart';
import 'core/models/role_model.dart';
import 'core/models/user_model.dart';

export 'package:objectbox/objectbox.dart'; // so that callers only have to import this file

final _entities = <obx_int.ModelEntity>[
  obx_int.ModelEntity(
      id: const obx_int.IdUid(1, 3214317593088249727),
      name: 'PayrollModel',
      lastPropertyId: const obx_int.IdUid(10, 6233624287632976140),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 8343269168936656316),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6886242308905019192),
            name: 'payrollId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 4346966552918546603),
            name: 'employeeId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 5263287385290275628),
            name: 'employeeName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2724384260861610674),
            name: 'postingDate',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 1677922205145721577),
            name: 'startDate',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 5916725108231859841),
            name: 'endDate',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 5794225464156390555),
            name: 'grossPay',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 2945941677514537349),
            name: 'netPay',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 6233624287632976140),
            name: 'totalDeduction',
            type: 8,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[
        obx_int.ModelRelation(
            id: const obx_int.IdUid(5, 3486817248701094001),
            name: 'earnings',
            targetId: const obx_int.IdUid(10, 2797639222038046012)),
        obx_int.ModelRelation(
            id: const obx_int.IdUid(6, 4287109309613490185),
            name: 'deductions',
            targetId: const obx_int.IdUid(9, 8303883214076406899))
      ],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(2, 3661973159102556276),
      name: 'AttendenceModel',
      lastPropertyId: const obx_int.IdUid(10, 2026988272520727263),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6113832711796417665),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 5272230451816943230),
            name: 'attendanceId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 2218247504626107837),
            name: 'employeeName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 7039224192423468333),
            name: 'attendanceDate',
            type: 10,
            flags: 8,
            indexId: const obx_int.IdUid(1, 7037776378521294698)),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 695736443717106242),
            name: 'status',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 516916311419388023),
            name: 'shift',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 8388117671154251476),
            name: 'inTime',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 2032811096723330749),
            name: 'outTime',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 900863365172717915),
            name: 'workingHours',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 2026988272520727263),
            name: 'employeeId',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(3, 7605319204650408037),
      name: 'PostModel',
      lastPropertyId: const obx_int.IdUid(21, 5549315618375047052),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 8822422830393738815),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 5670992319815410398),
            name: 'content',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 3775064118615377939),
            name: 'postId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 1424666039086257543),
            name: 'user',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(12, 5634630356369658641),
            name: 'creation',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(13, 1558474850552837033),
            name: 'isPoll',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(14, 1956602217242877393),
            name: 'pollExpiry',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(15, 690300099352848973),
            name: 'likeCount',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(16, 4317120331042940531),
            name: 'commentCount',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(18, 4845630068605848810),
            name: 'totalVotes',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(19, 3441521878043502057),
            name: 'isExpired',
            type: 1,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(20, 7649663344985898600),
            name: 'postuserInfoId',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(4, 5934763573459379279),
            relationTarget: 'PostUserInfo'),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(21, 5549315618375047052),
            name: 'isLiked',
            type: 1,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[
        obx_int.ModelRelation(
            id: const obx_int.IdUid(1, 8551799150360929868),
            name: 'media',
            targetId: const obx_int.IdUid(5, 3636532233600367712)),
        obx_int.ModelRelation(
            id: const obx_int.IdUid(2, 7427220829509614690),
            name: 'pollOptions',
            targetId: const obx_int.IdUid(4, 7083823751272081825)),
        obx_int.ModelRelation(
            id: const obx_int.IdUid(3, 4998306575583663522),
            name: 'likedPostUserInfo',
            targetId: const obx_int.IdUid(7, 2040870452160061065)),
        obx_int.ModelRelation(
            id: const obx_int.IdUid(4, 3165730724867197157),
            name: 'votedPostUserInfo',
            targetId: const obx_int.IdUid(8, 2808653228003911363))
      ],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(4, 7083823751272081825),
      name: 'PollOption',
      lastPropertyId: const obx_int.IdUid(6, 6347091677344432260),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 8838568022541513355),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 2837218805224005225),
            name: 'pollOptionId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 111729137565733148),
            name: 'optionText',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 3689665560340204632),
            name: 'votes',
            type: 6,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 2679313144056821144),
            name: 'percentage',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 6347091677344432260),
            name: 'isVoted',
            type: 1,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(5, 3636532233600367712),
      name: 'PostMedia',
      lastPropertyId: const obx_int.IdUid(3, 7130557243543842803),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 725257230422777747),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 1438149750527518903),
            name: 'fileUrl',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7130557243543842803),
            name: 'mediaType',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(6, 4681017783786778017),
      name: 'PostUserInfo',
      lastPropertyId: const obx_int.IdUid(6, 6621887159541337734),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 2781524005428224526),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 2842952986290530910),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 3558835681473347948),
            name: 'firstName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2539230414452306020),
            name: 'lastName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 145286554804436662),
            name: 'fullName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 6621887159541337734),
            name: 'userImage',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(7, 2040870452160061065),
      name: 'LikedPostUserInfo',
      lastPropertyId: const obx_int.IdUid(4, 5418615342732425812),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6398301644927730969),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6057022985885012829),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 983459878975452709),
            name: 'fullName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 5418615342732425812),
            name: 'userImage',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(8, 2808653228003911363),
      name: 'VotedPostUserInfo',
      lastPropertyId: const obx_int.IdUid(5, 4639311628966900091),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7886645249891837467),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 1203496322125057826),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 7763353340532401501),
            name: 'fullName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 3910305782467468925),
            name: 'userImage',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 4639311628966900091),
            name: 'voteDate',
            type: 10,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(9, 8303883214076406899),
      name: 'Deduction',
      lastPropertyId: const obx_int.IdUid(4, 5049997170487442972),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 700023453645988998),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6230371870289512224),
            name: 'salaryComponent',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 8634930506773488685),
            name: 'amount',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 5049997170487442972),
            name: 'defaultAmount',
            type: 8,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(10, 2797639222038046012),
      name: 'Earning',
      lastPropertyId: const obx_int.IdUid(4, 6085853900904442669),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 3919577671806783888),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 3301622877800068909),
            name: 'salaryComponent',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 1375234149140430917),
            name: 'amount',
            type: 8,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 6085853900904442669),
            name: 'defaultAmount',
            type: 8,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(11, 185818736576792292),
      name: 'RoleModel',
      lastPropertyId: const obx_int.IdUid(3, 2874811821607295597),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 6618859192659833241),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 8773492820034579805),
            name: 'name',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 2874811821607295597),
            name: 'userId',
            type: 11,
            flags: 520,
            indexId: const obx_int.IdUid(5, 8874616297877673506),
            relationTarget: 'UserModel')
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[]),
  obx_int.ModelEntity(
      id: const obx_int.IdUid(12, 4731581537534486812),
      name: 'UserModel',
      lastPropertyId: const obx_int.IdUid(11, 266519346603780806),
      flags: 0,
      properties: <obx_int.ModelProperty>[
        obx_int.ModelProperty(
            id: const obx_int.IdUid(1, 7485493958248428555),
            name: 'id',
            type: 6,
            flags: 1),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(2, 6165212698565174751),
            name: 'employeeId',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(3, 2242429188661905821),
            name: 'employeeName',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(4, 2620300503405494878),
            name: 'username',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(5, 6106963992713910004),
            name: 'email',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(6, 3241778503092642399),
            name: 'gender',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(7, 5484170193500930488),
            name: 'birthDate',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(8, 1276110321614921405),
            name: 'dateOfJoining',
            type: 10,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(9, 8935446937486966515),
            name: 'employeeNo',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(10, 6535519114589001951),
            name: 'fcmToken',
            type: 9,
            flags: 0),
        obx_int.ModelProperty(
            id: const obx_int.IdUid(11, 266519346603780806),
            name: 'profileImage',
            type: 9,
            flags: 0)
      ],
      relations: <obx_int.ModelRelation>[],
      backlinks: <obx_int.ModelBacklink>[
        obx_int.ModelBacklink(
            name: 'roles', srcEntity: 'RoleModel', srcField: '')
      ])
];

/// Shortcut for [obx.Store.new] that passes [getObjectBoxModel] and for Flutter
/// apps by default a [directory] using `defaultStoreDirectory()` from the
/// ObjectBox Flutter library.
///
/// Note: for desktop apps it is recommended to specify a unique [directory].
///
/// See [obx.Store.new] for an explanation of all parameters.
///
/// For Flutter apps, also calls `loadObjectBoxLibraryAndroidCompat()` from
/// the ObjectBox Flutter library to fix loading the native ObjectBox library
/// on Android 6 and older.
Future<obx.Store> openStore(
    {String? directory,
    int? maxDBSizeInKB,
    int? maxDataSizeInKB,
    int? fileMode,
    int? maxReaders,
    bool queriesCaseSensitiveDefault = true,
    String? macosApplicationGroup}) async {
  await loadObjectBoxLibraryAndroidCompat();
  return obx.Store(getObjectBoxModel(),
      directory: directory ?? (await defaultStoreDirectory()).path,
      maxDBSizeInKB: maxDBSizeInKB,
      maxDataSizeInKB: maxDataSizeInKB,
      fileMode: fileMode,
      maxReaders: maxReaders,
      queriesCaseSensitiveDefault: queriesCaseSensitiveDefault,
      macosApplicationGroup: macosApplicationGroup);
}

/// Returns the ObjectBox model definition for this project for use with
/// [obx.Store.new].
obx_int.ModelDefinition getObjectBoxModel() {
  final model = obx_int.ModelInfo(
      entities: _entities,
      lastEntityId: const obx_int.IdUid(12, 4731581537534486812),
      lastIndexId: const obx_int.IdUid(5, 8874616297877673506),
      lastRelationId: const obx_int.IdUid(6, 4287109309613490185),
      lastSequenceId: const obx_int.IdUid(0, 0),
      retiredEntityUids: const [],
      retiredIndexUids: const [5046094165871269980, 5220372761325304387],
      retiredPropertyUids: const [
        3982143284016684776,
        4136161748612723381,
        8219302454741045976,
        4955585233617740921,
        1752456425451931260,
        1319564842551062076,
        7810667676936469696,
        304401107688839998
      ],
      retiredRelationUids: const [],
      modelVersion: 5,
      modelVersionParserMinimum: 5,
      version: 1);

  final bindings = <Type, obx_int.EntityDefinition>{
    PayrollModel: obx_int.EntityDefinition<PayrollModel>(
        model: _entities[0],
        toOneRelations: (PayrollModel object) => [],
        toManyRelations: (PayrollModel object) => {
              obx_int.RelInfo<PayrollModel>.toMany(5, object.id):
                  object.earnings,
              obx_int.RelInfo<PayrollModel>.toMany(6, object.id):
                  object.deductions
            },
        getId: (PayrollModel object) => object.id,
        setId: (PayrollModel object, int id) {
          object.id = id;
        },
        objectToFB: (PayrollModel object, fb.Builder fbb) {
          final payrollIdOffset = fbb.writeString(object.payrollId);
          final employeeIdOffset = fbb.writeString(object.employeeId);
          final employeeNameOffset = fbb.writeString(object.employeeName);
          fbb.startTable(11);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, payrollIdOffset);
          fbb.addOffset(2, employeeIdOffset);
          fbb.addOffset(3, employeeNameOffset);
          fbb.addInt64(4, object.postingDate.millisecondsSinceEpoch);
          fbb.addInt64(5, object.startDate.millisecondsSinceEpoch);
          fbb.addInt64(6, object.endDate.millisecondsSinceEpoch);
          fbb.addFloat64(7, object.grossPay);
          fbb.addFloat64(8, object.netPay);
          fbb.addFloat64(9, object.totalDeduction);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final payrollIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final employeeIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final employeeNameParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 10, '');
          final postingDateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 12, 0));
          final startDateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 14, 0));
          final endDateParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 16, 0));
          final grossPayParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 18, 0);
          final totalDeductionParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 22, 0);
          final netPayParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 20, 0);
          final object = PayrollModel(
              payrollId: payrollIdParam,
              employeeId: employeeIdParam,
              employeeName: employeeNameParam,
              postingDate: postingDateParam,
              startDate: startDateParam,
              endDate: endDateParam,
              grossPay: grossPayParam,
              totalDeduction: totalDeductionParam,
              netPay: netPayParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          obx_int.InternalToManyAccess.setRelInfo<PayrollModel>(object.earnings,
              store, obx_int.RelInfo<PayrollModel>.toMany(5, object.id));
          obx_int.InternalToManyAccess.setRelInfo<PayrollModel>(
              object.deductions,
              store,
              obx_int.RelInfo<PayrollModel>.toMany(6, object.id));
          return object;
        }),
    AttendenceModel: obx_int.EntityDefinition<AttendenceModel>(
        model: _entities[1],
        toOneRelations: (AttendenceModel object) => [],
        toManyRelations: (AttendenceModel object) => {},
        getId: (AttendenceModel object) => object.id,
        setId: (AttendenceModel object, int id) {
          object.id = id;
        },
        objectToFB: (AttendenceModel object, fb.Builder fbb) {
          final attendanceIdOffset = fbb.writeString(object.attendanceId);
          final employeeNameOffset = fbb.writeString(object.employeeName);
          final statusOffset = fbb.writeString(object.status);
          final shiftOffset = fbb.writeString(object.shift);
          final inTimeOffset = fbb.writeString(object.inTime);
          final outTimeOffset = fbb.writeString(object.outTime);
          final employeeIdOffset = fbb.writeString(object.employeeId);
          fbb.startTable(11);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, attendanceIdOffset);
          fbb.addOffset(2, employeeNameOffset);
          fbb.addInt64(3, object.attendanceDate?.millisecondsSinceEpoch);
          fbb.addOffset(4, statusOffset);
          fbb.addOffset(5, shiftOffset);
          fbb.addOffset(6, inTimeOffset);
          fbb.addOffset(7, outTimeOffset);
          fbb.addFloat64(8, object.workingHours);
          fbb.addOffset(9, employeeIdOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final attendanceDateValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 10);
          final attendanceIdParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, '');
          final employeeIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 22, '');
          final employeeNameParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 8, '');
          final attendanceDateParam = attendanceDateValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(attendanceDateValue);
          final statusParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 12, '');
          final shiftParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 14, '');
          final inTimeParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 16, '');
          final outTimeParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 18, '');
          final workingHoursParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 20, 0);
          final object = AttendenceModel(
              attendanceId: attendanceIdParam,
              employeeId: employeeIdParam,
              employeeName: employeeNameParam,
              attendanceDate: attendanceDateParam,
              status: statusParam,
              shift: shiftParam,
              inTime: inTimeParam,
              outTime: outTimeParam,
              workingHours: workingHoursParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    PostModel: obx_int.EntityDefinition<PostModel>(
        model: _entities[2],
        toOneRelations: (PostModel object) => [object.postuserInfo],
        toManyRelations: (PostModel object) => {
              obx_int.RelInfo<PostModel>.toMany(1, object.id): object.media,
              obx_int.RelInfo<PostModel>.toMany(2, object.id):
                  object.pollOptions,
              obx_int.RelInfo<PostModel>.toMany(3, object.id):
                  object.likedPostUserInfo,
              obx_int.RelInfo<PostModel>.toMany(4, object.id):
                  object.votedPostUserInfo
            },
        getId: (PostModel object) => object.id,
        setId: (PostModel object, int id) {
          object.id = id;
        },
        objectToFB: (PostModel object, fb.Builder fbb) {
          final contentOffset = fbb.writeString(object.content);
          final postIdOffset = fbb.writeString(object.postId);
          final userOffset = fbb.writeString(object.user);
          fbb.startTable(22);
          fbb.addInt64(0, object.id);
          fbb.addOffset(2, contentOffset);
          fbb.addOffset(9, postIdOffset);
          fbb.addOffset(10, userOffset);
          fbb.addInt64(11, object.creation.millisecondsSinceEpoch);
          fbb.addBool(12, object.isPoll);
          fbb.addInt64(13, object.pollExpiry?.millisecondsSinceEpoch);
          fbb.addInt64(14, object.likeCount);
          fbb.addInt64(15, object.commentCount);
          fbb.addInt64(17, object.totalVotes);
          fbb.addBool(18, object.isExpired);
          fbb.addInt64(19, object.postuserInfo.targetId);
          fbb.addBool(20, object.isLiked);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final pollExpiryValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 30);
          final postIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 22, '');
          final contentParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final userParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 24, '');
          final creationParam = DateTime.fromMillisecondsSinceEpoch(
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 26, 0));
          final isPollParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 28, false);
          final pollExpiryParam = pollExpiryValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(pollExpiryValue);
          final likeCountParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 32, 0);
          final commentCountParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 34, 0);
          final isLikedParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 44, false);
          final totalVotesParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 38, 0);
          final isExpiredParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 40, false);
          final object = PostModel(
              postId: postIdParam,
              content: contentParam,
              user: userParam,
              creation: creationParam,
              isPoll: isPollParam,
              pollExpiry: pollExpiryParam,
              likeCount: likeCountParam,
              commentCount: commentCountParam,
              isLiked: isLikedParam,
              totalVotes: totalVotesParam,
              isExpired: isExpiredParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          object.postuserInfo.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 42, 0);
          object.postuserInfo.attach(store);
          obx_int.InternalToManyAccess.setRelInfo<PostModel>(object.media,
              store, obx_int.RelInfo<PostModel>.toMany(1, object.id));
          obx_int.InternalToManyAccess.setRelInfo<PostModel>(object.pollOptions,
              store, obx_int.RelInfo<PostModel>.toMany(2, object.id));
          obx_int.InternalToManyAccess.setRelInfo<PostModel>(
              object.likedPostUserInfo,
              store,
              obx_int.RelInfo<PostModel>.toMany(3, object.id));
          obx_int.InternalToManyAccess.setRelInfo<PostModel>(
              object.votedPostUserInfo,
              store,
              obx_int.RelInfo<PostModel>.toMany(4, object.id));
          return object;
        }),
    PollOption: obx_int.EntityDefinition<PollOption>(
        model: _entities[3],
        toOneRelations: (PollOption object) => [],
        toManyRelations: (PollOption object) => {},
        getId: (PollOption object) => object.id,
        setId: (PollOption object, int id) {
          object.id = id;
        },
        objectToFB: (PollOption object, fb.Builder fbb) {
          final pollOptionIdOffset = fbb.writeString(object.pollOptionId);
          final optionTextOffset = fbb.writeString(object.optionText);
          fbb.startTable(7);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, pollOptionIdOffset);
          fbb.addOffset(2, optionTextOffset);
          fbb.addInt64(3, object.votes);
          fbb.addFloat64(4, object.percentage);
          fbb.addBool(5, object.isVoted);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final pollOptionIdParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, '');
          final optionTextParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final votesParam =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 10, 0);
          final percentageParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 12, 0);
          final isVotedParam =
              const fb.BoolReader().vTableGet(buffer, rootOffset, 14, false);
          final object = PollOption(
              pollOptionId: pollOptionIdParam,
              optionText: optionTextParam,
              votes: votesParam,
              percentage: percentageParam,
              isVoted: isVotedParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    PostMedia: obx_int.EntityDefinition<PostMedia>(
        model: _entities[4],
        toOneRelations: (PostMedia object) => [],
        toManyRelations: (PostMedia object) => {},
        getId: (PostMedia object) => object.id,
        setId: (PostMedia object, int id) {
          object.id = id;
        },
        objectToFB: (PostMedia object, fb.Builder fbb) {
          final fileUrlOffset = fbb.writeString(object.fileUrl);
          final mediaTypeOffset = fbb.writeString(object.mediaType);
          fbb.startTable(4);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, fileUrlOffset);
          fbb.addOffset(2, mediaTypeOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final fileUrlParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final mediaTypeParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final object = PostMedia(
              fileUrl: fileUrlParam, mediaType: mediaTypeParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    PostUserInfo: obx_int.EntityDefinition<PostUserInfo>(
        model: _entities[5],
        toOneRelations: (PostUserInfo object) => [],
        toManyRelations: (PostUserInfo object) => {},
        getId: (PostUserInfo object) => object.id,
        setId: (PostUserInfo object, int id) {
          object.id = id;
        },
        objectToFB: (PostUserInfo object, fb.Builder fbb) {
          final emailOffset = fbb.writeString(object.email);
          final firstNameOffset = fbb.writeString(object.firstName);
          final lastNameOffset = fbb.writeString(object.lastName);
          final fullNameOffset = fbb.writeString(object.fullName);
          final userImageOffset = fbb.writeString(object.userImage);
          fbb.startTable(7);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, emailOffset);
          fbb.addOffset(2, firstNameOffset);
          fbb.addOffset(3, lastNameOffset);
          fbb.addOffset(4, fullNameOffset);
          fbb.addOffset(5, userImageOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final firstNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final lastNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final fullNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 12, '');
          final userImageParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 14, '');
          final object = PostUserInfo(
              email: emailParam,
              firstName: firstNameParam,
              lastName: lastNameParam,
              fullName: fullNameParam,
              userImage: userImageParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    LikedPostUserInfo: obx_int.EntityDefinition<LikedPostUserInfo>(
        model: _entities[6],
        toOneRelations: (LikedPostUserInfo object) => [],
        toManyRelations: (LikedPostUserInfo object) => {},
        getId: (LikedPostUserInfo object) => object.id,
        setId: (LikedPostUserInfo object, int id) {
          object.id = id;
        },
        objectToFB: (LikedPostUserInfo object, fb.Builder fbb) {
          final emailOffset = fbb.writeString(object.email);
          final fullNameOffset = fbb.writeString(object.fullName);
          final userImageOffset = fbb.writeString(object.userImage);
          fbb.startTable(5);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, emailOffset);
          fbb.addOffset(2, fullNameOffset);
          fbb.addOffset(3, userImageOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final fullNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final userImageParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final object = LikedPostUserInfo(
              email: emailParam,
              fullName: fullNameParam,
              userImage: userImageParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    VotedPostUserInfo: obx_int.EntityDefinition<VotedPostUserInfo>(
        model: _entities[7],
        toOneRelations: (VotedPostUserInfo object) => [],
        toManyRelations: (VotedPostUserInfo object) => {},
        getId: (VotedPostUserInfo object) => object.id,
        setId: (VotedPostUserInfo object, int id) {
          object.id = id;
        },
        objectToFB: (VotedPostUserInfo object, fb.Builder fbb) {
          final emailOffset = fbb.writeString(object.email);
          final fullNameOffset = fbb.writeString(object.fullName);
          final userImageOffset = fbb.writeString(object.userImage);
          fbb.startTable(6);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, emailOffset);
          fbb.addOffset(2, fullNameOffset);
          fbb.addOffset(3, userImageOffset);
          fbb.addInt64(4, object.voteDate?.millisecondsSinceEpoch);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final voteDateValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 12);
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final fullNameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 8, '');
          final userImageParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final voteDateParam = voteDateValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(voteDateValue);
          final object = VotedPostUserInfo(
              email: emailParam,
              fullName: fullNameParam,
              userImage: userImageParam,
              voteDate: voteDateParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    Deduction: obx_int.EntityDefinition<Deduction>(
        model: _entities[8],
        toOneRelations: (Deduction object) => [],
        toManyRelations: (Deduction object) => {},
        getId: (Deduction object) => object.id,
        setId: (Deduction object, int id) {
          object.id = id;
        },
        objectToFB: (Deduction object, fb.Builder fbb) {
          final salaryComponentOffset = fbb.writeString(object.salaryComponent);
          fbb.startTable(5);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, salaryComponentOffset);
          fbb.addFloat64(2, object.amount);
          fbb.addFloat64(3, object.defaultAmount);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final salaryComponentParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, '');
          final amountParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 8, 0);
          final defaultAmountParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 10, 0);
          final object = Deduction(
              salaryComponent: salaryComponentParam,
              amount: amountParam,
              defaultAmount: defaultAmountParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    Earning: obx_int.EntityDefinition<Earning>(
        model: _entities[9],
        toOneRelations: (Earning object) => [],
        toManyRelations: (Earning object) => {},
        getId: (Earning object) => object.id,
        setId: (Earning object, int id) {
          object.id = id;
        },
        objectToFB: (Earning object, fb.Builder fbb) {
          final salaryComponentOffset = fbb.writeString(object.salaryComponent);
          fbb.startTable(5);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, salaryComponentOffset);
          fbb.addFloat64(2, object.amount);
          fbb.addFloat64(3, object.defaultAmount);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final salaryComponentParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 6, '');
          final amountParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 8, 0);
          final defaultAmountParam =
              const fb.Float64Reader().vTableGet(buffer, rootOffset, 10, 0);
          final object = Earning(
              salaryComponent: salaryComponentParam,
              amount: amountParam,
              defaultAmount: defaultAmountParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);

          return object;
        }),
    RoleModel: obx_int.EntityDefinition<RoleModel>(
        model: _entities[10],
        toOneRelations: (RoleModel object) => [object.user],
        toManyRelations: (RoleModel object) => {},
        getId: (RoleModel object) => object.id,
        setId: (RoleModel object, int id) {
          object.id = id;
        },
        objectToFB: (RoleModel object, fb.Builder fbb) {
          final nameOffset = fbb.writeString(object.name);
          fbb.startTable(4);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, nameOffset);
          fbb.addInt64(2, object.user.targetId);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final nameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final object = RoleModel(name: nameParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          object.user.targetId =
              const fb.Int64Reader().vTableGet(buffer, rootOffset, 8, 0);
          object.user.attach(store);
          return object;
        }),
    UserModel: obx_int.EntityDefinition<UserModel>(
        model: _entities[11],
        toOneRelations: (UserModel object) => [],
        toManyRelations: (UserModel object) => {
              obx_int.RelInfo<RoleModel>.toOneBacklink(
                      3, object.id, (RoleModel srcObject) => srcObject.user):
                  object.roles
            },
        getId: (UserModel object) => object.id,
        setId: (UserModel object, int id) {
          object.id = id;
        },
        objectToFB: (UserModel object, fb.Builder fbb) {
          final employeeIdOffset = fbb.writeString(object.employeeId);
          final employeeNameOffset = fbb.writeString(object.employeeName);
          final usernameOffset = fbb.writeString(object.username);
          final emailOffset = fbb.writeString(object.email);
          final genderOffset = fbb.writeString(object.gender);
          final employeeNoOffset = fbb.writeString(object.employeeNo);
          final fcmTokenOffset = fbb.writeString(object.fcmToken);
          final profileImageOffset = fbb.writeString(object.profileImage);
          fbb.startTable(12);
          fbb.addInt64(0, object.id);
          fbb.addOffset(1, employeeIdOffset);
          fbb.addOffset(2, employeeNameOffset);
          fbb.addOffset(3, usernameOffset);
          fbb.addOffset(4, emailOffset);
          fbb.addOffset(5, genderOffset);
          fbb.addInt64(6, object.birthDate?.millisecondsSinceEpoch);
          fbb.addInt64(7, object.dateOfJoining?.millisecondsSinceEpoch);
          fbb.addOffset(8, employeeNoOffset);
          fbb.addOffset(9, fcmTokenOffset);
          fbb.addOffset(10, profileImageOffset);
          fbb.finish(fbb.endTable());
          return object.id;
        },
        objectFromFB: (obx.Store store, ByteData fbData) {
          final buffer = fb.BufferContext(fbData);
          final rootOffset = buffer.derefObject(0);
          final birthDateValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 16);
          final dateOfJoiningValue =
              const fb.Int64Reader().vTableGetNullable(buffer, rootOffset, 18);
          final employeeIdParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 6, '');
          final employeeNameParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 8, '');
          final usernameParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 10, '');
          final emailParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 12, '');
          final genderParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 14, '');
          final birthDateParam = birthDateValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(birthDateValue);
          final employeeNoParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 20, '');
          final fcmTokenParam = const fb.StringReader(asciiOptimization: true)
              .vTableGet(buffer, rootOffset, 22, '');
          final dateOfJoiningParam = dateOfJoiningValue == null
              ? null
              : DateTime.fromMillisecondsSinceEpoch(dateOfJoiningValue);
          final profileImageParam =
              const fb.StringReader(asciiOptimization: true)
                  .vTableGet(buffer, rootOffset, 24, '');
          final object = UserModel(
              employeeId: employeeIdParam,
              employeeName: employeeNameParam,
              username: usernameParam,
              email: emailParam,
              gender: genderParam,
              birthDate: birthDateParam,
              employeeNo: employeeNoParam,
              fcmToken: fcmTokenParam,
              dateOfJoining: dateOfJoiningParam,
              profileImage: profileImageParam)
            ..id = const fb.Int64Reader().vTableGet(buffer, rootOffset, 4, 0);
          obx_int.InternalToManyAccess.setRelInfo<UserModel>(
              object.roles,
              store,
              obx_int.RelInfo<RoleModel>.toOneBacklink(
                  3, object.id, (RoleModel srcObject) => srcObject.user));
          return object;
        })
  };

  return obx_int.ModelDefinition(model, bindings);
}

/// [PayrollModel] entity fields to define ObjectBox queries.
class PayrollModel_ {
  /// See [PayrollModel.id].
  static final id =
      obx.QueryIntegerProperty<PayrollModel>(_entities[0].properties[0]);

  /// See [PayrollModel.payrollId].
  static final payrollId =
      obx.QueryStringProperty<PayrollModel>(_entities[0].properties[1]);

  /// See [PayrollModel.employeeId].
  static final employeeId =
      obx.QueryStringProperty<PayrollModel>(_entities[0].properties[2]);

  /// See [PayrollModel.employeeName].
  static final employeeName =
      obx.QueryStringProperty<PayrollModel>(_entities[0].properties[3]);

  /// See [PayrollModel.postingDate].
  static final postingDate =
      obx.QueryDateProperty<PayrollModel>(_entities[0].properties[4]);

  /// See [PayrollModel.startDate].
  static final startDate =
      obx.QueryDateProperty<PayrollModel>(_entities[0].properties[5]);

  /// See [PayrollModel.endDate].
  static final endDate =
      obx.QueryDateProperty<PayrollModel>(_entities[0].properties[6]);

  /// See [PayrollModel.grossPay].
  static final grossPay =
      obx.QueryDoubleProperty<PayrollModel>(_entities[0].properties[7]);

  /// See [PayrollModel.netPay].
  static final netPay =
      obx.QueryDoubleProperty<PayrollModel>(_entities[0].properties[8]);

  /// See [PayrollModel.totalDeduction].
  static final totalDeduction =
      obx.QueryDoubleProperty<PayrollModel>(_entities[0].properties[9]);

  /// see [PayrollModel.earnings]
  static final earnings =
      obx.QueryRelationToMany<PayrollModel, Earning>(_entities[0].relations[0]);

  /// see [PayrollModel.deductions]
  static final deductions = obx.QueryRelationToMany<PayrollModel, Deduction>(
      _entities[0].relations[1]);
}

/// [AttendenceModel] entity fields to define ObjectBox queries.
class AttendenceModel_ {
  /// See [AttendenceModel.id].
  static final id =
      obx.QueryIntegerProperty<AttendenceModel>(_entities[1].properties[0]);

  /// See [AttendenceModel.attendanceId].
  static final attendanceId =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[1]);

  /// See [AttendenceModel.employeeName].
  static final employeeName =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[2]);

  /// See [AttendenceModel.attendanceDate].
  static final attendanceDate =
      obx.QueryDateProperty<AttendenceModel>(_entities[1].properties[3]);

  /// See [AttendenceModel.status].
  static final status =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[4]);

  /// See [AttendenceModel.shift].
  static final shift =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[5]);

  /// See [AttendenceModel.inTime].
  static final inTime =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[6]);

  /// See [AttendenceModel.outTime].
  static final outTime =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[7]);

  /// See [AttendenceModel.workingHours].
  static final workingHours =
      obx.QueryDoubleProperty<AttendenceModel>(_entities[1].properties[8]);

  /// See [AttendenceModel.employeeId].
  static final employeeId =
      obx.QueryStringProperty<AttendenceModel>(_entities[1].properties[9]);
}

/// [PostModel] entity fields to define ObjectBox queries.
class PostModel_ {
  /// See [PostModel.id].
  static final id =
      obx.QueryIntegerProperty<PostModel>(_entities[2].properties[0]);

  /// See [PostModel.content].
  static final content =
      obx.QueryStringProperty<PostModel>(_entities[2].properties[1]);

  /// See [PostModel.postId].
  static final postId =
      obx.QueryStringProperty<PostModel>(_entities[2].properties[2]);

  /// See [PostModel.user].
  static final user =
      obx.QueryStringProperty<PostModel>(_entities[2].properties[3]);

  /// See [PostModel.creation].
  static final creation =
      obx.QueryDateProperty<PostModel>(_entities[2].properties[4]);

  /// See [PostModel.isPoll].
  static final isPoll =
      obx.QueryBooleanProperty<PostModel>(_entities[2].properties[5]);

  /// See [PostModel.pollExpiry].
  static final pollExpiry =
      obx.QueryDateProperty<PostModel>(_entities[2].properties[6]);

  /// See [PostModel.likeCount].
  static final likeCount =
      obx.QueryIntegerProperty<PostModel>(_entities[2].properties[7]);

  /// See [PostModel.commentCount].
  static final commentCount =
      obx.QueryIntegerProperty<PostModel>(_entities[2].properties[8]);

  /// See [PostModel.totalVotes].
  static final totalVotes =
      obx.QueryIntegerProperty<PostModel>(_entities[2].properties[9]);

  /// See [PostModel.isExpired].
  static final isExpired =
      obx.QueryBooleanProperty<PostModel>(_entities[2].properties[10]);

  /// See [PostModel.postuserInfo].
  static final postuserInfo = obx.QueryRelationToOne<PostModel, PostUserInfo>(
      _entities[2].properties[11]);

  /// See [PostModel.isLiked].
  static final isLiked =
      obx.QueryBooleanProperty<PostModel>(_entities[2].properties[12]);

  /// see [PostModel.media]
  static final media =
      obx.QueryRelationToMany<PostModel, PostMedia>(_entities[2].relations[0]);

  /// see [PostModel.pollOptions]
  static final pollOptions =
      obx.QueryRelationToMany<PostModel, PollOption>(_entities[2].relations[1]);

  /// see [PostModel.likedPostUserInfo]
  static final likedPostUserInfo =
      obx.QueryRelationToMany<PostModel, LikedPostUserInfo>(
          _entities[2].relations[2]);

  /// see [PostModel.votedPostUserInfo]
  static final votedPostUserInfo =
      obx.QueryRelationToMany<PostModel, VotedPostUserInfo>(
          _entities[2].relations[3]);
}

/// [PollOption] entity fields to define ObjectBox queries.
class PollOption_ {
  /// See [PollOption.id].
  static final id =
      obx.QueryIntegerProperty<PollOption>(_entities[3].properties[0]);

  /// See [PollOption.pollOptionId].
  static final pollOptionId =
      obx.QueryStringProperty<PollOption>(_entities[3].properties[1]);

  /// See [PollOption.optionText].
  static final optionText =
      obx.QueryStringProperty<PollOption>(_entities[3].properties[2]);

  /// See [PollOption.votes].
  static final votes =
      obx.QueryIntegerProperty<PollOption>(_entities[3].properties[3]);

  /// See [PollOption.percentage].
  static final percentage =
      obx.QueryDoubleProperty<PollOption>(_entities[3].properties[4]);

  /// See [PollOption.isVoted].
  static final isVoted =
      obx.QueryBooleanProperty<PollOption>(_entities[3].properties[5]);
}

/// [PostMedia] entity fields to define ObjectBox queries.
class PostMedia_ {
  /// See [PostMedia.id].
  static final id =
      obx.QueryIntegerProperty<PostMedia>(_entities[4].properties[0]);

  /// See [PostMedia.fileUrl].
  static final fileUrl =
      obx.QueryStringProperty<PostMedia>(_entities[4].properties[1]);

  /// See [PostMedia.mediaType].
  static final mediaType =
      obx.QueryStringProperty<PostMedia>(_entities[4].properties[2]);
}

/// [PostUserInfo] entity fields to define ObjectBox queries.
class PostUserInfo_ {
  /// See [PostUserInfo.id].
  static final id =
      obx.QueryIntegerProperty<PostUserInfo>(_entities[5].properties[0]);

  /// See [PostUserInfo.email].
  static final email =
      obx.QueryStringProperty<PostUserInfo>(_entities[5].properties[1]);

  /// See [PostUserInfo.firstName].
  static final firstName =
      obx.QueryStringProperty<PostUserInfo>(_entities[5].properties[2]);

  /// See [PostUserInfo.lastName].
  static final lastName =
      obx.QueryStringProperty<PostUserInfo>(_entities[5].properties[3]);

  /// See [PostUserInfo.fullName].
  static final fullName =
      obx.QueryStringProperty<PostUserInfo>(_entities[5].properties[4]);

  /// See [PostUserInfo.userImage].
  static final userImage =
      obx.QueryStringProperty<PostUserInfo>(_entities[5].properties[5]);
}

/// [LikedPostUserInfo] entity fields to define ObjectBox queries.
class LikedPostUserInfo_ {
  /// See [LikedPostUserInfo.id].
  static final id =
      obx.QueryIntegerProperty<LikedPostUserInfo>(_entities[6].properties[0]);

  /// See [LikedPostUserInfo.email].
  static final email =
      obx.QueryStringProperty<LikedPostUserInfo>(_entities[6].properties[1]);

  /// See [LikedPostUserInfo.fullName].
  static final fullName =
      obx.QueryStringProperty<LikedPostUserInfo>(_entities[6].properties[2]);

  /// See [LikedPostUserInfo.userImage].
  static final userImage =
      obx.QueryStringProperty<LikedPostUserInfo>(_entities[6].properties[3]);
}

/// [VotedPostUserInfo] entity fields to define ObjectBox queries.
class VotedPostUserInfo_ {
  /// See [VotedPostUserInfo.id].
  static final id =
      obx.QueryIntegerProperty<VotedPostUserInfo>(_entities[7].properties[0]);

  /// See [VotedPostUserInfo.email].
  static final email =
      obx.QueryStringProperty<VotedPostUserInfo>(_entities[7].properties[1]);

  /// See [VotedPostUserInfo.fullName].
  static final fullName =
      obx.QueryStringProperty<VotedPostUserInfo>(_entities[7].properties[2]);

  /// See [VotedPostUserInfo.userImage].
  static final userImage =
      obx.QueryStringProperty<VotedPostUserInfo>(_entities[7].properties[3]);

  /// See [VotedPostUserInfo.voteDate].
  static final voteDate =
      obx.QueryDateProperty<VotedPostUserInfo>(_entities[7].properties[4]);
}

/// [Deduction] entity fields to define ObjectBox queries.
class Deduction_ {
  /// See [Deduction.id].
  static final id =
      obx.QueryIntegerProperty<Deduction>(_entities[8].properties[0]);

  /// See [Deduction.salaryComponent].
  static final salaryComponent =
      obx.QueryStringProperty<Deduction>(_entities[8].properties[1]);

  /// See [Deduction.amount].
  static final amount =
      obx.QueryDoubleProperty<Deduction>(_entities[8].properties[2]);

  /// See [Deduction.defaultAmount].
  static final defaultAmount =
      obx.QueryDoubleProperty<Deduction>(_entities[8].properties[3]);
}

/// [Earning] entity fields to define ObjectBox queries.
class Earning_ {
  /// See [Earning.id].
  static final id =
      obx.QueryIntegerProperty<Earning>(_entities[9].properties[0]);

  /// See [Earning.salaryComponent].
  static final salaryComponent =
      obx.QueryStringProperty<Earning>(_entities[9].properties[1]);

  /// See [Earning.amount].
  static final amount =
      obx.QueryDoubleProperty<Earning>(_entities[9].properties[2]);

  /// See [Earning.defaultAmount].
  static final defaultAmount =
      obx.QueryDoubleProperty<Earning>(_entities[9].properties[3]);
}

/// [RoleModel] entity fields to define ObjectBox queries.
class RoleModel_ {
  /// See [RoleModel.id].
  static final id =
      obx.QueryIntegerProperty<RoleModel>(_entities[10].properties[0]);

  /// See [RoleModel.name].
  static final name =
      obx.QueryStringProperty<RoleModel>(_entities[10].properties[1]);

  /// See [RoleModel.user].
  static final user =
      obx.QueryRelationToOne<RoleModel, UserModel>(_entities[10].properties[2]);
}

/// [UserModel] entity fields to define ObjectBox queries.
class UserModel_ {
  /// See [UserModel.id].
  static final id =
      obx.QueryIntegerProperty<UserModel>(_entities[11].properties[0]);

  /// See [UserModel.employeeId].
  static final employeeId =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[1]);

  /// See [UserModel.employeeName].
  static final employeeName =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[2]);

  /// See [UserModel.username].
  static final username =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[3]);

  /// See [UserModel.email].
  static final email =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[4]);

  /// See [UserModel.gender].
  static final gender =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[5]);

  /// See [UserModel.birthDate].
  static final birthDate =
      obx.QueryDateProperty<UserModel>(_entities[11].properties[6]);

  /// See [UserModel.dateOfJoining].
  static final dateOfJoining =
      obx.QueryDateProperty<UserModel>(_entities[11].properties[7]);

  /// See [UserModel.employeeNo].
  static final employeeNo =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[8]);

  /// See [UserModel.fcmToken].
  static final fcmToken =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[9]);

  /// See [UserModel.profileImage].
  static final profileImage =
      obx.QueryStringProperty<UserModel>(_entities[11].properties[10]);

  /// see [UserModel.roles]
  static final roles =
      obx.QueryBacklinkToMany<RoleModel, UserModel>(RoleModel_.user);
}
