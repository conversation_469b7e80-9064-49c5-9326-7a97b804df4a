name: rasiin_tasks_app
description: "Task Management application"
publish_to: "none" # Remove this line if you wish to publish to pub.dev

version: 1.0.1+2

environment:
  sdk: ">=3.4.4 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  # localization
  intl: ^0.20.2

  # UI & Icons
  cupertino_icons: ^1.0.6
  font_awesome_flutter: ^10.7.0
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  flutter_screenutil: ^5.9.3
  dropdown_search: ^6.0.1

  # Logging
  logging: ^1.2.0

  # Firebase Integration
  firebase_core: ^3.15.1
  firebase_messaging: ^15.2.9

  # HTTP Client and API Calls
  dio: ^5.6.0
  dio_cache_interceptor: ^4.0.3

  # Local Storage Solutions
  flutter_secure_storage: ^9.2.2

  # Notifications
  flutter_local_notifications: ^19.3.0

  # Dependency Injection
  get_it: ^8.0.3

  # UI Effects and Components
  shimmer: ^3.0.0
  another_flushbar: ^1.12.30

  # File and Image Pickers
  image_picker: ^1.1.2

  # State Management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.7

  # Utility Libraries
  fpdart: ^1.1.0
  html: ^0.15.4
  app_settings: ^6.1.1
  lottie: ^3.1.3
  modal_bottom_sheet: ^3.0.0

  # Popups and Overlays
  popover: ^0.3.0+1

  # Cached Images
  cached_network_image: ^3.4.1


  # Connection Checker
  internet_connection_checker_plus: ^2.7.1

  # enviroment variable usage
  flutter_dotenv: ^5.2.1

  # Logger
  logger: ^2.5.0
  fl_chart: ^1.0.0
  stream_transform: ^2.1.1
  syncfusion_flutter_pdfviewer: ^30.1.39
  file_picker: ^10.2.0
  permission_handler: ^12.0.1
  flutter_speed_dial: ^7.0.0
  share_plus: ^11.0.0
  path_provider: ^2.1.5
  objectbox_flutter_libs: ^4.1.0
  objectbox: ^4.1.0
  printing: ^5.14.2
  video_player: ^2.9.3
  



dev_dependencies:
  flutter_test:
    sdk: flutter

  # App Icon Generator
  flutter_launcher_icons: ^0.14.4

  # Splash screen generation
  flutter_native_splash: ^2.2.18

  # Analyzer
  analyzer: ^7.5.6

  # Code Generation Tools
  build_runner: ^2.1.7
  objectbox_generator: ^4.1.0

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/icon/icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false

flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icon/
    - assets/animations/
    - .env.production
    - .env.development
    - .env.test
    - shorebird.yaml
    # - .env.development
    # - .env.test

flutter_native_splash:
  color: "#FFFFFF" # Set background color to white
  image: assets/icon/rasiin_splash.png # Splash logo pth
  android: true
  android_12:
    image: assets/icon/rasiin_splash.png
    icon_background_color: "#FFFFFF"
  ios: true
  web: false
  fullscreen: true

  # Center the image with padding and scale it properly
  android_gravity: center
  ios_content_mode: scaleAspectFit # Ensure image scales properly

